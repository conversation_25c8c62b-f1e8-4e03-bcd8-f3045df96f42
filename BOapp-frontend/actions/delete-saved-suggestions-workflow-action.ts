// actions/delete-saved-suggestions-workflow-action.ts
"use server"

import { auth } from "@clerk/nextjs/server"
import { getOptimizationByOptimizerIdAction } from "./db/optimizations-actions"
import { deleteSavedSuggestionsAction } from "./db/delete-saved-suggestions-action"

export type ActionState<T = undefined> = {
  isSuccess: boolean
  message: string
  data?: T
}

/**
 * Deletes all saved suggestions for an optimization
 */
export async function deleteSavedSuggestionsWorkflowAction(
  optimizationId: string
): Promise<ActionState<{ count: number }>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to delete saved suggestions"
    }
  }

  try {
    console.log(`Deleting saved suggestions for optimization ${optimizationId}`)

    // Get the optimization from our database
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)

    if (!optResult.isSuccess || !optResult.data) {
      console.error(`Database Error: ${optResult.message}`)
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      }
    }

    // Delete all saved suggestions for this optimization
    const result = await deleteSavedSuggestionsAction(optResult.data.id)
    
    if (!result.isSuccess) {
      console.error(`Failed to delete saved suggestions: ${result.message}`)
      return {
        isSuccess: false,
        message: `Failed to delete saved suggestions: ${result.message}`
      }
    }

    console.log(`Successfully deleted ${result.data?.count || 0} saved suggestions`)
    
    return {
      isSuccess: true,
      message: `Successfully deleted ${result.data?.count || 0} saved suggestions`,
      data: { count: result.data?.count || 0 }
    }
  } catch (error) {
    console.error("Error deleting saved suggestions:", error)
    return {
      isSuccess: false,
      message: "Failed to delete saved suggestions"
    }
  }
}
