/*
Contains server actions related to profiles in the DB.
*/

"use server"

import { db } from "@/db/db"
import {
  InsertProfile,
  profilesTable,
  SelectProfile
} from "@/db/schema/profiles-schema"
import { ActionState } from "@/types"
import { eq } from "drizzle-orm"

export async function createProfileAction(
  data: InsertProfile
): Promise<ActionState<SelectProfile>> {
  try {
    const [newProfile] = await db.insert(profilesTable).values(data).returning()
    return {
      isSuccess: true,
      message: "Profile created successfully",
      data: newProfile
    }
  } catch (error) {
    console.error("Error creating profile:", error)
    return {
      isSuccess: false,
      message: `Failed to create profile: ${error instanceof Error ? error.message : 'Database connection error'}`
    }
  }
}

export async function getProfileByUserIdAction(
  userId: string
): Promise<ActionState<SelectProfile>> {
  try {
    const profile = await db.query.profiles.findFirst({
      where: eq(profilesTable.userId, userId)
    })
    if (!profile) {
      // Use a specific message for profile not found (used to determine if we need to create one)
      return { isSuccess: false, message: "Profile not found" }
    }

    return {
      isSuccess: true,
      message: "Profile retrieved successfully",
      data: profile
    }
  } catch (error) {
    console.error("Error getting profile by user id", error)
    return {
      isSuccess: false,
      message: `Database error: ${error instanceof Error ? error.message : 'Connection error'}`
    }
  }
}

export async function updateProfileAction(
  userId: string,
  data: Partial<InsertProfile>
): Promise<ActionState<SelectProfile>> {
  try {
    console.log(`Updating profile for user ${userId} with data:`, data);

    // Check if the profile exists first
    const existingProfile = await db.query.profiles.findFirst({
      where: eq(profilesTable.userId, userId)
    });

    if (!existingProfile) {
      console.log(`Profile not found for user ${userId}, creating new profile`);
      // If profile doesn't exist, create it
      return createProfileAction({
        userId,
        ...data,
        membership: data.membership || "free"
      });
    }

    // Update the existing profile
    const [updatedProfile] = await db
      .update(profilesTable)
      .set({
        ...data,
        updatedAt: new Date()
      })
      .where(eq(profilesTable.userId, userId))
      .returning();

    if (!updatedProfile) {
      console.error(`Failed to update profile for user ${userId}`);
      return { isSuccess: false, message: "Profile not found to update" };
    }

    console.log(`Successfully updated profile for user ${userId}:`, updatedProfile);
    return {
      isSuccess: true,
      message: "Profile updated successfully",
      data: updatedProfile
    };
  } catch (error) {
    console.error(`Error updating profile for user ${userId}:`, error);
    return {
      isSuccess: false,
      message: `Failed to update profile: ${error instanceof Error ? error.message : 'Database connection error'}`
    };
  }
}

export async function updateProfileByStripeCustomerIdAction(
  stripeCustomerId: string,
  data: Partial<InsertProfile>
): Promise<ActionState<SelectProfile>> {
  try {
    console.log(`Updating profile for Stripe customer ${stripeCustomerId} with data:`, data);

    // Check if the profile exists first
    const existingProfile = await db.query.profiles.findFirst({
      where: eq(profilesTable.stripeCustomerId, stripeCustomerId)
    });

    if (!existingProfile) {
      console.error(`No profile found with Stripe customer ID: ${stripeCustomerId}`);

      // Try to find the profile by subscription ID as a fallback
      if (data.stripeSubscriptionId) {
        console.log(`Trying to find profile by subscription ID: ${data.stripeSubscriptionId}`);
        const profileBySubscription = await db.query.profiles.findFirst({
          where: eq(profilesTable.stripeSubscriptionId, data.stripeSubscriptionId)
        });

        if (profileBySubscription) {
          console.log(`Found profile by subscription ID: ${profileBySubscription.userId}`);
          // Update the profile with the customer ID
          return updateProfileAction(profileBySubscription.userId, {
            ...data,
            stripeCustomerId
          });
        }
      }

      return {
        isSuccess: false,
        message: "Profile not found by Stripe customer ID and no fallback available"
      };
    }

    // Update the existing profile
    const [updatedProfile] = await db
      .update(profilesTable)
      .set({
        ...data,
        updatedAt: new Date()
      })
      .where(eq(profilesTable.stripeCustomerId, stripeCustomerId))
      .returning();

    if (!updatedProfile) {
      console.error(`Failed to update profile for Stripe customer ${stripeCustomerId}`);
      return {
        isSuccess: false,
        message: "Profile not found by Stripe customer ID"
      };
    }

    console.log(`Successfully updated profile for Stripe customer ${stripeCustomerId}:`, updatedProfile);
    return {
      isSuccess: true,
      message: "Profile updated by Stripe customer ID successfully",
      data: updatedProfile
    };
  } catch (error) {
    console.error(`Error updating profile for Stripe customer ${stripeCustomerId}:`, error);
    return {
      isSuccess: false,
      message: `Failed to update profile by Stripe customer ID: ${error instanceof Error ? error.message : 'Database connection error'}`
    };
  }
}

export async function deleteProfileAction(
  userId: string
): Promise<ActionState<void>> {
  try {
    await db.delete(profilesTable).where(eq(profilesTable.userId, userId))
    return {
      isSuccess: true,
      message: "Profile deleted successfully",
      data: undefined
    }
  } catch (error) {
    console.error("Error deleting profile:", error)
    return {
      isSuccess: false,
      message: `Failed to delete profile: ${error instanceof Error ? error.message : 'Database connection error'}`
    }
  }
}
