// actions/db/optimization-status-history-actions.ts
"use server"

import { db } from "@/db/db"
import {
  optimizationStatusHistoryTable,
  InsertOptimizationStatusHistory,
  SelectOptimizationStatusHistory
} from "@/db/schema/optimization-status-history-schema"
import { ActionState } from "@/types"
import { eq, desc } from "drizzle-orm"
import { auth } from "@clerk/nextjs/server"

/**
 * Creates a new status history entry
 */
export async function createStatusHistoryEntryAction(
  entry: InsertOptimizationStatusHistory
): Promise<ActionState<SelectOptimizationStatusHistory>> {
  try {
    // Validate required fields
    if (!entry.optimizationId) {
      throw new Error("Optimization ID is required")
    }

    if (!entry.previousStatus) {
      throw new Error("Previous status is required")
    }

    if (!entry.newStatus) {
      throw new Error("New status is required")
    }

    if (!entry.createdBy) {
      throw new Error("Creator ID is required")
    }

    // Insert into database
    const [newEntry] = await db.insert(optimizationStatusHistoryTable)
      .values(entry)
      .returning()

    return {
      isSuccess: true,
      message: "Status history entry created successfully",
      data: newEntry
    }
  } catch (error) {
    console.error("Error creating status history entry:", error)
    return {
      isSuccess: false,
      message: `Failed to create status history entry: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Gets status history for an optimization
 */
export async function getStatusHistoryAction(
  optimizationId: string
): Promise<ActionState<SelectOptimizationStatusHistory[]>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to view status history"
    }
  }

  try {
    const history = await db.select().from(optimizationStatusHistoryTable)
      .where(eq(optimizationStatusHistoryTable.optimizationId, optimizationId))
      .orderBy(desc(optimizationStatusHistoryTable.createdAt))

    return {
      isSuccess: true,
      message: "Status history retrieved successfully",
      data: history
    }
  } catch (error) {
    console.error("Error getting status history:", error)
    return {
      isSuccess: false,
      message: `Failed to get status history: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}
