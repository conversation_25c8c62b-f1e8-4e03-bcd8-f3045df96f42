// actions/db/check-saved-samples-action.ts
"use server"

import { db } from "@/db/db"
import { samplesTable } from "@/db/schema"
import { eq, and } from "drizzle-orm"
import { auth } from "@clerk/nextjs/server"
import { getOptimizationByOptimizerIdAction } from "./optimizations-actions"

export type ActionState<T = undefined> = {
  isSuccess: boolean
  message: string
  data?: T
}

/**
 * Check if there are saved samples for an optimization
 */
export async function checkSavedSamplesAction(
  optimizerId: string
): Promise<ActionState<{ hasSavedSamples: boolean; count: number }>> {
  try {
    // Verify user has access to the optimization
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to check for saved samples"
      }
    }

    // Get the optimization from our database
    const optResult = await getOptimizationByOptimizerIdAction(optimizerId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Count saved samples for this optimization
    const savedSamples = await db
      .select({ id: samplesTable.id })
      .from(samplesTable)
      .where(
        and(
          eq(samplesTable.optimizationId, optResult.data.id),
          eq(samplesTable.status, "pending")
        )
      )

    const count = savedSamples.length

    return {
      isSuccess: true,
      message: `Found ${count} saved samples`,
      data: {
        hasSavedSamples: count > 0,
        count
      }
    }
  } catch (error) {
    console.error("Error checking for saved samples:", error)
    return {
      isSuccess: false,
      message: `Failed to check for saved samples: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}
