// actions/optimization-workflow-actions.ts
"use server"

import { ActionState } from "@/types";
import { auth } from "@clerk/nextjs/server";
import {
  createOptimization,
  getSuggestion,
  addMeasurement as addApiMeasurement,
  getBestPoint,
  loadOptimization,
  checkOptimizationExists
} from "./optimization-actions";
import {
  createOptimizationDBAction,
  getOptimizationByOptimizerIdAction,
  createMeasurementAction
} from "./db/optimizations-actions";
import {
  InsertOptimization,
  SelectOptimization,
  SelectMeasurement
} from "@/db/schema/optimizations-schema";

/**
 * Creates a new optimization and stores its metadata in the database
 */
export async function createOptimizationWorkflowAction(
  name: string,
  description: string,
  config: {
    parameters: any[];
    target_config: any;
    recommender_config?: any;
    constraints?: any[];
  }
): Promise<ActionState<SelectOptimization>> {
  // Get the current user session from <PERSON>
  const { userId, getToken } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to create an optimization"
    };
  }

  // Get the authentication token (not used directly but might be needed in the future)
  await getToken();

  try {
    console.log("Starting optimization workflow with name:", name);

    // The optimizer ID will be generated in the createOptimization function
    // We'll use a similar format for the database entry

    // Create the optimization in the API
    console.log("Calling API to create optimization");
    // Log the config to check for any issues
    console.log("Config:", JSON.stringify(config, null, 2));

    // Additional logging for categorical parameters
    const categoricalParams = config.parameters.filter(p => p.type === "CategoricalParameter");
    if (categoricalParams.length > 0) {
      console.log("Categorical parameters found:", categoricalParams.length);
      categoricalParams.forEach((param, index) => {
        console.log(`Categorical parameter ${index + 1}: ${param.name}`);
        console.log(`  Values: ${JSON.stringify(param.values)}`);
        console.log(`  Encoding: ${param.encoding || 'OHE'}`);
      });
    }

    // Ensure recommender_config has the required fields
    if (config.recommender_config) {
      // Ensure n_restarts is an integer
      if (config.recommender_config.n_restarts === undefined ||
          config.recommender_config.n_restarts === null) {
        config.recommender_config.n_restarts = 10;
      }

      // Ensure n_raw_samples is an integer
      if (config.recommender_config.n_raw_samples === undefined ||
          config.recommender_config.n_raw_samples === null) {
        config.recommender_config.n_raw_samples = 64;
      }
    }

    // Special handling for categorical parameters
    if (Array.isArray(config.parameters)) {
      config.parameters = config.parameters.map(param => {
        if (param.type === "CategoricalParameter") {
          // Ensure values is an array and not empty
          if (!Array.isArray(param.values) || param.values.length === 0) {
            console.error(`Categorical parameter ${param.name} has invalid or missing values:`, param.values);
            throw new Error(`Categorical parameter ${param.name} must have at least one value`);
          }

          // Log the values for debugging
          console.log(`Categorical parameter ${param.name} values:`, JSON.stringify(param.values));

          // Ensure encoding is set
          if (!param.encoding) {
            param.encoding = "OHE";
          }
        }
        return param;
      });
    }

    // We'll use the name directly and let the createOptimization function handle sanitization
    const apiResult = await createOptimization(name, config);
    console.log("API result:", apiResult);

    if (!apiResult.isSuccess) {
      console.error("API error:", apiResult.message);
      return {
        isSuccess: false,
        message: `API Error: ${apiResult.message}`
      };
    }

    // Create an entry in our database
    console.log("Creating entry in database");

    // Extract the optimizer ID from the API response or generate one based on the name
    // The API might not return optimizer_id directly in the response
    console.log("API result data:", apiResult.data);
    const optimizerId = (apiResult.data as unknown as { optimizer_id: string }).optimizer_id || name.toLowerCase().replace(/\s+/g, '_') + '_' + Date.now();


    // Handle target name and mode differently for single vs multi-target optimizations
    let targetName: string;
    let targetMode: string;

    // Check if this is a multi-target optimization by examining the target_config
    if (Array.isArray(config.target_config) && config.target_config.length > 1) {
      // For multi-target optimizations, use the first target's name and a combined description
      targetName = config.target_config[0].name || "Multi-target";
      targetMode = "MULTI";
      console.log("Using multi-target configuration with primary target:", targetName);
    } else {
      // For single-target optimizations
      const targetConfig = Array.isArray(config.target_config)
        ? config.target_config[0]
        : config.target_config;
      targetName = targetConfig.name || "Target";
      targetMode = targetConfig.mode || "MAX";
      console.log("Using single-target configuration:", targetName, targetMode);
    }

    const dbOptimization: InsertOptimization = {
      userId,
      name,
      description,
      optimizerId, // Use the optimizer ID from the API
      config,
      targetName,
      targetMode,
      status: "active" // Start as active
    };

    console.log("Database entry to create:", dbOptimization);
    const dbResult = await createOptimizationDBAction(dbOptimization);
    console.log("Database result:", dbResult);

    if (!dbResult.isSuccess) {
      console.error("Database error:", dbResult.message);
      return {
        isSuccess: false,
        message: `Database Error: ${dbResult.message}`
      };
    }

    console.log("Optimization workflow completed successfully");
    return {
      isSuccess: true,
      message: "Optimization created successfully",
      data: dbResult.data
    };

  } catch (error) {
    console.error("Error in optimization workflow:", error);
    return {
      isSuccess: false,
      message: `Failed to create optimization workflow: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Gets the next suggestion and stores it in the database
 */
export async function getSuggestionWorkflowAction(
  optimizationId: string,
  batchSize: number = 1
): Promise<ActionState<{ suggestions: any[], batchId: string | null }>> {
  console.log(`[getSuggestionWorkflowAction] Starting with optimizationId=${optimizationId}, batchSize=${batchSize}`);

  const { userId } = await auth();

  if (!userId) {
    console.log(`[getSuggestionWorkflowAction] No user ID found, aborting`);
    return {
      isSuccess: false,
      message: "You must be signed in to get suggestions"
    };
  }

  console.log(`[getSuggestionWorkflowAction] User ID: ${userId}`);

  try {
    // Get the optimization from our database
    console.log(`[getSuggestionWorkflowAction] Fetching optimization from database with ID: ${optimizationId}`);
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId);

    if (!optResult.isSuccess) {
      console.error(`[getSuggestionWorkflowAction] Database error: ${optResult.message}`);
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      };
    }

    console.log(`[getSuggestionWorkflowAction] Successfully retrieved optimization from database`);

    // Validate batch size
    const validatedBatchSize = Math.min(Math.max(1, batchSize), 100);
    if (validatedBatchSize !== batchSize) {
      console.warn(`[getSuggestionWorkflowAction] Adjusted batch size from ${batchSize} to ${validatedBatchSize} (valid range: 1-100)`);
    }

    // Always generate a batch ID, regardless of batch size
    // This ensures that all measurements from the same batch share the same batch ID
    // We use a consistent format: batch_<optimizationId>_<timestamp>
    const batchId = `batch_${optimizationId}_${Date.now()}`;
    console.log(`[getSuggestionWorkflowAction] Generated batch ID: ${batchId} for batch size ${validatedBatchSize}`);

    // Get suggestion from the API
    console.log(`[getSuggestionWorkflowAction] Calling API to get ${validatedBatchSize} suggestions`);
    const apiResult = await getSuggestion(optimizationId, validatedBatchSize);

    if (!apiResult.isSuccess || !apiResult.data) {
      console.error(`[getSuggestionWorkflowAction] API error: ${apiResult.message}`);
      return {
        isSuccess: false,
        message: `API Error: ${apiResult.message}`
      };
    }

    console.log(`[getSuggestionWorkflowAction] API returned ${apiResult.data.suggestions.length} suggestions successfully`);

    // Log the first suggestion for debugging
    if (apiResult.data.suggestions.length > 0) {
      console.log(`[getSuggestionWorkflowAction] First suggestion sample:`,
        JSON.stringify(apiResult.data.suggestions[0], null, 2).substring(0, 200) + '...');
    }

    return {
      isSuccess: true,
      message: "Got suggestions successfully",
      data: {
        suggestions: apiResult.data.suggestions,
        batchId: batchId
      }
    };

  } catch (error) {
    console.error(`[getSuggestionWorkflowAction] Error in workflow:`, error);
    return {
      isSuccess: false,
      message: `Failed to get suggestions: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Adds a measurement to the optimization and stores it in the database
 */
export async function addMeasurementWorkflowAction(
  optimizationId: string,
  parameters: Record<string, any>,
  targetValue: number | Record<string, number>,
  isRecommended: boolean = true,
  batchId: string | null = null
): Promise<ActionState<SelectMeasurement>> {
  const { userId } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to add measurements"
    };
  }

  try {
    // Get the optimization from our database
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId);

    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      };
    }

    // Add measurement to the API
    // For multi-target optimizations, we need to handle this differently
    let apiResult;

    // Handle both multi-target and single-target measurements the same way
    // The addApiMeasurement function will handle the different formats
    console.log("Adding measurement for optimization");
    if (optResult.data.targetMode === "MULTI") {
      console.log("Multi-target optimization detected");
    }

    // Cast targetValue to any to handle both single and multi-target cases
    // The addApiMeasurement function in the API client handles both formats
    apiResult = await addApiMeasurement(
      optimizationId,
      parameters,
      targetValue as any
    );

    if (!apiResult.isSuccess) {
      return {
        isSuccess: false,
        message: `API Error: ${apiResult.message}`
      };
    }

    // Add measurement to our database
    let dbTargetValue: string;
    let dbTargetValues: Record<string, number> | null = null;

    if (typeof targetValue === "object") {
      // For multi-target, use the first target value as the main target value
      // and store all target values in the targetValues field
      const firstTargetKey = Object.keys(targetValue)[0];
      dbTargetValue = targetValue[firstTargetKey].toString();
      dbTargetValues = targetValue;
      console.log("Multi-target values:", dbTargetValues);
    } else {
      dbTargetValue = targetValue.toString();
      // For single target, we can also store it in targetValues for consistency
      if (optResult.data.targetName) {
        dbTargetValues = { [optResult.data.targetName]: targetValue as number };
      }
    }

    const measurement = {
      optimizationId: optResult.data.id,
      parameters,
      targetValue: dbTargetValue,
      targetValues: dbTargetValues,
      isRecommended,
      batchId
    };

    // Log batch information with more details
    if (batchId) {
      console.log(`Adding measurement with batch ID: ${batchId} (isRecommended: ${isRecommended})`);
    } else {
      console.log(`Adding measurement without batch ID (isRecommended: ${isRecommended})`);
    }

    console.log("Saving measurement to database:", measurement);

    const dbResult = await createMeasurementAction(measurement);

    if (!dbResult.isSuccess) {
      return {
        isSuccess: false,
        message: `Database Error: ${dbResult.message}`
      };
    }

    // Update best value in the optimization if it's better
    try {
      const bestResult = await getBestPoint(optimizationId);

      if (bestResult.isSuccess && bestResult.data && bestResult.data.best_value !== undefined) {
        // Update the optimization with the current best values and ensure it's marked as active
        // Use the status action to track history
        await import("./optimization-status-actions").then(module =>
          module.updateOptimizationStatusAction(
            optResult.data.id,
            "active",
            "measurement_added"
          )
        );
      }
    } catch (error) {
      console.error("Error updating best value:", error);
      // Continue even if this part fails
    }

    return {
      isSuccess: true,
      message: "Measurement added successfully",
      data: dbResult.data
    };

  } catch (error) {
    console.error("Error in workflow:", error);
    return {
      isSuccess: false,
      message: "Failed to add measurement"
    };
  }
}

/**
 * Loads an existing optimization
 */
export async function loadOptimizationWorkflowAction(
  optimizationId: string
): Promise<ActionState<any>> {
  const { userId } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to load an optimization"
    };
  }

  try {
    // Get the optimization from our database to verify access
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId);

    if (!optResult.isSuccess) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      };
    }

    // Load the optimization from the API
    const apiResult = await loadOptimization(optimizationId);

    if (!apiResult.isSuccess) {
      return {
        isSuccess: false,
        message: `Failed to load optimization: ${apiResult.message}`
      };
    }

    return {
      isSuccess: true,
      message: "Optimization loaded successfully",
      data: apiResult.data
    };

  } catch (error) {
    console.error("Error in workflow:", error);
    return {
      isSuccess: false,
      message: `Failed to load optimization: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Generate samples using Latin Hypercube Sampling or random sampling
 *
 * @deprecated Use the generateSamplesWorkflowAction from sample-workflow-actions.ts instead
 */
export async function generateSamplesWorkflowAction(
  optimizationId: string,
  numSamples: number = 10,
  samplingStrategy: string = "LHS",
  seed?: number
): Promise<ActionState<{ samples: any[] }>> {
  // Forward to the new implementation
  // Cast samplingStrategy to the correct type (SamplingMethod)
  const result = await import("./sample-workflow-actions").then(module =>
    module.generateSamplesWorkflowAction(optimizationId, numSamples, samplingStrategy as any, seed)
  );

  // Convert the result format to maintain backward compatibility
  if (result.isSuccess && result.data) {
    return {
      isSuccess: true,
      message: result.message,
      data: {
        samples: result.data.samples
      }
    };
  }

  return {
    isSuccess: false,
    message: result.message
  };
}

export async function getBestPointWorkflowAction(
  optimizationId: string
): Promise<ActionState<{
  best_parameters?: Record<string, any>;
  best_value?: number;
  best_values?: Record<string, number>;
  composite_score?: number;
  normalized_values?: Record<string, number>;
  target_weights?: Record<string, number>;
}>> {
  const { userId } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to get the best point"
    };
  }

  try {
    // Get the optimization from our database to verify access
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId);

    if (!optResult.isSuccess) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      };
    }

    // First, check if the optimization exists
    console.log(`Checking if optimization exists with ID: ${optimizationId}`);
    try {
      const existsResult = await checkOptimizationExists(optimizationId);
      if (!existsResult.isSuccess) {
        console.error(`Error checking if optimization exists: ${existsResult.message}`);
        return {
          isSuccess: false,
          message: `Error checking if optimization exists: ${existsResult.message}`
        };
      }

      if (!existsResult.data) {
        console.error(`Optimization does not exist: ${optimizationId}`);
        return {
          isSuccess: false,
          message: `Optimization does not exist: ${optimizationId}`
        };
      }

      console.log(`Optimization exists: ${optimizationId}`);
    } catch (error) {
      console.error(`Error checking if optimization exists: ${error}`);
      return {
        isSuccess: false,
        message: `Error checking if optimization exists: ${error instanceof Error ? error.message : String(error)}`
      };
    }

    // Note: We're skipping the load step because the /load endpoint doesn't exist in the backend API
    // For newly created optimizations, this is fine because they're already loaded in memory
    // For existing optimizations, we rely on the backend to load them when needed

    // Get the best point from the API
    console.log(`Getting best point for optimizer ID: ${optimizationId}`);
    try {
      const apiResult = await getBestPoint(optimizationId);

      if (!apiResult.isSuccess) {
        console.error(`API Error getting best point: ${apiResult.message}`);
        return {
          isSuccess: false,
          message: `API Error: ${apiResult.message}`
        };
      }

      // Check if we have data or just a message (no measurements yet)
      if (!apiResult.data || !apiResult.data.best_parameters) {
        console.log(`No best point available yet: ${apiResult.message || 'No measurements yet'}`);
        return {
          isSuccess: true,
          message: apiResult.message || "No best point available yet",
          data: {
            best_parameters: undefined,
            best_value: undefined,
            best_values: undefined,
            composite_score: undefined,
            normalized_values: undefined,
            target_weights: undefined
          }
        };
      }

      // Return the best point with all available data
      // Use optional chaining to safely access properties that might not exist
      return {
        isSuccess: true,
        message: "Got best point successfully",
        data: {
          best_parameters: apiResult.data.best_parameters,
          best_value: apiResult.data.best_value,
          // For multi-target optimizations, these fields might be present
          // Use undefined as fallback if they don't exist
          best_values: (apiResult.data as any).best_values,
          composite_score: (apiResult.data as any).composite_score,
          normalized_values: (apiResult.data as any).normalized_values,
          target_weights: (apiResult.data as any).target_weights
        }
      };
    } catch (error) {
      console.error(`Error getting best point from API: ${error}`);

      // Handle the specific Python error
      if (error instanceof Error && error.message.includes("'tuple' object has no attribute 'get'")) {
        console.log("Handling tuple error gracefully - this is likely a backend API issue with a new optimization");
        // Return a successful response with empty data
        return {
          isSuccess: true,
          message: "No best point available yet",
          data: {
            best_parameters: undefined,
            best_value: undefined,
            best_values: undefined,
            composite_score: undefined,
            normalized_values: undefined,
            target_weights: undefined
          }
        };
      }

      return {
        isSuccess: false,
        message: `Error getting best point: ${error instanceof Error ? error.message : String(error)}`
      };
    }

  } catch (error) {
    console.error("Error in workflow:", error);
    return {
      isSuccess: false,
      message: "Failed to get best point"
    };
  }
}