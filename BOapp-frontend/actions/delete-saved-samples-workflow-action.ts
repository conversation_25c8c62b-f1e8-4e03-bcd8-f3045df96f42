// actions/delete-saved-samples-workflow-action.ts
"use server"

import { auth } from "@clerk/nextjs/server"
import { getOptimizationByOptimizerIdAction } from "./db/optimizations-actions"
import { deleteSamplesByStatusAction } from "./db/samples-actions"
import { ActionState } from "./suggestion-workflow-actions"

/**
 * Deletes all saved samples for an optimization
 */
export async function deleteSavedSamplesWorkflowAction(
  optimizationId: string
): Promise<ActionState<{ count: number }>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to delete saved samples"
    }
  }

  try {
    // Get the optimization from our database
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)

    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      }
    }

    // Delete saved samples from the database
    const deleteResult = await deleteSamplesByStatusAction(optResult.data.id, "pending")

    if (!deleteResult.isSuccess) {
      return {
        isSuccess: false,
        message: `Failed to delete saved samples: ${deleteResult.message}`
      }
    }

    return {
      isSuccess: true,
      message: "Saved samples deleted successfully",
      data: {
        count: deleteResult.data?.count || 0
      }
    }
  } catch (error) {
    console.error("Error in workflow:", error)
    return {
      isSuccess: false,
      message: "Failed to delete saved samples"
    }
  }
}
