// actions/optimization-actions.ts
"use server"

import { ActionState } from "@/types";
import { auth } from "@clerk/nextjs/server";
import {
  checkAPIHealthAction,
  createOptimizationAction,
  getSuggestionAction,
  addMeasurementAction,
  addMultipleMeasurementsAction,
  getBestPointAction,
  loadOptimizationAction,
  checkOptimizationExistsAction,
  generateSamplesAction
} from "@/lib/api/baybe-client";

/**
 * Checks the health status of the BayBE API
 */
export async function checkAPIHealth(): Promise<
  ActionState<{ status: string; using_gpu: boolean; gpu_info?: any }>
> {
  // Get the current user session from Clerk
  const { getToken } = await auth();
  const token = await getToken();

  return checkAPIHealthAction(token ?? undefined);
}

/**
 * Creates a new optimization with the given configuration
 */
export async function createOptimization(
  optimizerName: string,
  config: {
    parameters: any[];
    target_config: any;
    recommender_config?: any;
    constraints?: any[];
  }
): Promise<ActionState<{
  optimizer_id: string; status: string; message: string; constraint_count?: number 
}>> {
  // Get the current user session from <PERSON>
  const { userId, getToken } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to create an optimization"
    };
  }

  // Get the authentication token
  const token = await getToken();

  // Create a unique optimizer ID that includes the user ID to prevent collisions
  // Replace spaces and special characters to avoid URL encoding issues
  const sanitizedName = optimizerName.replace(/[^a-zA-Z0-9]/g, '_');
  const optimizerId = `${userId}_${sanitizedName}_${Date.now()}`;

  // Special handling for categorical parameters
  if (Array.isArray(config.parameters)) {
    config.parameters = config.parameters.map(param => {
      if (param.type === "CategoricalParameter") {
        // Ensure values is an array and not empty
        if (!Array.isArray(param.values) || param.values.length === 0) {
          console.error(`Categorical parameter ${param.name} has invalid or missing values:`, param.values);
          throw new Error(`Categorical parameter ${param.name} must have at least one value`);
        }

        // Log the values for debugging
        console.log(`Categorical parameter ${param.name} values in optimization-actions:`, JSON.stringify(param.values));
      }
      return param;
    });
  }

  return createOptimizationAction(optimizerId, config, token ?? undefined);
}

/**
 * Gets the next suggestion for experimentation
 */
export async function getSuggestion(
  optimizerId: string,
  batchSize: number = 1
): Promise<ActionState<{ status: string; suggestions: any[] }>> {
  console.log(`[getSuggestion] Starting with optimizerId=${optimizerId}, batchSize=${batchSize}`);

  // Get the current user session from Clerk
  const { userId, getToken } = await auth();

  if (!userId) {
    console.log(`[getSuggestion] No user ID found, aborting`);
    return {
      isSuccess: false,
      message: "You must be signed in to get suggestions"
    };
  }

  console.log(`[getSuggestion] User ID: ${userId}`);

  // Validate batch size
  const validatedBatchSize = Math.min(Math.max(1, batchSize), 100);
  if (validatedBatchSize !== batchSize) {
    console.warn(`[getSuggestion] Adjusted batch size from ${batchSize} to ${validatedBatchSize} (valid range: 1-100)`);
  }

  // Get the authentication token
  console.log(`[getSuggestion] Getting authentication token`);
  const token = await getToken();
  console.log(`[getSuggestion] Token received (length: ${token?.length || 0})`);

  console.log(`[getSuggestion] Calling getSuggestionAction with batch size ${validatedBatchSize}`);
  return getSuggestionAction(optimizerId, validatedBatchSize, token ?? undefined);
}

/**
 * Adds a measurement to the optimization
 */
export async function addMeasurement(
  optimizerId: string,
  parameters: Record<string, any>,
  targetValue: number
): Promise<ActionState<{ status: string; message: string }>> {
  // Get the current user session from Clerk
  const { userId, getToken } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to add measurements"
    };
  }

  // Get the authentication token
  const token = await getToken();

  return addMeasurementAction(optimizerId, parameters, targetValue, token ?? undefined);
}

/**
 * Adds multiple measurements to the optimization
 */
export async function addMultipleMeasurements(
  optimizerId: string,
  measurements: { parameters: Record<string, any>; target_value: number }[]
): Promise<ActionState<{ status: string; message: string }>> {
  // Get the current user session from Clerk
  const { userId, getToken } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to add measurements"
    };
  }

  // Get the authentication token
  const token = await getToken();

  return addMultipleMeasurementsAction(optimizerId, measurements, token ?? undefined);
}

/**
 * Gets the current best point for the optimization
 */
export async function getBestPoint(
  optimizerId: string
): Promise<
  ActionState<{
    status: string;
    best_parameters?: Record<string, any>;
    best_value?: number;
    message?: string;
  }>
> {
  // Get the current user session from Clerk
  const { userId, getToken } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to get the best point"
    };
  }

  // Get the authentication token
  const token = await getToken();

  return getBestPointAction(optimizerId, token ?? undefined);
}

/**
 * Checks if an optimization exists
 */
export async function checkOptimizationExists(
  optimizerId: string
): Promise<ActionState<boolean>> {
  // Get the current user session from Clerk
  const { userId, getToken } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to check if an optimization exists"
    };
  }

  // Get the authentication token
  const token = await getToken();

  return checkOptimizationExistsAction(optimizerId, token ?? undefined);
}

/**
 * Loads an existing optimization
 */
export async function loadOptimization(
  optimizerId: string
): Promise<ActionState<{ status: string; message: string }>> {
  // Get the current user session from Clerk
  const { userId, getToken } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to load an optimization"
    };
  }

  // Get the authentication token
  const token = await getToken();

  return loadOptimizationAction(optimizerId, token ?? undefined);
}

/**
 * Generate samples using Latin Hypercube Sampling or random sampling
 */
export async function generateSamples(
  optimizerId: string,
  numSamples: number = 10,
  samplingStrategy: string = "LHS",
  seed?: number
): Promise<ActionState<{ status: string; samples: any[] }>> {
  // Get the current user session from Clerk
  const { userId, getToken } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to generate samples"
    };
  }

  // Get the authentication token
  const token = await getToken();

  return generateSamplesAction(optimizerId, numSamples, samplingStrategy, seed, token ?? undefined);
}