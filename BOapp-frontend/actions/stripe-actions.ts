/*
Contains server actions related to <PERSON><PERSON>.
*/

import {
  updateProfileAction,
  updateProfileByStripeCustomerIdAction
} from "@/actions/db/profiles-actions"
import { SelectProfile } from "@/db/schema"
import { stripe } from "@/lib/stripe"
import Strip<PERSON> from "stripe"

type MembershipStatus = SelectProfile["membership"]

const getMembershipStatus = (
  status: Stripe.Subscription.Status,
  membership: MembershipStatus
): MembershipStatus => {
  switch (status) {
    case "active":
    case "trialing":
      return membership
    case "canceled":
    case "incomplete":
    case "incomplete_expired":
    case "past_due":
    case "paused":
    case "unpaid":
      return "free"
    default:
      return "free"
  }
}

const getSubscription = async (subscriptionId: string) => {
  return stripe.subscriptions.retrieve(subscriptionId, {
    expand: ["default_payment_method"]
  })
}

export const updateStripeCustomer = async (
  userId: string,
  subscriptionId: string,
  customerId: string
) => {
  try {
    if (!userId || !subscriptionId || !customerId) {
      throw new Error("Missing required parameters for updateStripeCustomer")
    }

    const subscription = await getSubscription(subscriptionId)

    // Determine subscription type (monthly or yearly) based on the price
    const priceId = subscription.items.data[0].price.id

    // Log the price IDs for debugging
    console.log(`Actual price ID from Stripe: ${priceId}`);
    console.log(`Environment variables:`, {
      MONTHLY: process.env.STRIPE_PRICE_ID_MONTHLY,
      YEARLY: process.env.STRIPE_PRICE_ID_YEARLY
    });

    // Try to determine subscription type from price ID
    let subscriptionType = null;

    if (priceId === process.env.STRIPE_PRICE_ID_MONTHLY) {
      subscriptionType = "monthly";
    } else if (priceId === process.env.STRIPE_PRICE_ID_YEARLY) {
      subscriptionType = "yearly";
    } else {
      // Fallback: Try to determine from the price interval
      const interval = subscription.items.data[0].price.recurring?.interval;
      console.log(`Interval from Stripe: ${interval}`);

      if (interval === 'month') {
        subscriptionType = "monthly";
      } else if (interval === 'year') {
        subscriptionType = "yearly";
      }
    }

    console.log(`Updating user ${userId} with subscription type: ${subscriptionType}`)

    const result = await updateProfileAction(userId, {
      stripeCustomerId: customerId,
      stripeSubscriptionId: subscription.id,
      // Add subscription type to the profile
      subscriptionType: subscriptionType as "monthly" | "yearly" | null | undefined
    })

    if (!result.isSuccess) {
      throw new Error("Failed to update customer profile")
    }

    return result.data
  } catch (error) {
    console.error("Error in updateStripeCustomer:", error)
    throw error instanceof Error
      ? error
      : new Error("Failed to update Stripe customer")
  }
}

export const manageSubscriptionStatusChange = async (
  subscriptionId: string,
  customerId: string,
  productId: string
): Promise<MembershipStatus> => {
  try {
    console.log("Starting manageSubscriptionStatusChange with params:", {
      subscriptionId,
      customerId,
      productId
    });

    if (!subscriptionId || !customerId || !productId) {
      throw new Error(
        "Missing required parameters for manageSubscriptionStatusChange"
      )
    }

    // Validate that productId is a string
    if (typeof productId !== 'string') {
      console.error("Invalid productId type:", typeof productId);
      console.error("ProductId value:", productId);
      throw new Error(`ProductId must be a string, got ${typeof productId}`);
    }

    const subscription = await getSubscription(subscriptionId)
    console.log("Retrieved subscription:", {
      id: subscription.id,
      status: subscription.status,
      priceId: subscription.items.data[0].price.id
    });

    console.log("Retrieving product with ID:", productId);
    const product = await stripe.products.retrieve(productId)
    console.log("Retrieved product:", {
      id: product.id,
      name: product.name,
      metadata: product.metadata
    });

    const membership = product.metadata.membership as MembershipStatus

    if (!["free", "pro"].includes(membership)) {
      throw new Error(
        `Invalid membership type in product metadata: ${membership}`
      )
    }

    const membershipStatus = getMembershipStatus(
      subscription.status,
      membership
    )

    // Determine subscription type (monthly or yearly) based on the price
    const priceId = subscription.items.data[0].price.id

    // Log the price IDs for debugging
    console.log(`Actual price ID from Stripe: ${priceId}`);
    console.log(`Environment variables:`, {
      MONTHLY: process.env.STRIPE_PRICE_ID_MONTHLY,
      YEARLY: process.env.STRIPE_PRICE_ID_YEARLY
    });

    // Try to determine subscription type from price ID
    let subscriptionType = null;

    if (priceId === process.env.STRIPE_PRICE_ID_MONTHLY) {
      subscriptionType = "monthly";
    } else if (priceId === process.env.STRIPE_PRICE_ID_YEARLY) {
      subscriptionType = "yearly";
    } else {
      // Fallback: Try to determine from the price interval
      const interval = subscription.items.data[0].price.recurring?.interval;
      console.log(`Interval from Stripe: ${interval}`);

      if (interval === 'month') {
        subscriptionType = "monthly";
      } else if (interval === 'year') {
        subscriptionType = "yearly";
      }
    }

    console.log(`Updating subscription for customer ${customerId} with type: ${subscriptionType}`)

    const updateResult = await updateProfileByStripeCustomerIdAction(
      customerId,
      {
        stripeSubscriptionId: subscription.id,
        membership: membershipStatus,
        subscriptionType: subscriptionType as "monthly" | "yearly" | null | undefined
      }
    )

    if (!updateResult.isSuccess) {
      throw new Error("Failed to update subscription status")
    }

    return membershipStatus
  } catch (error) {
    console.error("Error in manageSubscriptionStatusChange:", error)
    throw error instanceof Error
      ? error
      : new Error("Failed to update subscription status")
  }
}
