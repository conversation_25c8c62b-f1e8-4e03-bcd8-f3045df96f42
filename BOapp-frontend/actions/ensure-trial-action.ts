"use server"

import { auth } from "@clerk/nextjs/server"
import { getProfileByUserIdAction } from "./db/profiles-actions"
import { initializeTrialAction } from "./trial-actions"

/**
 * Ensures that a user has a trial initialized if they don't already have one.
 * This function should be called when a user logs in or accesses protected routes.
 */
export async function ensureUserTrialAction() {
  try {
    const { userId } = await auth()

    if (!userId) {
      console.log("ensureUserTrialAction: User not authenticated")
      return { success: false, message: "User not authenticated" }
    }

    console.log(`ensureUserTrialAction: Checking trial status for user ${userId}`)

    // Check if user already has a profile with trial data
    let profileResult;
    try {
      profileResult = await getProfileByUserIdAction(userId)
    } catch (dbError) {
      console.error(`Database error when checking profile: ${dbError}`)
      // Return a temporary success to prevent errors during login
      // The trial will be checked again on subsequent requests
      return {
        success: true,
        message: "Temporary trial access granted due to database connection issues",
        hasExistingTrial: true
      }
    }

    // If user has a profile with trial data, do nothing
    if (profileResult?.isSuccess &&
        profileResult.data &&
        (profileResult.data.trialStartedAt || profileResult.data.stripeSubscriptionId)) {

      console.log(`ensureUserTrialAction: User ${userId} already has trial data or subscription`)
      console.log(`Trial data: ${profileResult.data.trialStartedAt ? new Date(profileResult.data.trialStartedAt).toISOString() : 'none'}`)
      console.log(`Trial end: ${profileResult.data.trialEndsAt ? new Date(profileResult.data.trialEndsAt).toISOString() : 'none'}`)
      console.log(`Subscription: ${profileResult.data.stripeSubscriptionId || 'none'}`)

      return {
        success: true,
        message: "User already has trial data or subscription",
        hasExistingTrial: true
      }
    }

    console.log(`ensureUserTrialAction: Initializing trial for user ${userId}`)

    // Initialize trial for the user
    let trialResult;
    try {
      trialResult = await initializeTrialAction(userId)

      if (!trialResult.isSuccess) {
        console.error("Failed to initialize trial:", trialResult.message)
        // If we can't initialize the trial, grant temporary access
        return {
          success: true,
          message: "Temporary trial access granted. Please try again later.",
          hasExistingTrial: true
        }
      }
    } catch (trialError) {
      console.error(`Error initializing trial: ${trialError}`)
      // If there's an error, grant temporary access
      return {
        success: true,
        message: "Temporary trial access granted due to initialization issues",
        hasExistingTrial: true
      }
    }

    console.log(`ensureUserTrialAction: Trial initialized successfully for user ${userId}`)

    return {
      success: true,
      message: "Trial initialized successfully",
      hasExistingTrial: false,
      trialEndsAt: trialResult.data?.trialEndsAt
    }
  } catch (error) {
    console.error("Error ensuring user trial:", error)
    console.error(`Stack trace: ${error instanceof Error ? error.stack : 'No stack trace'}`)
    return {
      success: false,
      message: `Error ensuring user trial: ${error instanceof Error ? error.message : "Unknown error"}`
    }
  }
}
