// actions/check-saved-samples-workflow-action.ts
"use server"

import { auth } from "@clerk/nextjs/server"
import { checkSavedSamplesAction } from "./db/check-saved-samples-action"

export type ActionState<T = undefined> = {
  isSuccess: boolean
  message: string
  data?: T
}

/**
 * Check if there are saved samples for an optimization
 */
export async function checkSavedSamplesWorkflowAction(
  optimizerId: string
): Promise<ActionState<{ hasSavedSamples: boolean; count: number }>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to check for saved samples"
    }
  }

  try {
    console.log(`Checking for saved samples for optimization ${optimizerId}`)

    // Check for saved samples in the database
    const result = await checkSavedSamplesAction(optimizerId)
    
    if (!result.isSuccess) {
      console.error(`Failed to check for saved samples: ${result.message}`)
      return {
        isSuccess: false,
        message: `Failed to check for saved samples: ${result.message}`
      }
    }

    console.log(`Found ${result.data?.count || 0} saved samples`)
    
    return {
      isSuccess: true,
      message: `Found ${result.data?.count || 0} saved samples`,
      data: {
        hasSavedSamples: result.data?.hasSavedSamples || false,
        count: result.data?.count || 0
      }
    }
  } catch (error) {
    console.error("Error checking for saved samples:", error)
    return {
      isSuccess: false,
      message: "Failed to check for saved samples"
    }
  }
}
