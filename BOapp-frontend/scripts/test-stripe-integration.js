#!/usr/bin/env node

/**
 * This script tests the Stripe integration by simulating a checkout session completed event.
 * It creates a test user profile and then simulates a webhook event to update the profile with Stripe data.
 * 
 * Usage:
 * 1. Make sure the database is running
 * 2. Run: node scripts/test-stripe-integration.js
 */

require('dotenv').config({ path: '.env.local' });
const { Client } = require('pg');
const Stripe = require('stripe');

// Initialize Stripe with the test secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Database connection
const dbClient = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.DATABASE_URL.includes('sslmode=require') 
    ? { rejectUnauthorized: false } 
    : false
});

// Test data
const TEST_USER_ID = 'test_user_' + Math.random().toString(36).substring(2, 10);
const TEST_CUSTOMER_ID = 'cus_test_' + Math.random().toString(36).substring(2, 10);
const TEST_SUBSCRIPTION_ID = 'sub_test_' + Math.random().toString(36).substring(2, 10);
const TEST_SUBSCRIPTION_TYPE = 'monthly';

async function createTestProfile() {
  console.log('Creating test profile...');
  
  try {
    // Create a test profile
    const result = await dbClient.query(
      `INSERT INTO profiles (user_id, membership, created_at, updated_at) 
       VALUES ($1, $2, NOW(), NOW()) 
       RETURNING *`,
      [TEST_USER_ID, 'free']
    );
    
    console.log('Test profile created:', result.rows[0]);
    return result.rows[0];
  } catch (error) {
    console.error('Error creating test profile:', error);
    throw error;
  }
}

async function updateProfileWithStripeData() {
  console.log('Updating profile with Stripe data...');
  
  try {
    // Update the profile with Stripe data
    const result = await dbClient.query(
      `UPDATE profiles 
       SET stripe_customer_id = $1, 
           stripe_subscription_id = $2, 
           subscription_type = $3,
           membership = $4,
           updated_at = NOW() 
       WHERE user_id = $5 
       RETURNING *`,
      [TEST_CUSTOMER_ID, TEST_SUBSCRIPTION_ID, TEST_SUBSCRIPTION_TYPE, 'pro', TEST_USER_ID]
    );
    
    if (result.rows.length === 0) {
      throw new Error(`Profile not found for user ID: ${TEST_USER_ID}`);
    }
    
    console.log('Profile updated with Stripe data:', result.rows[0]);
    return result.rows[0];
  } catch (error) {
    console.error('Error updating profile with Stripe data:', error);
    throw error;
  }
}

async function verifyProfileUpdate() {
  console.log('Verifying profile update...');
  
  try {
    // Verify the profile was updated correctly
    const result = await dbClient.query(
      'SELECT * FROM profiles WHERE user_id = $1',
      [TEST_USER_ID]
    );
    
    if (result.rows.length === 0) {
      throw new Error(`Profile not found for user ID: ${TEST_USER_ID}`);
    }
    
    const profile = result.rows[0];
    
    // Check if all Stripe data was saved correctly
    const isSuccess = 
      profile.stripe_customer_id === TEST_CUSTOMER_ID &&
      profile.stripe_subscription_id === TEST_SUBSCRIPTION_ID &&
      profile.subscription_type === TEST_SUBSCRIPTION_TYPE &&
      profile.membership === 'pro';
    
    if (isSuccess) {
      console.log('✅ Profile update verified successfully!');
      console.log('Profile data:', profile);
    } else {
      console.error('❌ Profile update verification failed!');
      console.error('Expected:', {
        stripe_customer_id: TEST_CUSTOMER_ID,
        stripe_subscription_id: TEST_SUBSCRIPTION_ID,
        subscription_type: TEST_SUBSCRIPTION_TYPE,
        membership: 'pro'
      });
      console.error('Actual:', profile);
    }
    
    return isSuccess;
  } catch (error) {
    console.error('Error verifying profile update:', error);
    throw error;
  }
}

async function cleanupTestProfile() {
  console.log('Cleaning up test profile...');
  
  try {
    // Delete the test profile
    await dbClient.query(
      'DELETE FROM profiles WHERE user_id = $1',
      [TEST_USER_ID]
    );
    
    console.log('Test profile deleted');
  } catch (error) {
    console.error('Error cleaning up test profile:', error);
  }
}

async function main() {
  try {
    // Connect to the database
    await dbClient.connect();
    console.log('Connected to database');
    
    // Run the test
    await createTestProfile();
    await updateProfileWithStripeData();
    await verifyProfileUpdate();
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    // Clean up
    await cleanupTestProfile();
    
    // Close the database connection
    await dbClient.end();
    console.log('Database connection closed');
  }
}

// Run the test
main().catch(console.error);
