#!/usr/bin/env node

/**
 * This script verifies that the subscription_type column has been added to the profiles table
 * Run with: node scripts/verify-subscription-type.js
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const { Client } = require('pg');

// Override the DATABASE_URL environment variable with the correct port
const DATABASE_URL = 'postgresql://postgres:RTNjGBnorvVpzAVeJJXAWbNfVOqKhCwJ@localhost:15435/railway?sslmode=prefer';

async function verifySubscriptionType() {
  const client = new Client({
    connectionString: DATABASE_URL,
    ssl: DATABASE_URL.includes('sslmode=require') 
      ? { rejectUnauthorized: false } 
      : false
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Query to check if the subscription_type column exists
    const query = `
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_name = 'profiles' AND column_name = 'subscription_type'
    `;

    const result = await client.query(query);

    if (result.rows.length > 0) {
      console.log('✅ subscription_type column exists in profiles table:');
      console.log(result.rows[0]);
    } else {
      console.log('❌ subscription_type column does not exist in profiles table');
    }

    // Query to check if the subscription_type enum exists
    const enumQuery = `
      SELECT n.nspname as schema, t.typname as type
      FROM pg_type t
      JOIN pg_namespace n ON n.oid = t.typnamespace
      WHERE t.typname = 'subscription_type'
    `;

    const enumResult = await client.query(enumQuery);

    if (enumResult.rows.length > 0) {
      console.log('✅ subscription_type enum exists:');
      console.log(enumResult.rows[0]);
    } else {
      console.log('❌ subscription_type enum does not exist');
    }

  } catch (error) {
    console.error('Error verifying subscription_type:', error);
  } finally {
    await client.end();
  }
}

verifySubscriptionType().catch(console.error);
