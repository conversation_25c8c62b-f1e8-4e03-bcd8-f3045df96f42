#!/usr/bin/env node

/**
 * This script checks for profiles created in the last 10 minutes.
 */

require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// Get the database URL from the environment
let databaseUrl = process.env.DATABASE_URL;
console.log('Original Database URL:', databaseUrl);

// Modify the URL to disable SSL
databaseUrl = databaseUrl.replace('sslmode=require', 'sslmode=disable');
console.log('Modified Database URL:', databaseUrl);

// Create a connection pool
const pool = new Pool({
  connectionString: databaseUrl,
  ssl: false // Disable SSL
});

async function checkRecentProfiles() {
  let client;
  
  try {
    console.log('Connecting to database...');
    client = await pool.connect();
    console.log('Connected to database successfully!');
    
    // Get current time
    const now = new Date();
    console.log('Current time:', now.toISOString());
    
    // Check for profiles created in the last 10 minutes
    console.log('\nChecking for profiles created in the last 10 minutes:');
    const recentResult = await client.query('SELECT * FROM profiles WHERE created_at > NOW() - INTERVAL \'10 minutes\' ORDER BY created_at DESC');
    
    if (recentResult.rows.length === 0) {
      console.log('No profiles created in the last 10 minutes');
    } else {
      console.log(`Found ${recentResult.rows.length} profiles created in the last 10 minutes:`);
      recentResult.rows.forEach((profile, index) => {
        console.log(`\nProfile ${index + 1}:`);
        console.log(JSON.stringify(profile, null, 2));
        
        // Check if this profile has complete Stripe data
        const hasStripeData = profile.stripe_customer_id && profile.stripe_subscription_id && profile.subscription_type;
        console.log(`Has complete Stripe data: ${hasStripeData ? 'Yes' : 'No'}`);
        
        if (!hasStripeData) {
          console.log('Missing Stripe data:');
          if (!profile.stripe_customer_id) console.log('- stripe_customer_id is missing');
          if (!profile.stripe_subscription_id) console.log('- stripe_subscription_id is missing');
          if (!profile.subscription_type) console.log('- subscription_type is missing');
        }
      });
    }
    
    // Check for profiles updated in the last 10 minutes
    console.log('\nChecking for profiles updated in the last 10 minutes:');
    const updatedResult = await client.query('SELECT * FROM profiles WHERE updated_at > NOW() - INTERVAL \'10 minutes\' AND updated_at != created_at ORDER BY updated_at DESC');
    
    if (updatedResult.rows.length === 0) {
      console.log('No profiles updated in the last 10 minutes');
    } else {
      console.log(`Found ${updatedResult.rows.length} profiles updated in the last 10 minutes:`);
      updatedResult.rows.forEach((profile, index) => {
        console.log(`\nProfile ${index + 1}:`);
        console.log(JSON.stringify(profile, null, 2));
        
        // Check if this profile has complete Stripe data
        const hasStripeData = profile.stripe_customer_id && profile.stripe_subscription_id && profile.subscription_type;
        console.log(`Has complete Stripe data: ${hasStripeData ? 'Yes' : 'No'}`);
        
        if (!hasStripeData) {
          console.log('Missing Stripe data:');
          if (!profile.stripe_customer_id) console.log('- stripe_customer_id is missing');
          if (!profile.stripe_subscription_id) console.log('- stripe_subscription_id is missing');
          if (!profile.subscription_type) console.log('- subscription_type is missing');
        }
      });
    }
    
  } catch (err) {
    console.error('Database error:', err);
  } finally {
    if (client) {
      client.release();
      console.log('Database connection released');
    }
    
    // Close the pool
    await pool.end();
    console.log('Connection pool closed');
  }
}

checkRecentProfiles().catch(console.error);
