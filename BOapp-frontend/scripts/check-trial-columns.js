#!/usr/bin/env node

/**
 * This script checks if the trial-related columns exist in the profiles table
 * Run with: node scripts/check-trial-columns.js
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const { Client } = require('pg');

// Get the database URL from environment variables
let databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Modify the connection string to disable SSL verification
databaseUrl = databaseUrl.replace('sslmode=require', 'sslmode=no-verify');
console.log(`🔍 Using database: ${databaseUrl.replace(/:[^:]*@/, ':****@')}`);

// Create a client with SSL disabled
const client = new Client({
  connectionString: databaseUrl,
  ssl: {
    rejectUnauthorized: false
  }
});

async function checkTrialColumns() {
  try {
    // Connect to the database
    await client.connect();
    console.log('✅ Connected to database');
    
    // Check if profiles table exists
    const tableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'profiles'
      )
    `);
    
    if (!tableResult.rows[0].exists) {
      console.log('❌ Profiles table does not exist in the database');
      await client.end();
      return;
    }
    
    console.log('✅ Profiles table exists');
    
    // Check for trial-related columns
    const columnsResult = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'profiles' 
      AND column_name IN ('trial_started_at', 'trial_ends_at', 'has_trial_expired')
    `);
    
    const foundColumns = columnsResult.rows.map(row => row.column_name);
    console.log('Trial columns found:', foundColumns);
    
    const missingColumns = ['trial_started_at', 'trial_ends_at', 'has_trial_expired']
      .filter(col => !foundColumns.includes(col));
    
    if (missingColumns.length > 0) {
      console.log('❌ Missing trial columns:', missingColumns);
    } else {
      console.log('✅ All trial columns are present in the profiles table');
    }
    
    // Close the connection
    await client.end();
    console.log('✅ Database connection closed');
    
  } catch (error) {
    console.error('❌ Error checking trial columns:', error);
    
    // Try to close the connection
    try {
      await client.end();
    } catch (closeError) {
      console.error('Error closing database connection:', closeError);
    }
    
    process.exit(1);
  }
}

// Start the process
checkTrialColumns();
