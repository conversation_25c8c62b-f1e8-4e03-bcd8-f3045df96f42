#!/usr/bin/env node

/**
 * This script checks if the database is accessible
 * Run with: node scripts/check-db.js
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

// Get the database URL from environment variables
const databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

console.log(`🔍 Checking database connection to: ${databaseUrl.replace(/:[^:]*@/, ':****@')}`);

// Create a client
const client = new Client({
  connectionString: databaseUrl,
  connectionTimeoutMillis: 5000, // 5 seconds
  ssl: databaseUrl.includes('sslmode=require') ? { rejectUnauthorized: false } : false
});

// Connect to the database
client.connect()
  .then(() => {
    console.log('✅ Successfully connected to the database');
    
    // Try a simple query
    return client.query('SELECT current_timestamp as time, current_database() as database');
  })
  .then(result => {
    console.log(`✅ Database query successful`);
    console.log(`📊 Database: ${result.rows[0].database}`);
    console.log(`⏰ Current database time: ${result.rows[0].time}`);
    
    // Close the connection
    return client.end();
  })
  .then(() => {
    console.log('✅ Connection closed successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('❌ Database connection error:', err.message);
    
    // Provide helpful suggestions based on the error
    if (err.code === 'ECONNREFUSED') {
      console.error('\nPossible solutions:');
      console.error('1. Make sure your database server is running');
      console.error('2. Check if the port in DATABASE_URL is correct');
      console.error('3. Verify that the host in DATABASE_URL is accessible from this machine');
    } else if (err.code === 'ETIMEDOUT' || err.code === 'CONNECT_TIMEOUT') {
      console.error('\nPossible solutions:');
      console.error('1. Check if there\'s a firewall blocking the connection');
      console.error('2. Verify that the database server is accepting connections');
      console.error('3. Try increasing the connection timeout');
    } else if (err.code === '28P01') {
      console.error('\nPossible solutions:');
      console.error('1. Check if the username and password in DATABASE_URL are correct');
    } else if (err.code === '3D000') {
      console.error('\nPossible solutions:');
      console.error('1. Verify that the database name in DATABASE_URL exists');
      console.error('2. Create the database if it doesn\'t exist');
    }
    
    process.exit(1);
  });
