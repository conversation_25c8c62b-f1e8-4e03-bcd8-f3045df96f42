#!/usr/bin/env node

/**
 * This script checks the profiles table in the database to see if Stripe data is being recorded.
 *
 * Usage:
 * 1. Make sure the database is running
 * 2. Run: node scripts/check-profiles.js
 */

require('dotenv').config({ path: '.env.local' });
const { Client } = require('pg');

// Database connection
const dbClient = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false } // Always disable SSL verification for this script
});

async function checkProfiles() {
  try {
    // Connect to the database
    await dbClient.connect();
    console.log('Connected to database');

    // Query all profiles
    const result = await dbClient.query('SELECT * FROM profiles');

    console.log(`Found ${result.rows.length} profiles in the database`);

    // Check each profile for Stripe data
    result.rows.forEach((profile, index) => {
      console.log(`\nProfile ${index + 1}:`);
      console.log(`  User ID: ${profile.user_id}`);
      console.log(`  Membership: ${profile.membership}`);
      console.log(`  Stripe Customer ID: ${profile.stripe_customer_id || 'Not set'}`);
      console.log(`  Stripe Subscription ID: ${profile.stripe_subscription_id || 'Not set'}`);
      console.log(`  Subscription Type: ${profile.subscription_type || 'Not set'}`);
      console.log(`  Created At: ${profile.created_at}`);
      console.log(`  Updated At: ${profile.updated_at}`);

      // Check if Stripe data is present
      const hasStripeData = profile.stripe_customer_id && profile.stripe_subscription_id && profile.subscription_type;
      console.log(`  Has Complete Stripe Data: ${hasStripeData ? 'Yes' : 'No'}`);
    });

    // Check for profiles with Stripe data
    const profilesWithStripeData = result.rows.filter(
      profile => profile.stripe_customer_id && profile.stripe_subscription_id && profile.subscription_type
    );

    console.log(`\nProfiles with complete Stripe data: ${profilesWithStripeData.length} out of ${result.rows.length}`);

    if (profilesWithStripeData.length === 0) {
      console.log('\nNo profiles found with complete Stripe data!');
      console.log('This suggests that the Stripe data is not being recorded in the database.');
    }

  } catch (error) {
    console.error('Error checking profiles:', error);
  } finally {
    // Close the database connection
    await dbClient.end();
    console.log('\nDatabase connection closed');
  }
}

// Run the check
checkProfiles().catch(console.error);
