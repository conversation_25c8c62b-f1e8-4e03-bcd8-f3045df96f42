#!/usr/bin/env node

/**
 * This script uses <PERSON><PERSON><PERSON> to update the database schema
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const { execSync } = require('child_process');

console.log('🔄 Running Drizzle schema push to update database...');

try {
  // Run Drizzle push command
  execSync('npx drizzle-kit push', { stdio: 'inherit' });
  
  console.log('✅ Database schema updated successfully!');
  process.exit(0);
} catch (error) {
  console.error('❌ Error updating database schema:', error.message);
  process.exit(1);
}
