// scripts/test-survey-submission.js
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const dotenv = require('dotenv');

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' });
// Also load from .env if .env.local doesn't exist
dotenv.config();

// Test user ID - replace with an actual user ID from your system
const TEST_USER_ID = process.env.TEST_USER_ID || 'test_user_id';

async function testSurveySubmission() {
  try {
    // Create database connection
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL environment variable is not set');
    }
    
    console.log('Connecting to database...');
    
    // Create a connection
    const client = postgres(connectionString, { 
      max: 1,
      ssl: process.env.DATABASE_SSL === "true" ? { rejectUnauthorized: false } : false
    });
    
    const db = drizzle(client);
    console.log('Connected to database');
    
    // Test survey responses
    const testResponses = {
      industry: 'pharmaceuticals',
      role: 'researcher',
      useCase: 'drug'
    };
    
    // Insert test survey response
    console.log(`Inserting test survey response for user ${TEST_USER_ID}...`);
    
    await db.execute(`
      INSERT INTO user_surveys (user_id, responses, completed, created_at, updated_at)
      VALUES ('${TEST_USER_ID}', '${JSON.stringify(testResponses)}', 'true', NOW(), NOW())
      ON CONFLICT (user_id) 
      DO UPDATE SET 
        responses = '${JSON.stringify(testResponses)}',
        completed = 'true',
        updated_at = NOW()
    `);
    
    console.log('Test survey response inserted successfully!');
    
    // Verify the survey response was saved
    console.log('Verifying survey response...');
    
    const result = await db.execute(`
      SELECT * FROM user_surveys WHERE user_id = '${TEST_USER_ID}'
    `);
    
    if (result.length > 0) {
      console.log('Survey response verification successful!');
      console.log('Survey data:', result[0]);
    } else {
      console.error('Survey response verification failed!');
    }
    
    // Close the connection
    await client.end();
    console.log('Database connection closed');
    
  } catch (error) {
    console.error('Error testing survey submission:', error);
    process.exit(1);
  }
}

testSurveySubmission();
