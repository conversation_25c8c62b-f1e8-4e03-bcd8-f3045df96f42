#!/usr/bin/env node

/**
 * This script checks the database connection and queries the profiles table.
 */

require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// Get the database URL from the environment
let databaseUrl = process.env.DATABASE_URL;
console.log('Original Database URL:', databaseUrl);

// Modify the URL to disable SSL
databaseUrl = databaseUrl.replace('sslmode=require', 'sslmode=disable');
console.log('Modified Database URL:', databaseUrl);

// Create a connection pool
const pool = new Pool({
  connectionString: databaseUrl,
  ssl: false // Disable SSL
});

async function checkConnection() {
  let client;

  try {
    console.log('Connecting to database...');
    client = await pool.connect();
    console.log('Connected to database successfully!');

    // Test query
    console.log('Running test query...');
    const result = await client.query('SELECT NOW() as time');
    console.log('Database time:', result.rows[0].time);

    // Query profiles table
    console.log('\nQuerying profiles table...');
    const profilesResult = await client.query('SELECT COUNT(*) FROM profiles');
    console.log(`Total profiles: ${profilesResult.rows[0].count}`);

    // Get a sample of profiles
    const sampleResult = await client.query('SELECT * FROM profiles LIMIT 5');
    console.log(`\nSample profiles (${sampleResult.rows.length}):`);
    sampleResult.rows.forEach((profile, index) => {
      console.log(`\nProfile ${index + 1}:`);
      console.log(JSON.stringify(profile, null, 2));
    });

  } catch (err) {
    console.error('Database connection error:', err);
  } finally {
    if (client) {
      client.release();
      console.log('Database connection released');
    }

    // Close the pool
    await pool.end();
    console.log('Connection pool closed');
  }
}

checkConnection().catch(console.error);
