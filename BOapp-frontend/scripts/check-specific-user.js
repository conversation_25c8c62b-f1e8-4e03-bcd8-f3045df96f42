#!/usr/bin/env node

/**
 * This script checks a specific user profile in the database.
 */

require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// Get the database URL from the environment
let databaseUrl = process.env.DATABASE_URL;
console.log('Original Database URL:', databaseUrl);

// Modify the URL to disable SSL
databaseUrl = databaseUrl.replace('sslmode=require', 'sslmode=disable');
console.log('Modified Database URL:', databaseUrl);

// Create a connection pool
const pool = new Pool({
  connectionString: databaseUrl,
  ssl: false // Disable SSL
});

// The user ID from the logs
const userId = 'user_2x1wjfPjawlMN2LmIvAMMWl3QkS';

async function checkUser() {
  let client;
  
  try {
    console.log('Connecting to database...');
    client = await pool.connect();
    console.log('Connected to database successfully!');
    
    // Query the specific user
    console.log(`\nQuerying profile for user: ${userId}`);
    const userResult = await client.query('SELECT * FROM profiles WHERE user_id = $1', [userId]);
    
    if (userResult.rows.length === 0) {
      console.log(`No profile found for user: ${userId}`);
      
      // Check if there's a profile with the Stripe customer ID from the logs
      const stripeCustomerId = 'cus_SIoIV9xuKdiIvT';
      console.log(`\nChecking for profile with Stripe customer ID: ${stripeCustomerId}`);
      const stripeResult = await client.query('SELECT * FROM profiles WHERE stripe_customer_id = $1', [stripeCustomerId]);
      
      if (stripeResult.rows.length === 0) {
        console.log(`No profile found with Stripe customer ID: ${stripeCustomerId}`);
      } else {
        console.log(`Found profile with Stripe customer ID: ${stripeCustomerId}`);
        console.log(JSON.stringify(stripeResult.rows[0], null, 2));
      }
      
      // Check if there's a profile with the Stripe subscription ID from the logs
      const stripeSubscriptionId = 'sub_1ROCfeDG8E3Bo2wQVpsMNpb9';
      console.log(`\nChecking for profile with Stripe subscription ID: ${stripeSubscriptionId}`);
      const subResult = await client.query('SELECT * FROM profiles WHERE stripe_subscription_id = $1', [stripeSubscriptionId]);
      
      if (subResult.rows.length === 0) {
        console.log(`No profile found with Stripe subscription ID: ${stripeSubscriptionId}`);
      } else {
        console.log(`Found profile with Stripe subscription ID: ${stripeSubscriptionId}`);
        console.log(JSON.stringify(subResult.rows[0], null, 2));
      }
    } else {
      console.log(`Found profile for user: ${userId}`);
      console.log(JSON.stringify(userResult.rows[0], null, 2));
    }
    
    // Check for any profiles with subscription_type set
    console.log('\nChecking for profiles with subscription_type set:');
    const subTypeResult = await client.query('SELECT * FROM profiles WHERE subscription_type IS NOT NULL');
    
    if (subTypeResult.rows.length === 0) {
      console.log('No profiles found with subscription_type set');
    } else {
      console.log(`Found ${subTypeResult.rows.length} profiles with subscription_type set:`);
      subTypeResult.rows.forEach((profile, index) => {
        console.log(`\nProfile ${index + 1}:`);
        console.log(JSON.stringify(profile, null, 2));
      });
    }
    
    // Check for any profiles created in the last hour
    console.log('\nChecking for profiles created in the last hour:');
    const recentResult = await client.query('SELECT * FROM profiles WHERE created_at > NOW() - INTERVAL \'1 hour\'');
    
    if (recentResult.rows.length === 0) {
      console.log('No profiles created in the last hour');
    } else {
      console.log(`Found ${recentResult.rows.length} profiles created in the last hour:`);
      recentResult.rows.forEach((profile, index) => {
        console.log(`\nProfile ${index + 1}:`);
        console.log(JSON.stringify(profile, null, 2));
      });
    }
    
  } catch (err) {
    console.error('Database error:', err);
  } finally {
    if (client) {
      client.release();
      console.log('Database connection released');
    }
    
    // Close the pool
    await pool.end();
    console.log('Connection pool closed');
  }
}

checkUser().catch(console.error);
