// Test script for Resend API
require('dotenv').config({ path: '.env.local' });
const { Resend } = require('resend');

// Initialize Resend with API key from environment variables
const resendApiKey = process.env.RESEND_API_KEY;
const resend = new Resend(resendApiKey);

// The from address for all emails
const fromEmail = process.env.EMAIL_FROM || '<EMAIL>';

// Replace with your email address for testing
const toEmail = '<EMAIL>'; // Using your Resend account email

async function testResend() {
  try {
    console.log('Testing Resend API...');
    console.log('API Key:', resendApiKey ? `${resendApiKey.substring(0, 8)}...` : 'Not set');
    console.log('From Email:', fromEmail);
    console.log('To Email:', toEmail);

    const result = await resend.emails.send({
      from: fromEmail,
      to: [toEmail],
      subject: 'Test Email from Resend',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4f46e5;">Test Email</h2>
          <p>This is a test email from Resend to verify that your API key is working correctly.</p>
          <p>If you received this email, your Resend configuration is working!</p>
        </div>
      `,
    });

    console.log('Email sent successfully!');
    console.log('Result:', result);
  } catch (error) {
    console.error('Error sending email:', error);
  }
}

testResend();
