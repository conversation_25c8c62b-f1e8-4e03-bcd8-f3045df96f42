#!/usr/bin/env node

/**
 * This script runs Drizzle migrations to update the database schema
 * Run with: node scripts/run-drizzle-migration.js
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const { execSync } = require('child_process');

console.log('🔄 Running Drizzle migrations...');

try {
  // Run the Drizzle migration for PostgreSQL
  execSync('npx drizzle-kit push:pg', { stdio: 'inherit' });

  console.log('✅ Drizzle migrations completed successfully!');
  process.exit(0);
} catch (error) {
  console.error('❌ Error running Drizzle migrations:', error.message);
  process.exit(1);
}
