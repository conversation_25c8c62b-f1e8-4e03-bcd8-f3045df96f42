#!/usr/bin/env node

/**
 * This script restores the trial-related fields to the profiles table
 * Run with: node scripts/restore-trial-fields.js
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const { Client } = require('pg');

// Get the database URL from environment variables
let databaseUrl = process.env.DATABASE_URL;

// Remove SSL requirement for local development
if (databaseUrl && databaseUrl.includes('sslmode=require')) {
  databaseUrl = databaseUrl.replace('sslmode=require', 'sslmode=prefer');
  console.log('Modified database URL to use sslmode=prefer');
}

if (!databaseUrl) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

console.log(`🔍 Connecting to database: ${databaseUrl.replace(/:[^:]*@/, ':****@')}`);

// Create a client
const client = new Client({
  connectionString: databaseUrl,
  // Disable SSL for local development
  ssl: false
});

async function restoreTrialFields() {
  try {
    // Connect to the database
    await client.connect();
    console.log('✅ Connected to database');

    // Check if columns exist
    const checkResult = await client.query(`
      SELECT
        column_name
      FROM
        information_schema.columns
      WHERE
        table_name = 'profiles' AND
        column_name IN ('trial_started_at', 'trial_ends_at', 'has_trial_expired')
    `);

    const existingColumns = checkResult.rows.map(row => row.column_name);
    console.log('Existing trial columns:', existingColumns);

    // Add missing columns
    if (!existingColumns.includes('trial_started_at')) {
      console.log('Adding trial_started_at column...');
      await client.query('ALTER TABLE "profiles" ADD COLUMN "trial_started_at" timestamp');
    }

    if (!existingColumns.includes('trial_ends_at')) {
      console.log('Adding trial_ends_at column...');
      await client.query('ALTER TABLE "profiles" ADD COLUMN "trial_ends_at" timestamp');
    }

    if (!existingColumns.includes('has_trial_expired')) {
      console.log('Adding has_trial_expired column...');
      await client.query('ALTER TABLE "profiles" ADD COLUMN "has_trial_expired" text DEFAULT \'false\'');
    }

    console.log('✅ Trial fields restored successfully!');

    // Close the connection
    await client.end();
    console.log('✅ Database connection closed');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error restoring trial fields:', error);

    // Try to close the connection
    try {
      await client.end();
    } catch (closeError) {
      console.error('Error closing database connection:', closeError);
    }

    process.exit(1);
  }
}

// Run the migration
restoreTrialFields();
