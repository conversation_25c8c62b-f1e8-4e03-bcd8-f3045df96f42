/*
Email service for sending verification emails and other notifications.
*/

import { Resend } from "resend"

// The from address for all emails
const fromEmail = process.env.EMAIL_FROM || "<EMAIL>"

// The base URL for verification links
const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"

// Create a function to get or create the Resend client
function getResendClient() {
  // Check if we have an API key
  const resendApiKey = process.env.RESEND_API_KEY

  if (!resendApiKey) {
    console.warn(
      "RESEND_API_KEY is not set. Email functionality will be disabled."
    )
    // Return a mock client that logs instead of sending
    return {
      emails: {
        send: async (options: any) => {
          console.log("Email would be sent (RESEND_API_KEY not set):", options)
          return {
            id: "mock-email-id",
            message: "Email sending skipped - no API key"
          }
        }
      }
    }
  }

  // Return the real client if we have an API key
  return new Resend(resendApi<PERSON>ey)
}

/**
 * Send a verification email to the user
 */
export async function sendVerificationEmail(
  to: string,
  token: string,
  name: string,
  institution: string
) {
  try {
    const verificationLink = `${baseUrl}/verify-email?token=${token}`

    // Get the Resend client (real or mock)
    const resend = getResendClient()

    const result = await resend.emails.send({
      from: fromEmail,
      to: [to],
      subject: "Verify your academic email address",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4f46e5;">Verify Your Academic Email</h2>
          <p>Hello ${name},</p>
          <p>Thank you for applying for academic access to Optimizer™. To complete your verification as a member of ${institution}, please click the button below:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationLink}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Verify Email Address</a>
          </div>
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #6b7280;">${verificationLink}</p>
          <p>This verification link will expire in 24 hours.</p>
          <p>If you did not request this verification, please ignore this email.</p>
          <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;" />
          <p style="color: #6b7280; font-size: 14px;">© ${new Date().getFullYear()} Optimizer™. All rights reserved.</p>
        </div>
      `
    })

    return { success: true, data: result }
  } catch (error) {
    console.error("Error sending verification email:", error)
    return { success: false, error }
  }
}
