/*
Utility functions for exporting data in various formats.
*/

import { saveAs } from "file-saver"
import * as XLSX from "xlsx"

/**
 * Convert an array of objects to CSV format
 * @param data Array of objects to convert
 * @param headers Optional custom headers (if not provided, will use object keys)
 * @returns CSV string
 */
export function convertToCSV<T extends Record<string, any>>(
  data: T[],
  headers?: { key: keyof T; label: string }[]
): string {
  if (!data || data.length === 0) {
    return ""
  }

  // If headers are not provided, use the keys of the first object
  const keys = headers
    ? headers.map(h => h.key)
    : (Object.keys(data[0]) as (keyof T)[])
  const headerLabels = headers ? headers.map(h => h.label) : (keys as string[])

  // Create the CSV header row
  const headerRow = headerLabels.map(label => `"${label}"`).join(",")

  // Create the data rows
  const rows = data.map(item => {
    return keys
      .map(key => {
        // Handle different data types
        const value = item[key]
        if (value === null || value === undefined) {
          return '""'
        } else if (typeof value === "string") {
          // Escape quotes in strings
          return `"${value.replace(/"/g, '""')}"`
        } else if (
          value &&
          typeof value === "object" &&
          "toISOString" in value
        ) {
          return `"${value.toISOString()}"`
        } else {
          return `"${value}"`
        }
      })
      .join(",")
  })

  // Combine header and rows
  return [headerRow, ...rows].join("\n")
}

/**
 * Download data as a CSV file using file-saver
 * @param data The data to download
 * @param filename The name of the file
 * @param headers Optional custom headers
 */
export function downloadCSV<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[]
): void {
  // Convert data to CSV
  const csv = convertToCSV(data, headers)

  // Create a Blob with the CSV data
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })

  // Use file-saver to save the file
  saveAs(blob, filename)
}

/**
 * Export data to true Excel (.xlsx) format
 * @param data The data to download
 * @param filename The name of the file
 * @param headers Optional custom headers
 */
export function downloadExcel<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[]
): void {
  // Make sure filename has .xlsx extension
  const excelFilename = filename.endsWith(".xlsx")
    ? filename
    : `${
        filename.endsWith(".csv")
          ? filename.substring(0, filename.lastIndexOf("."))
          : filename
      }.xlsx`

  // Create a new workbook
  const workbook = XLSX.utils.book_new()

  // Process data for Excel
  let excelData: any[]

  if (headers) {
    // If headers are provided, use them to order and rename columns
    excelData = data.map(item => {
      const row: Record<string, any> = {}
      headers.forEach(header => {
        row[header.label] = item[header.key]
      })
      return row
    })
  } else {
    // Otherwise use the data as is
    excelData = data
  }

  // Convert data to worksheet
  const worksheet = XLSX.utils.json_to_sheet(excelData)

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Data")

  // Generate Excel file as array buffer
  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" })

  // Create Blob from buffer
  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  })

  // Use file-saver to save the file
  saveAs(blob, excelFilename)
}

/**
 * Export data to Excel-compatible CSV format
 * @param data The data to download
 * @param filename The name of the file
 * @param headers Optional custom headers
 */
export function downloadExcelCSV<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[]
): void {
  // Make sure filename has .csv extension
  const baseFilename =
    filename.endsWith(".xlsx") || filename.endsWith(".csv")
      ? filename.substring(0, filename.lastIndexOf("."))
      : filename

  const csvFilename = `${baseFilename}.csv`

  // Convert data to CSV (Excel can open CSV files)
  const csv = convertToCSV(data, headers)

  // Add BOM (Byte Order Mark) for Excel compatibility with UTF-8
  const bom = new Uint8Array([0xef, 0xbb, 0xbf])

  // Use text/csv MIME type with UTF-8 encoding for better Excel compatibility
  const blob = new Blob([bom, csv], { type: "text/csv;charset=utf-8" })

  // Use file-saver to save the file
  saveAs(blob, csvFilename)
}
