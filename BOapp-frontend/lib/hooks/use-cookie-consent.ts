"use client"

import { useEffect, useState } from "react"

interface CookieConsentState {
  analytics: boolean
  marketing: boolean
  functional: boolean
  necessary: boolean
  isLoaded: boolean
}

export function useCookieConsent() {
  const [consentState, setConsentState] = useState<CookieConsentState>({
    analytics: false,
    marketing: false,
    functional: false,
    necessary: true, // Necessary cookies are always allowed
    isLoaded: false
  })

  useEffect(() => {
    // Check if CookieScript is loaded
    const checkCookieScript = () => {
      if (typeof window !== "undefined" && window.CookieScript) {
        setConsentState(prev => ({ ...prev, isLoaded: true }))

        // Get current consent state from CookieScript
        try {
          const currentState = window.CookieScript.currentState()
          if (currentState) {
            setConsentState(prev => ({
              ...prev,
              analytics: currentState.categories?.analytics || false,
              marketing: currentState.categories?.marketing || false,
              functional: currentState.categories?.functional || false,
              necessary: true
            }))
          }
        } catch (error) {
          console.warn("Could not get CookieScript state:", error)
        }
      }
    }

    // Check immediately
    checkCookieScript()

    // Set up event listeners for consent changes
    const handleAccept = () => {
      setConsentState(prev => ({
        ...prev,
        analytics: true,
        marketing: true,
        functional: true,
        necessary: true
      }))
    }

    const handleReject = () => {
      setConsentState(prev => ({
        ...prev,
        analytics: false,
        marketing: false,
        functional: false,
        necessary: true
      }))
    }

    const handleConsentChange = (event: CustomEvent) => {
      const { detail } = event
      if (detail && detail.categories) {
        setConsentState(prev => ({
          ...prev,
          analytics: detail.categories.analytics || false,
          marketing: detail.categories.marketing || false,
          functional: detail.categories.functional || false,
          necessary: true
        }))
      }
    }

    // Add event listeners
    window.addEventListener("CookieScriptAccept", handleAccept)
    window.addEventListener("CookieScriptReject", handleReject)
    window.addEventListener(
      "CookieScriptConsentChange",
      handleConsentChange as EventListener
    )

    // Cleanup
    return () => {
      window.removeEventListener("CookieScriptAccept", handleAccept)
      window.removeEventListener("CookieScriptReject", handleReject)
      window.removeEventListener(
        "CookieScriptConsentChange",
        handleConsentChange as EventListener
      )
    }
  }, [])

  // Helper functions
  const acceptAll = () => {
    if (typeof window !== "undefined" && window.CookieScript) {
      window.CookieScript.acceptAllCookies()
    }
  }

  const rejectAll = () => {
    if (typeof window !== "undefined" && window.CookieScript) {
      window.CookieScript.rejectAllCookies()
    }
  }

  const showSettings = () => {
    if (typeof window !== "undefined" && window.CookieScript) {
      window.CookieScript.showSettings()
    }
  }

  return {
    consentState,
    acceptAll,
    rejectAll,
    showSettings,
    hasAnalyticsConsent: consentState.analytics,
    hasMarketingConsent: consentState.marketing,
    hasFunctionalConsent: consentState.functional,
    isLoaded: consentState.isLoaded
  }
}
