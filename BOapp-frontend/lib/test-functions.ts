/*
This file contains implementations of common test functions used in optimization.
These functions are used for visualization purposes in the landing page.
*/

/**
 * Ackley function - a common test function for optimization algorithms
 * Has many local minima but only one global minimum at (0,0)
 *
 * @param x - x coordinate
 * @param y - y coordinate
 * @param a - parameter a (default: 20)
 * @param b - parameter b (default: 0.2)
 * @param c - parameter c (default: 2*PI)
 * @returns function value at (x,y)
 */
export function ackley(
  x: number,
  y: number,
  a: number = 20,
  b: number = 0.2,
  c: number = 2 * Math.PI
): number {
  const term1 = -a * Math.exp(-b * Math.sqrt(0.5 * (x * x + y * y)))
  const term2 = -Math.exp(0.5 * (Math.cos(c * x) + Math.cos(c * y)))
  return term1 + term2 + a + Math.exp(1)
}

/**
 * Rosenbrock function - a common test function for optimization algorithms
 * Has a narrow valley from the global minimum to a local curved valley
 *
 * @param x - x coordinate
 * @param y - y coordinate
 * @param a - parameter a (default: 1)
 * @param b - parameter b (default: 100)
 * @returns function value at (x,y)
 */
export function rosenbrock(
  x: number,
  y: number,
  a: number = 1,
  b: number = 100
): number {
  return (a - x) ** 2 + b * (y - x ** 2) ** 2
}

/**
 * Himmelblau function - a common test function with 4 identical local minima
 *
 * @param x - x coordinate
 * @param y - y coordinate
 * @returns function value at (x,y)
 */
export function himmelblau(x: number, y: number): number {
  return (x ** 2 + y - 11) ** 2 + (x + y ** 2 - 7) ** 2
}

/**
 * Rastrigin function - a highly multimodal function with many local minima
 *
 * @param x - x coordinate
 * @param y - y coordinate
 * @param A - parameter A (default: 10)
 * @returns function value at (x,y)
 */
export function rastrigin(x: number, y: number, A: number = 10): number {
  return (
    2 * A +
    x ** 2 -
    A * Math.cos(2 * Math.PI * x) +
    y ** 2 -
    A * Math.cos(2 * Math.PI * y)
  )
}

/**
 * Eggholder function - a difficult function to optimize with many local minima
 *
 * @param x - x coordinate
 * @param y - y coordinate
 * @returns function value at (x,y)
 */
export function eggholder(x: number, y: number): number {
  return (
    -(y + 47) * Math.sin(Math.sqrt(Math.abs(x / 2 + (y + 47)))) -
    x * Math.sin(Math.sqrt(Math.abs(x - (y + 47))))
  )
}

/**
 * Generate a grid of values for a 2D function
 *
 * @param func - The function to evaluate
 * @param xRange - Range of x values [min, max]
 * @param yRange - Range of y values [min, max]
 * @param resolution - Number of points in each dimension
 * @returns Object with x, y arrays and z 2D array
 */
export function generateFunctionGrid(
  func: (x: number, y: number) => number,
  xRange: [number, number],
  yRange: [number, number],
  resolution: number = 50
): { x: number[]; y: number[]; z: number[][] } {
  const x = Array.from(
    { length: resolution },
    (_, i) => xRange[0] + (i * (xRange[1] - xRange[0])) / (resolution - 1)
  )

  const y = Array.from(
    { length: resolution },
    (_, i) => yRange[0] + (i * (yRange[1] - yRange[0])) / (resolution - 1)
  )

  const z = Array.from({ length: resolution }, (_, i) =>
    Array.from({ length: resolution }, (_, j) => func(x[j], y[i]))
  )

  return { x, y, z }
}

/**
 * Generate a set of random points within the given ranges
 *
 * @param count - Number of points to generate
 * @param xRange - Range of x values [min, max]
 * @param yRange - Range of y values [min, max]
 * @returns Array of [x, y] points
 */
export function generateRandomPoints(
  count: number,
  xRange: [number, number],
  yRange: [number, number]
): [number, number][] {
  return Array.from({ length: count }, () => [
    xRange[0] + Math.random() * (xRange[1] - xRange[0]),
    yRange[0] + Math.random() * (yRange[1] - yRange[0])
  ])
}

/**
 * Find the minimum value in a 2D grid
 *
 * @param z - 2D array of values
 * @param x - Array of x coordinates
 * @param y - Array of y coordinates
 * @returns Object with minimum value and its coordinates
 */
export function findMinimum(
  z: number[][],
  x: number[],
  y: number[]
): { value: number; x: number; y: number } {
  let minValue = Infinity
  let minI = 0
  let minJ = 0

  for (let i = 0; i < z.length; i++) {
    for (let j = 0; j < z[i].length; j++) {
      if (z[i][j] < minValue) {
        minValue = z[i][j]
        minI = i
        minJ = j
      }
    }
  }

  return { value: minValue, x: x[minJ], y: y[minI] }
}
