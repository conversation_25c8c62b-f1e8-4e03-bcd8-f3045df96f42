{"name": "INNOptimizer™", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "npm run lint:fix && npm run format:write", "type-check": "tsc --noEmit", "lint:fix": "next lint --fix", "format:write": "prettier --write \"{app,lib,db,components,context,types}/**/*.{ts,tsx}\" --cache", "format:check": "prettier --check \"{app,lib,db,components,context,types}**/*.{ts,tsx}\" --cache", "analyze": "ANALYZE=true npm run build", "db:generate": "npx drizzle-kit generate", "db:migrate": "npx drizzle-kit migrate", "db:push": "npx drizzle-kit push", "prepare": "husky install"}, "dependencies": {"@clerk/backend": "^1.24.0", "@clerk/nextjs": "^6.11.2", "@clerk/themes": "^2.2.17", "@hookform/resolvers": "^4.0.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@tailwindcss/postcss": "^4.0.6", "@types/three": "^0.176.0", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.5.0", "3dmol": "^2.4.2", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "drizzle-orm": "^0.39.3", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "file-saver": "^2.0.5", "framer-motion": "^12.4.2", "gsap": "^3.13.0", "input-otp": "^1.4.2", "lucide-react": "^0.475.0", "motion": "^12.12.1", "next": "^15.3.1", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "pg": "^8.16.0", "plotly.js": "^3.0.1", "plotly.js-dist": "^3.0.1", "postcss": "^8.5.2", "postgres": "^3.4.5", "react": "^19.0", "react-day-picker": "^9.6.5", "react-dom": "^19.0", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "react-plotly.js": "^2.6.0", "react-resizable-panels": "^2.1.7", "react-tsparticles": "^2.12.2", "recharts": "^2.15.1", "resend": "^2.0.0", "sonner": "^1.7.4", "stripe": "^17.6.0", "svix": "^1.18.0", "tailwind-merge": "^3.0.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "tsparticles": "^3.8.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/file-saver": "^2.0.7", "@types/node": "^22", "@types/plotly.js": "^2.35.5", "@types/react": "^19.0", "@types/react-dom": "^19.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.30.4", "eslint": "^9", "eslint-config-next": "15.1.7", "eslint-config-prettier": "^10.0.1", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "^9.1.7", "prettier": "^3.5.0", "typescript": "^5"}}