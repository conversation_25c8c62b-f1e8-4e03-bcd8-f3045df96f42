# Trial Period Implementation

This document explains how the 14-day trial period functionality is implemented in the application.

## Overview

When a new user registers, they automatically receive a 14-day trial with full access to premium features. After the trial period expires, they are downgraded to the free tier unless they have subscribed to a paid plan.

## Database Schema

The trial information is stored in the `profiles` table with the following fields:

- `trialStartedAt`: Timestamp when the trial started
- `trialEndsAt`: Timestamp when the trial will end
- `hasTrialExpired`: Flag to track if the trial has expired ('true' or 'false')

## Key Components

### 1. Trial Initialization

When a new user registers, the trial is automatically initialized through the Clerk webhook handler:

- The webhook listens for the `user.created` event from Clerk
- When a new user is created, it calls `initializeTrialAction` to set up the trial
- The trial start date is set to the current date, and the end date is set to 14 days later

### 2. Trial Status Checking

The application checks the trial status in several ways:

- `checkTrialStatusAction`: Checks if a user's trial is active or expired
- `getCurrentUserTrialStatusAction`: Gets the trial status for the currently authenticated user
- `updateExpiredTrialsAction`: Batch job to find and update all expired trials

### 3. Subscription Integration

The trial functionality is integrated with the subscription system:

- `getSubscriptionAction`: Returns subscription information, including trial status
- If a user has an active Stripe subscription, they are not considered to be on a trial
- When a trial expires, the user's membership is downgraded to 'free'

### 4. UI Components

The UI displays trial information to the user:

- `SubscriptionInfo`: Provides trial status information to other components
- `SubscriptionPromo`: Displays a promotion banner with trial days remaining

## Scheduled Jobs

A scheduled job runs daily to check for expired trials:

- The `/api/cron/check-trials` endpoint is called by a scheduler (e.g., Vercel Cron)
- It runs `updateExpiredTrialsAction` to find and update expired trials
- Users with expired trials are automatically downgraded to the free tier

## Manual Testing

For testing purposes, you can manually initialize a trial:

- POST to `/api/user/initialize-trial` (requires authentication)
- This will start or restart a 14-day trial for the authenticated user

## Migration

When deploying this feature, you need to run the database migration:

```bash
# Run the migration to add trial fields to the profiles table
npx drizzle-kit push:pg
```

## Security Considerations

- Trial initialization is protected by Clerk webhook verification
- The cron job for checking expired trials is protected by a secret token
- Manual trial initialization requires user authentication

## Future Improvements

Potential improvements to consider:

1. Add email notifications when a trial is about to expire
2. Implement trial extensions for special promotions
3. Add analytics to track trial conversion rates
4. Create an admin interface for managing trials
