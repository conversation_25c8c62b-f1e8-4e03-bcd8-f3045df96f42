# CookieScript Integration Setup Guide

This guide explains how to set up and configure the CookieScript cookie consent widget in your Next.js application.

## Overview

CookieScript is a GDPR/CCPA compliant cookie consent management platform that automatically:
- Scans your website for cookies
- Displays a customizable consent banner
- Blocks third-party cookies based on user preferences
- Provides detailed cookie reporting and analytics

## Setup Instructions

### 1. Create a CookieScript Account

1. Go to [cookie-script.com](https://cookie-script.com)
2. Sign up for a free account
3. Create a new website/domain in your dashboard
4. Copy your unique CookieScript ID from the installation tab

### 2. Configure Environment Variables

Add your CookieScript ID to your environment file:

```bash
# In .env.local
NEXT_PUBLIC_COOKIESCRIPT_ID=your_actual_cookiescript_id_here
```

**Important**: Replace `your_actual_cookiescript_id_here` with the actual ID from your CookieScript dashboard.

### 3. Integration Details

The integration is already set up in your project with the following components:

#### Files Added/Modified:
- `components/utilities/cookie-script.tsx` - Main CookieScript component
- `lib/hooks/use-cookie-consent.ts` - React hook for managing consent state
- `app/layout.tsx` - Root layout with CookieScript integration
- `types/global.d.ts` - TypeScript definitions for CookieScript
- `.env.local` - Environment configuration

#### Features Included:
- ✅ Automatic cookie scanning and categorization
- ✅ GDPR/CCPA compliant consent banner
- ✅ Google Consent Mode v2 integration
- ✅ React hook for consent state management
- ✅ TypeScript support
- ✅ Debug mode for development
- ✅ Event listeners for consent changes

### 4. Customization Options

#### Basic Usage
The CookieScript component is already integrated in your root layout with default settings.

#### Advanced Configuration
You can customize the component by passing props:

```tsx
<CookieScript 
  cookieScriptId="your_id_here"
  enableGoogleConsentMode={true}
  enableDebugMode={false}
/>
```

#### Using the Consent Hook
Use the `useCookieConsent` hook in your components to react to consent changes:

```tsx
import { useCookieConsent } from '@/lib/hooks/use-cookie-consent'

function MyComponent() {
  const { 
    hasAnalyticsConsent, 
    hasMarketingConsent, 
    acceptAll, 
    showSettings 
  } = useCookieConsent()

  // Only load analytics if user consented
  useEffect(() => {
    if (hasAnalyticsConsent) {
      // Load Google Analytics, etc.
    }
  }, [hasAnalyticsConsent])

  return (
    <div>
      <button onClick={acceptAll}>Accept All Cookies</button>
      <button onClick={showSettings}>Cookie Settings</button>
    </div>
  )
}
```

### 5. CookieScript Dashboard Configuration

In your CookieScript dashboard, you can:

1. **Customize the banner appearance**:
   - Colors, fonts, and positioning
   - Button text and messages
   - Multiple language support

2. **Configure cookie categories**:
   - Necessary (always enabled)
   - Functional
   - Analytics
   - Marketing

3. **Set up automatic scanning**:
   - Monthly automatic cookie scans
   - Automatic script blocking
   - Cookie database updates

4. **Enable advanced features**:
   - Google Consent Mode v2
   - IAB TCF 2.0 compliance
   - Cross-domain consent sharing

### 6. Testing the Integration

1. **Development Testing**:
   - Set `enableDebugMode={true}` in the CookieScript component
   - Check browser console for CookieScript logs
   - Test consent banner appearance and functionality

2. **Production Testing**:
   - Verify the banner appears on first visit
   - Test accept/reject functionality
   - Check that third-party scripts are properly blocked/allowed
   - Validate Google Consent Mode signals (if using Google Analytics)

### 7. Compliance Features

The integration includes:

- **GDPR Compliance**: Proper consent collection and storage
- **CCPA Compliance**: "Do Not Sell" functionality
- **Google Consent Mode v2**: Automatic integration with Google services
- **Cookie Blocking**: Automatic blocking of third-party cookies based on consent
- **Consent Records**: Detailed logging of user consent choices

### 8. Troubleshooting

#### Common Issues:

1. **Banner not appearing**:
   - Check that `NEXT_PUBLIC_COOKIESCRIPT_ID` is set correctly
   - Verify the ID in your CookieScript dashboard
   - Check browser console for errors

2. **TypeScript errors**:
   - Ensure `types/global.d.ts` includes CookieScript definitions
   - Restart your TypeScript server

3. **Consent state not updating**:
   - Check that event listeners are properly set up
   - Verify CookieScript is fully loaded before accessing state

#### Debug Mode:
Enable debug mode to see detailed console logs:
```tsx
<CookieScript enableDebugMode={true} />
```

### 9. Additional Resources

- [CookieScript Documentation](https://help.cookie-script.com/)
- [Google Consent Mode v2 Guide](https://help.cookie-script.com/en/google-consent-mode-v2-installation)
- [GDPR Compliance Guide](https://cookie-script.com/gdpr-and-e-privacy-regulation-compliance.html)

## Support

For CookieScript-specific issues, contact their support team through the dashboard or visit their help center.
For integration issues with this Next.js setup, check the console logs and verify your configuration.
