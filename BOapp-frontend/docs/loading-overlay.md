# Loading Overlay

This document explains how to use the global loading overlay for API calls in the application.

## Overview

The loading overlay provides visual feedback to users when API calls are in progress. It displays a spinner and "Loading..." text centered on the screen with a semi-transparent backdrop.

## Implementation

The loading overlay is implemented using:

1. A React Context (`LoadingContext`) to manage the global loading state
2. A `LoadingOverlay` component that displays when API calls are in progress
3. Utility functions and hooks to integrate with API calls

## Usage

There are three ways to use the loading overlay:

### 1. Using the `useApiLoading` hook

This is the recommended approach for component-level API calls:

```tsx
import { useApiLoading } from "@/lib/hooks/use-api-loading"

function MyComponent() {
  const { withLoading } = useApiLoading()

  const handleFetchData = withLoading(async () => {
    // Your API call here
    const data = await fetchSomeData()
    return data
  })

  return (
    <button onClick={handleFetchData}>
      Fetch Data
    </button>
  )
}
```

### 2. Using the `fetchWithLoading` utility

For direct API calls, you can use the `fetchWithLoading` utility:

```tsx
import { fetchWithLoading } from "@/lib/api/loading-fetch"

async function fetchData() {
  try {
    // This will automatically trigger the loading overlay
    const result = await fetchWithLoading("/api/endpoint", "GET")
    return result
  } catch (error) {
    console.error("Error fetching data:", error)
  }
}
```

### 3. Directly using the `useLoading` hook

For more complex scenarios, you can use the `useLoading` hook directly:

```tsx
import { useLoading } from "@/contexts/loading-context"

function MyComponent() {
  const { startLoading, stopLoading } = useLoading()

  const handleComplexOperation = async () => {
    startLoading()
    try {
      // Your complex operation here
      await doSomethingComplex()
    } finally {
      stopLoading()
    }
  }

  return (
    <button onClick={handleComplexOperation}>
      Do Complex Operation
    </button>
  )
}
```

## Customization

The loading overlay can be customized by modifying the `LoadingOverlay` component in `components/ui/loading-overlay.tsx`. You can change:

- The appearance of the spinner
- The text displayed
- The backdrop color and opacity
- The animation timing and effects

## Notes

- The loading overlay has a 300ms delay before appearing to prevent flashing for very quick operations
- It also has a fade-in/fade-out effect for a smoother user experience
- Multiple concurrent API calls will keep the overlay visible until all calls are complete
