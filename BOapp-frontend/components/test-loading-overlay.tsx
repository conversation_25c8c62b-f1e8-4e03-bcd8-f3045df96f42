"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useLoading } from "@/contexts/loading-context"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function TestLoadingOverlay() {
  const { startLoading, stopLoading } = useLoading()
  const [isTestRunning, setIsTestRunning] = useState(false)

  const simulateLoading = async (seconds: number) => {
    if (isTestRunning) return

    setIsTestRunning(true)
    startLoading()

    try {
      // Simulate an API call with a delay
      await new Promise(resolve => setTimeout(resolve, seconds * 1000))
    } finally {
      stopLoading()
      setIsTestRunning(false)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Test Loading Overlay</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col space-y-2">
          <Button
            onClick={() => simulateLoading(2)}
            disabled={isTestRunning}
            variant="default"
          >
            Show Loading for 2 Seconds
          </Button>

          <Button
            onClick={() => simulateLoading(5)}
            disabled={isTestRunning}
            variant="secondary"
          >
            Show Loading for 5 Seconds
          </Button>
        </div>

        <p className="text-muted-foreground text-sm">
          Click a button to test the global loading overlay.
        </p>
      </CardContent>
    </Card>
  )
}
