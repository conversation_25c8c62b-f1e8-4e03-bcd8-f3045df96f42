/*
This client component provides a subscription promotion banner for the sidebar.
*/

"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Sparkles, ArrowUpRight, X, CheckCircle2, Crown } from "lucide-react"
import { cn } from "@/lib/utils"
import Link from "next/link"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"

type SubscriptionTier =
  | "free"
  | "trial"
  | "basic"
  | "advanced"
  | "monthly"
  | "yearly"
  | "premium"
  | "premium-monthly"
  | "premium-yearly"

interface SubscriptionPromoProps {
  currentTier: SubscriptionTier
  trialDaysLeft?: number
  isCollapsed: boolean
}

export function SubscriptionPromo({
  currentTier,
  trialDaysLeft = 14,
  isCollapsed
}: SubscriptionPromoProps) {
  const [isDismissed, setIsDismissed] = useState(false)

  if (
    isDismissed ||
    currentTier === "monthly" ||
    currentTier === "yearly" ||
    currentTier === "premium" ||
    currentTier === "premium-monthly" ||
    currentTier === "premium-yearly"
  )
    return null

  // Define tier details matching actual pricing cards
  const tiers = {
    free: {
      name: "Free",
      color: "bg-gray-500",
      next: "premium-monthly",
      cta: "Start Free Trial",
      message: "Limited access",
      benefit:
        "14-day full access trial + All optimization features + Advanced analytics",
      urgency: "Get started with full access today"
    },
    trial: {
      name: "Trial",
      color: "bg-amber-500",
      next: "premium-monthly",
      cta: "Subscribe to Continue",
      message: `${trialDaysLeft} days remaining`,
      benefit:
        "Unlimited access + Priority support + Advanced analytics + Unlimited optimizations",
      urgency:
        trialDaysLeft <= 3
          ? "Trial ending soon!"
          : "Don't lose access to premium features"
    },
    basic: {
      name: "Basic",
      color: "bg-blue-500",
      next: "premium-monthly",
      cta: "Upgrade to Premium",
      message: "Limited features",
      benefit:
        "Unlimited access + Priority support + Advanced analytics + Unlimited optimizations",
      urgency: "Unlock all features with premium"
    },
    advanced: {
      name: "Advanced",
      color: "bg-purple-500",
      next: "premium-monthly",
      cta: "Upgrade to Premium",
      message: "Enhanced features",
      benefit:
        "Unlimited access + Priority support + Advanced analytics + Unlimited optimizations",
      urgency: "Get the most out of your optimizations"
    },
    monthly: {
      name: "Premium Monthly",
      color: "bg-emerald-500",
      next: null,
      cta: "",
      message: "€79/month",
      benefit: "",
      urgency: ""
    },
    yearly: {
      name: "Premium Yearly",
      color: "bg-emerald-500",
      next: null,
      cta: "",
      message: "€799/year (Save 17%)",
      benefit: "",
      urgency: ""
    },
    premium: {
      name: "Premium",
      color: "bg-emerald-500",
      next: null,
      cta: "",
      message: "Unlimited access",
      benefit: "",
      urgency: ""
    },
    "premium-monthly": {
      name: "Premium Monthly",
      color: "bg-emerald-500",
      next: null,
      cta: "",
      message: "€79/month",
      benefit: "",
      urgency: ""
    },
    "premium-yearly": {
      name: "Premium Yearly",
      color: "bg-emerald-500",
      next: null,
      cta: "",
      message: "€799/year (Save 17%)",
      benefit: "",
      urgency: ""
    }
  }

  const currentTierInfo = tiers[currentTier]
  const nextTier = currentTierInfo.next
    ? tiers[currentTierInfo.next as SubscriptionTier]
    : null

  // Create simple tooltip content for collapsed view
  const tooltipContent = (
    <div className="text-center text-xs">
      <div className="font-medium">{currentTierInfo.cta || "Subscription"}</div>
    </div>
  )

  if (isCollapsed) {
    return (
      <div className="border-t p-2">
        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Link href="/dashboard/billing">
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-full",
                    currentTier === "trial"
                      ? "hover:border-amber-200 hover:bg-amber-50"
                      : "hover:border-blue-200 hover:bg-blue-50"
                  )}
                >
                  <div className="flex flex-col items-center space-y-1">
                    {currentTier === "trial" ? (
                      <Crown className="size-4 text-amber-500" />
                    ) : (
                      <ArrowUpRight className="size-4 text-blue-500" />
                    )}
                    <div
                      className={cn(
                        "size-1.5 rounded-full",
                        currentTier === "trial" ? "bg-amber-500" : "bg-blue-500"
                      )}
                    ></div>
                  </div>
                </Button>
              </Link>
            </TooltipTrigger>
            <TooltipContent side="right">{tooltipContent}</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    )
  }

  return (
    <div className="border-t p-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center">
            {currentTier === "trial" ? (
              <Crown className="mr-1 size-4 text-amber-500" />
            ) : (
              <Sparkles className="mr-1 size-4 text-blue-500" />
            )}
            <span className="text-xs font-medium">
              {currentTier === "trial" ? "Trial" : "Plan"}
            </span>
          </div>

          <div className="flex items-center">
            <div
              className={cn(
                "size-2 rounded-full",
                currentTier === "trial" ? "bg-amber-500" : "bg-blue-500"
              )}
            ></div>
            <span className="ml-1 text-xs">
              {currentTierInfo.name}
              {currentTier === "trial" && (
                <span
                  className={cn(
                    "ml-1 font-medium",
                    trialDaysLeft <= 3 ? "text-red-500" : "text-amber-500"
                  )}
                >
                  ({trialDaysLeft}d left)
                </span>
              )}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-1">
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href="/dashboard/billing">
                  <Button variant="ghost" size="icon" className="size-6">
                    {currentTier === "trial" ? (
                      <Crown className="size-3" />
                    ) : (
                      <ArrowUpRight className="size-3" />
                    )}
                    <span className="sr-only">
                      {currentTier === "trial" ? "Subscribe" : "Upgrade"}
                    </span>
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent side="right">
                <div className="text-xs">
                  <div className="font-medium">{currentTierInfo.cta}</div>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            variant="ghost"
            size="icon"
            className="size-6"
            onClick={() => setIsDismissed(true)}
          >
            <X className="size-3" />
            <span className="sr-only">Dismiss</span>
          </Button>
        </div>
      </div>
    </div>
  )
}
