"use client"

import { useEffect } from "react"
import { useAuth } from "@clerk/nextjs"
import { ensureUserTrialAction } from "@/actions/ensure-trial-action"

/**
 * This component ensures that a user has a trial initialized.
 * It should be included in the dashboard layout.
 */
export function EnsureTrial() {
  const { isSignedIn, isLoaded } = useAuth()

  useEffect(() => {
    // Only run if the auth state is loaded and the user is signed in
    if (isLoaded && isSignedIn) {
      const checkAndInitializeTrial = async () => {
        try {
          await ensureUserTrialAction()
        } catch (error) {
          console.error("Error ensuring trial:", error)
        }
      }

      checkAndInitializeTrial()
    }
  }, [isLoaded, isSignedIn])

  // This component doesn't render anything
  return null
}
