/*
This client component provides a beta feedback note banner for the sidebar.
*/

"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { X, MessageSquarePlus, Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { motion } from "framer-motion"
import Image from "next/image"
import { BRAND } from "@/lib/constants"

interface BetaFeedbackNoteProps {
  isCollapsed: boolean
}

export function BetaFeedbackNote({ isCollapsed }: BetaFeedbackNoteProps) {
  const [isDismissed, setIsDismissed] = useState(false)

  if (isDismissed) return null

  if (isCollapsed) {
    return (
      <div className="px-2 py-3">
        <Button
          variant="outline"
          className="relative h-10 w-full border-2 border-dashed border-purple-500 p-0"
          onClick={() => setIsDismissed(false)}
        >
          <Sparkles className="size-5 text-purple-500" />
        </Button>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="relative m-3 overflow-hidden rounded-lg border-2 border-dashed border-purple-500 p-3"
    >
      <button
        className="text-muted-foreground hover:bg-accent hover:text-accent-foreground absolute right-2 top-2 rounded-full p-0.5"
        onClick={() => setIsDismissed(true)}
      >
        <X className="size-3" />
      </button>

      <div className="mb-2 flex justify-center">
        <div className="relative size-24 overflow-hidden rounded-lg">
          <Image
            src="/images/ai-robot-assistant.png"
            alt="AI Assistant Robot"
            fill
            className="object-cover"
          />
        </div>
      </div>

      <h4 className="mb-1 text-center text-sm font-semibold">Beta Product</h4>
      <p className="text-muted-foreground mb-3 text-center text-xs">
        Your feedback is crucial to help us improve the {BRAND.NAME}. We're
        actively enhancing the product based on user input.
      </p>

      <Link href="/dashboard/feedback">
        <Button size="sm" className="w-full gap-1">
          <MessageSquarePlus className="size-3.5" />
          <span>Share Feedback</span>
        </Button>
      </Link>
    </motion.div>
  )
}
