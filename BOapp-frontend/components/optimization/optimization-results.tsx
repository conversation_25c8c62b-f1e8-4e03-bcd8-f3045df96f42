// components/optimization/optimization-results.tsx
"use client"

import React, { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"
import { getBestPointWorkflowAction } from "@/actions/optimization-workflow-actions"
import { getMeasurementsAction } from "@/actions/db/optimizations-actions"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts"
import {
  ArrowU<PERSON>,
  ArrowDown,
  RefreshC<PERSON>,
  Beaker,
  Trophy,
  History,
  ChevronRight,
  Download,
  RotateCw,
  Target,
  Check,
  Layers,
  AreaChart,
  FileSpreadsheet
} from "lucide-react"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { ParameterImpactChart } from "@/components/optimization/parameter-impact-chart"
import { OptimizationStatusControl } from "@/components/optimization/optimization-status-control"
import { OptimizationStatusHistory } from "@/components/optimization/optimization-status-history"
import { BestResultsDocumentation } from "@/components/documentation/best-results-docs"
import { AnalysisTab } from "@/components/optimization/analysis/analysis-tab"
import { OverviewDashboard } from "@/components/optimization/analysis/overview-dashboard"
import { downloadCSV, downloadExcel } from "@/lib/export-utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"

// Helper function to format dates consistently for server and client
const formatDate = (date: Date) => {
  // Use a consistent format that doesn't depend on locale
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}:${String(date.getSeconds()).padStart(2, "0")}`
}

// Helper function to format data for the chart
const formatChartData = (
  measurements: SelectMeasurement[],
  targetName?: string
) => {
  // Group measurements into batches based on their batch ID (same logic as in History tab)
  const batches: SelectMeasurement[][] = []
  const measurementsCopy = [...measurements]

  // Sort by creation date (newest first)
  measurementsCopy.sort((a, b) => {
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  })

  // Group by batch ID
  while (measurementsCopy.length > 0) {
    const current = measurementsCopy.shift()!
    const batchId = current.batchId

    // If this measurement has a batch ID, find all others with the same ID
    if (batchId) {
      const batchItems = [current]
      let i = 0
      while (i < measurementsCopy.length) {
        if (measurementsCopy[i].batchId === batchId) {
          batchItems.push(measurementsCopy.splice(i, 1)[0])
        } else {
          i++
        }
      }
      batches.push(batchItems)
    } else {
      // If no batch ID, treat as a single-item batch
      batches.push([current])
    }
  }

  // Sort batches by creation date (newest first)
  batches.sort((a, b) => {
    const aTime = new Date(a[0].createdAt).getTime()
    const bTime = new Date(b[0].createdAt).getTime()
    return bTime - aTime
  })

  // Reverse batches to show oldest first in the chart (consistent with History tab)
  const reversedBatches = [...batches].reverse()

  // Create chart data points with experiment numbers matching the History tab
  const chartData: Record<string, any>[] = []
  let experimentNumber = 1

  reversedBatches.forEach(batch => {
    if (batch.length === 1) {
      // Single measurement (not a batch)
      const measurement = batch[0]
      let targetValue = parseFloat(measurement.targetValue)

      // Create the data point with experiment number
      const dataPoint: Record<string, any> = {
        iteration: experimentNumber, // Use experiment number instead of sequential index
        experimentNumber: experimentNumber, // Store the experiment number explicitly
        experimentLabel: `${experimentNumber}`, // Label for the x-axis
        targetValue,
        isRecommended: measurement.isRecommended,
        parameters: measurement.parameters,
        timestamp: measurement.createdAt,
        isBatchItem: false
      }

      // Add all target values as separate properties for multi-target visualizations
      if (measurement.targetValues) {
        const targetValues = measurement.targetValues as Record<string, number>
        Object.entries(targetValues).forEach(([key, value]) => {
          dataPoint[key] = value
        })
      }

      // Make sure the primary target is included as a named property
      if (targetName) {
        // If we have targetValues and a specific target name, use that value
        if (
          measurement.targetValues &&
          measurement.targetValues[
            targetName as keyof typeof measurement.targetValues
          ] !== undefined
        ) {
          dataPoint[targetName] = measurement.targetValues[
            targetName as keyof typeof measurement.targetValues
          ] as number
        } else {
          // Otherwise use the targetValue
          dataPoint[targetName] = targetValue
        }
      }

      chartData.push(dataPoint)
    } else {
      // For batches, create individual data points for each item in the batch
      // Sort batch items by creation time (oldest first) to match the History tab's ordering
      const sortedBatch = [...batch].sort((a, b) => {
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      })

      sortedBatch.forEach((measurement, batchIndex) => {
        const targetValue = parseFloat(measurement.targetValue)

        // Create the data point with experiment number and sub-index
        const dataPoint: Record<string, any> = {
          iteration: `${experimentNumber}.${batchIndex + 1}`, // Use format like "8.1", "8.2"
          experimentNumber: experimentNumber, // Store the main experiment number
          experimentLabel: `${experimentNumber}.${batchIndex + 1}`, // Label for the x-axis
          batchIndex: batchIndex + 1, // Store the batch index (1-based)
          targetValue,
          isRecommended: measurement.isRecommended,
          parameters: measurement.parameters,
          timestamp: measurement.createdAt,
          isBatchItem: true,
          batchSize: batch.length
        }

        // Add all target values as separate properties for multi-target visualizations
        if (measurement.targetValues) {
          const targetValues = measurement.targetValues as Record<
            string,
            number
          >
          Object.entries(targetValues).forEach(([key, value]) => {
            dataPoint[key] = value
          })
        }

        // Make sure the primary target is included as a named property
        if (targetName) {
          // If we have targetValues and a specific target name, use that value
          if (
            measurement.targetValues &&
            measurement.targetValues[
              targetName as keyof typeof measurement.targetValues
            ] !== undefined
          ) {
            dataPoint[targetName] = measurement.targetValues[
              targetName as keyof typeof measurement.targetValues
            ] as number
          } else {
            // Otherwise use the targetValue
            dataPoint[targetName] = targetValue
          }
        }

        chartData.push(dataPoint)
      })
    }

    // Increment experiment number for the next batch
    experimentNumber++
  })

  return chartData
}

// Helper function to calculate best values at each iteration
const calculateBestValues = (
  chartData: any[],
  mode: "MAX" | "MIN",
  targetName?: string,
  isMultiTarget: boolean = false
) => {
  let bestValue = mode === "MAX" ? -Infinity : Infinity
  let bestValuesByExperiment: Record<number, number> = {}

  // First pass: calculate best values by main experiment number
  chartData.forEach(point => {
    // For multi-target optimizations, use the specific target value if available
    let value
    if (isMultiTarget && targetName && point[targetName] !== undefined) {
      value = parseFloat(point[targetName])
    } else {
      value = parseFloat(point.targetValue)
    }

    const experimentNumber = point.experimentNumber

    // Initialize if not exists
    if (!bestValuesByExperiment[experimentNumber]) {
      bestValuesByExperiment[experimentNumber] =
        mode === "MAX" ? -Infinity : Infinity
    }

    // Update best value for this experiment
    if (mode === "MAX") {
      bestValuesByExperiment[experimentNumber] = Math.max(
        bestValuesByExperiment[experimentNumber],
        value
      )
      bestValue = Math.max(bestValue, value)
    } else {
      bestValuesByExperiment[experimentNumber] = Math.min(
        bestValuesByExperiment[experimentNumber],
        value
      )
      bestValue = Math.min(bestValue, value)
    }
  })

  // Second pass: assign best values to each point
  return chartData.map(point => {
    // For the best value line, we want to show the best value up to this experiment
    // Get all experiment numbers up to and including this one
    const experimentNumber = point.experimentNumber
    const experimentsToConsider = Object.keys(bestValuesByExperiment)
      .map(Number)
      .filter(expNum => expNum <= experimentNumber)

    // Find the best value among all these experiments
    let bestValueSoFar = mode === "MAX" ? -Infinity : Infinity
    experimentsToConsider.forEach(expNum => {
      if (mode === "MAX") {
        bestValueSoFar = Math.max(
          bestValueSoFar,
          bestValuesByExperiment[expNum]
        )
      } else {
        bestValueSoFar = Math.min(
          bestValueSoFar,
          bestValuesByExperiment[expNum]
        )
      }
    })

    return { ...point, bestValue: bestValueSoFar }
  })
}

// Helper function to get target mode for a specific target in multi-target optimization
const getTargetMode = (
  optimization: SelectOptimization,
  targetName: string
): "MAX" | "MIN" => {
  if (optimization.targetMode !== "MULTI") {
    // Handle enum-style target modes like "TargetMode.MAX"
    const targetMode = String(optimization.targetMode).toUpperCase()
    if (targetMode.includes("MAX")) {
      return "MAX"
    } else if (targetMode.includes("MIN")) {
      return "MIN"
    }
    return "MAX" // Default to MAX if we can't determine
  }

  // For multi-target, find the specific target's mode from the config
  try {
    const config = optimization.config as any
    if (Array.isArray(config.target_config)) {
      const targetConfig = config.target_config.find(
        (t: any) => t.name === targetName
      )
      if (targetConfig && targetConfig.mode) {
        return targetConfig.mode as "MAX" | "MIN"
      }
    }
  } catch (error) {
    console.error("Error getting target mode:", error)
  }

  // Default to MAX if we can't determine
  return "MAX"
}

interface OptimizationResultsProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  initialBestPoint?: {
    best_parameters?: Record<string, any>
    best_value?: number
    best_values?: Record<string, number>
    composite_score?: number
    normalized_values?: Record<string, number>
    target_weights?: Record<string, number>
  }
}

export function OptimizationResults({
  optimization,
  measurements: initialMeasurements,
  initialBestPoint
}: OptimizationResultsProps) {
  const router = useRouter()
  const [measurements, setMeasurements] =
    useState<SelectMeasurement[]>(initialMeasurements)
  const [bestPoint, setBestPoint] = useState(initialBestPoint || {})
  const [isLoadingBest, setIsLoadingBest] = useState(false)
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")
  const [activeAnalysisSection, setActiveAnalysisSection] =
    useState("parameters")
  const [expandedBatches, setExpandedBatches] = useState<
    Record<string, boolean>
  >({})
  const [decimalPrecision, setDecimalPrecision] = useState(2) // Default to 2 decimal places
  const [batchIds, setBatchIds] = useState<string[]>([]) // Track all batch IDs for expand/collapse all
  const [allExpanded, setAllExpanded] = useState(false) // Track whether all batches are expanded
  const [optimizationState, setOptimizationState] = useState(optimization) // Track optimization state

  // Helper function to format numbers with the specified precision
  const formatNumber = (
    value: number,
    precision: number = decimalPrecision
  ) => {
    return value.toFixed(precision)
  }

  const refreshBestPoint = async () => {
    setIsLoadingBest(true)
    try {
      const result = await getBestPointWorkflowAction(optimization.optimizerId)
      if (result.isSuccess && result.data) {
        setBestPoint(result.data)
      } else {
        toast({
          title: "Error loading best point",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error refreshing best point:", error)
      toast({
        title: "Error",
        description: "Failed to refresh best point",
        variant: "destructive"
      })
    } finally {
      setIsLoadingBest(false)
    }
  }

  const refreshMeasurements = async () => {
    setIsLoadingHistory(true)
    try {
      const result = await getMeasurementsAction(optimization.id)
      if (result.isSuccess && result.data) {
        setMeasurements(result.data)
      } else {
        toast({
          title: "Error loading measurements",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error refreshing measurements:", error)
      toast({
        title: "Error",
        description: "Failed to refresh measurements history",
        variant: "destructive"
      })
    } finally {
      setIsLoadingHistory(false)
    }
  }

  // Prepare data for export
  const prepareExportData = () => {
    // Create a flattened array of all measurements with experiment numbers
    const exportData: Record<string, any>[] = []

    // Group measurements into batches (similar to the table rendering logic)
    const batches: (typeof measurements)[] = []
    const measurementsCopy = [...measurements]

    // Sort by creation date (newest first)
    measurementsCopy.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    })

    // Group by batch ID (simplified version of the table rendering logic)
    while (measurementsCopy.length > 0) {
      const current = measurementsCopy.shift()!
      const batchId = current.batchId

      if (batchId) {
        const batchItems = [current]
        let i = 0
        while (i < measurementsCopy.length) {
          if (measurementsCopy[i].batchId === batchId) {
            batchItems.push(measurementsCopy.splice(i, 1)[0])
          } else {
            i++
          }
        }
        batches.push(batchItems)
      } else {
        batches.push([current])
      }
    }

    // Sort batches by creation date (newest first)
    batches.sort((a, b) => {
      const aTime = new Date(a[0].createdAt).getTime()
      const bTime = new Date(b[0].createdAt).getTime()
      return bTime - aTime
    })

    // Reverse batches to show oldest first in the export (consistent with History tab)
    const reversedBatches = [...batches].reverse()

    // Create export data with experiment numbers
    let experimentNumber = 1

    reversedBatches.forEach(batch => {
      if (batch.length === 1) {
        // Single measurement
        const measurement = batch[0]

        // Create the data point with experiment number
        const dataPoint: Record<string, any> = {
          "Experiment #": experimentNumber,
          Date: formatDate(new Date(measurement.createdAt)),
          [optimization.targetName]: parseFloat(
            measurement.targetValue
          ).toFixed(decimalPrecision),
          Source: measurement.isRecommended ? "API" : "Manual",
          "Suggestion Type":
            (measurement.parameters as any)?._sampleClass === "exploratory"
              ? "Exploratory"
              : "Sequential"
        }

        // Add all target values for multi-target optimizations
        if (isMultiTarget && measurement.targetValues) {
          const targetValues = measurement.targetValues as Record<
            string,
            number
          >
          Object.entries(targetValues).forEach(([key, value]) => {
            if (key !== optimization.targetName) {
              dataPoint[key] = value.toFixed(decimalPrecision)
            }
          })
        }

        // Add all parameter values
        const params = measurement.parameters as Record<string, any>
        parameterNames.forEach((name: string) => {
          if (typeof params[name] === "number") {
            dataPoint[name] = params[name].toFixed(decimalPrecision)
          } else if (params[name] !== undefined) {
            dataPoint[name] = String(params[name])
          } else {
            dataPoint[name] = ""
          }
        })

        exportData.push(dataPoint)
      } else {
        // For batches, create individual data points for each item
        batch.forEach((measurement, batchIndex) => {
          // Create the data point with experiment number and sub-index
          const dataPoint: Record<string, any> = {
            "Experiment #": `${experimentNumber}.${batchIndex + 1}`,
            Date: formatDate(new Date(measurement.createdAt)),
            [optimization.targetName]: parseFloat(
              measurement.targetValue
            ).toFixed(decimalPrecision),
            Source: measurement.isRecommended ? "API" : "Manual",
            "Suggestion Type":
              (measurement.parameters as any)?._sampleClass === "exploratory"
                ? "Exploratory"
                : "Batch Item"
          }

          // Add all target values for multi-target optimizations
          if (isMultiTarget && measurement.targetValues) {
            const targetValues = measurement.targetValues as Record<
              string,
              number
            >
            Object.entries(targetValues).forEach(([key, value]) => {
              if (key !== optimization.targetName) {
                dataPoint[key] = value.toFixed(decimalPrecision)
              }
            })
          }

          // Add all parameter values
          const params = measurement.parameters as Record<string, any>
          parameterNames.forEach((name: string) => {
            if (typeof params[name] === "number") {
              dataPoint[name] = params[name].toFixed(decimalPrecision)
            } else if (params[name] !== undefined) {
              dataPoint[name] = String(params[name])
            } else {
              dataPoint[name] = ""
            }
          })

          exportData.push(dataPoint)
        })
      }

      // Increment experiment number for the next batch
      experimentNumber++
    })

    return exportData
  }

  // Handle export to CSV
  const handleExportCSV = async () => {
    if (measurements.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no measurements available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export
      const exportData = prepareExportData()

      // Generate filename with optimization name and current date
      const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-measurements-${new Date().toISOString().split("T")[0]}.csv`

      // Download the CSV
      downloadCSV(exportData, filename)

      toast({
        title: "Export successful",
        description: "The measurements data has been exported as CSV.",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting measurements to CSV:", error)
      toast({
        title: "Export failed",
        description: "Failed to export measurements data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Handle export to Excel (.xlsx)
  const handleExportExcel = async () => {
    if (measurements.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no measurements available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export
      const exportData = prepareExportData()

      // Generate filename with optimization name and current date
      const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-measurements-${new Date().toISOString().split("T")[0]}.xlsx`

      // Download as Excel (.xlsx)
      downloadExcel(exportData, filename)

      toast({
        title: "Export successful",
        description:
          "The measurements data has been exported as Excel (.xlsx) file.",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting measurements to Excel:", error)
      toast({
        title: "Export failed",
        description: "Failed to export measurements data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Determine if this is a multi-target optimization
  const isMultiTarget = optimization.targetMode === "MULTI"

  // Get the total number of targets from the configuration
  const getTargetCount = () => {
    if (!isMultiTarget) return 1

    const targetConfig = (optimization.config as any).target_config
    if (Array.isArray(targetConfig)) {
      return targetConfig.length
    }
    return 1
  }

  const targetCount = getTargetCount()

  // Prepare chart data
  const primaryTargetMode = getTargetMode(optimization, optimization.targetName)
  let chartData = calculateBestValues(
    formatChartData(measurements, optimization.targetName),
    primaryTargetMode,
    optimization.targetName,
    isMultiTarget
  )

  // Debug chart data
  console.log("Chart data:", chartData)

  // For multi-target optimization, add composite score to chart data
  if (isMultiTarget && measurements.length > 0) {
    const targetConfigs = (optimization.config as any).target_config || []

    // Create a map of target names to their modes
    const targetModes: Record<string, "MAX" | "MIN"> = {}
    targetConfigs.forEach((target: any) => {
      targetModes[target.name] = target.mode as "MAX" | "MIN"
    })

    // Calculate min/max values for each target for normalization
    const targetMinMax: Record<string, { min: number; max: number }> = {}
    Object.keys(targetModes).forEach(targetName => {
      const values = measurements
        .map(m => {
          if (
            m.targetValues &&
            typeof m.targetValues === "object" &&
            targetName in (m.targetValues as Record<string, number>)
          ) {
            return (m.targetValues as Record<string, number>)[targetName]
          }
          return targetName === optimization.targetName
            ? parseFloat(m.targetValue)
            : NaN
        })
        .filter(v => !isNaN(v))

      if (values.length > 0) {
        targetMinMax[targetName] = {
          min: Math.min(...values),
          max: Math.max(...values)
        }
      }
    })

    // If we have best point data but no composite score, calculate it
    if (bestPoint.best_values && !bestPoint.composite_score) {
      console.log("Calculating composite score for best point")
      let bestScore = 0
      let totalWeight = 0
      const normalizedValues: Record<string, number> = {}
      const targetWeights: Record<string, number> = {}

      Object.keys(targetModes).forEach(targetName => {
        if (
          bestPoint.best_values &&
          targetName in bestPoint.best_values &&
          targetMinMax[targetName]
        ) {
          const value = bestPoint.best_values[targetName]
          const { min, max } = targetMinMax[targetName]
          const weight = 1 / Object.keys(targetModes).length // Equal weights

          // Skip if min equals max (no variation)
          if (min === max) return

          // Normalize value between 0 and 1
          let normalized = (value - min) / (max - min)

          // Invert for MIN targets
          if (targetModes[targetName] === "MIN") {
            normalized = 1 - normalized
          }

          bestScore += normalized * weight
          totalWeight += weight

          // Store normalized value and weight
          normalizedValues[targetName] = normalized
          targetWeights[targetName] = weight
        }
      })

      // Add composite score to the best point
      if (totalWeight > 0) {
        bestPoint.composite_score = bestScore
        bestPoint.normalized_values = normalizedValues
        bestPoint.target_weights = targetWeights
        console.log("Calculated composite score:", bestScore)
        console.log("Normalized values:", normalizedValues)
        console.log("Target weights:", targetWeights)
      }
    }
  }

  // Prepare data series for other targets in multi-target optimization
  const otherTargets: string[] = []

  if (
    isMultiTarget &&
    measurements.length > 0 &&
    measurements[0].targetValues
  ) {
    // Get all target names from the first measurement's targetValues
    const firstMeasurement = measurements[0]
    if (firstMeasurement.targetValues) {
      Object.keys(firstMeasurement.targetValues).forEach(targetName => {
        if (targetName !== optimization.targetName) {
          otherTargets.push(targetName)
        }
      })
    }
    console.log("Other targets for multi-target visualization:", otherTargets)
  }

  // Determine the parameter names from the configuration
  const parameterNames = ((optimization.config as any).parameters || []).map(
    (param: any) => param.name
  )

  // Function to toggle expand/collapse all batches
  const toggleAllBatches = () => {
    // Toggle the state
    const newExpandedState = !allExpanded
    setAllExpanded(newExpandedState)

    // Update all batch states
    const batchStates: Record<string, boolean> = {}
    batchIds.forEach(id => {
      batchStates[id] = newExpandedState
    })
    setExpandedBatches(batchStates)

    // Show toast notification
    toast({
      title: newExpandedState
        ? "Expanded all batches"
        : "Collapsed all batches",
      description: `${batchIds.length} batch${batchIds.length !== 1 ? "es" : ""} ${newExpandedState ? "expanded" : "collapsed"}`
    })
  }

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <div className="mb-6 flex flex-col justify-between sm:flex-row sm:items-center">
        <h2 className="text-2xl font-bold">{optimization.name}</h2>
        <TabsList className="mt-4 sm:mt-0">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="parameters">Parameters</TabsTrigger>
          <TabsTrigger value="analysis">Analysis</TabsTrigger>
        </TabsList>
      </div>

      <TabsContent value="overview" className="space-y-6">
        {/* Best Point Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div className="space-y-1">
              <CardTitle className="flex items-center text-lg">
                <Trophy className="mr-2 size-5 text-yellow-500" />
                Best Result
                <BestResultsDocumentation />
              </CardTitle>
              <CardDescription>
                Current best point found by the optimizer
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshBestPoint}
              disabled={isLoadingBest}
            >
              {isLoadingBest ? (
                <RotateCw className="mr-2 size-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 size-4" />
              )}
              Refresh
            </Button>
          </CardHeader>
          <CardContent>
            {bestPoint.best_parameters ? (
              <div className="space-y-4">
                {/* For multi-target: Show Composite Score as primary metric */}
                {isMultiTarget && bestPoint.composite_score !== undefined ? (
                  <div className="bg-primary/10 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">Composite Score</h4>
                        <p className="text-muted-foreground mt-1 text-xs">
                          Weighted score combining all targets
                        </p>
                      </div>
                      <span className="text-primary flex items-center text-xl font-bold">
                        {bestPoint.composite_score.toFixed(4)}
                        <Trophy className="text-primary ml-1 size-5" />
                      </span>
                    </div>
                  </div>
                ) : (
                  /* For single-target: Show target value */
                  <div className="bg-muted rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{optimization.targetName}</h4>
                      <span className="flex items-center text-xl font-bold">
                        {bestPoint.best_value?.toFixed(4)}
                        {optimization.targetMode === "MAX" ||
                        optimization.targetMode.includes("MAX") ? (
                          <ArrowUp className="ml-1 size-5 text-green-500" />
                        ) : (
                          <ArrowDown className="ml-1 size-5 text-green-500" />
                        )}
                      </span>
                    </div>
                  </div>
                )}

                {/* Target Contributions for multi-target optimization */}
                {isMultiTarget &&
                  bestPoint.normalized_values &&
                  bestPoint.target_weights && (
                    <div className="mt-2 space-y-2">
                      <h4 className="font-medium">Target Contributions:</h4>
                      <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                        {Object.entries(bestPoint.normalized_values).map(
                          ([targetName, normalizedValue]) => {
                            // Get the raw value from best_values
                            const rawValue = bestPoint.best_values?.[targetName]
                            if (rawValue === undefined) return null

                            // Get the weight
                            const weight =
                              bestPoint.target_weights?.[targetName] ||
                              1.0 /
                                Object.keys(bestPoint.normalized_values || {})
                                  .length

                            // Get the target config to determine if it's MAX or MIN
                            const targetConfig = Array.isArray(
                              (optimization.config as any).target_config
                            )
                              ? (optimization.config as any).target_config.find(
                                  (t: any) => t.name === targetName
                                )
                              : null
                            const targetMode = targetConfig?.mode || ""
                            const isMax =
                              targetMode === "MAX" ||
                              (typeof targetMode === "string" &&
                                targetMode.includes("MAX"))

                            // Calculate contribution to composite score
                            const contribution = weight * normalizedValue

                            return (
                              <div
                                key={targetName}
                                className="bg-muted rounded-lg p-3"
                              >
                                <div className="flex items-center justify-between">
                                  <div>
                                    <h5 className="text-sm font-medium">
                                      {targetName}
                                    </h5>
                                    <div className="text-muted-foreground mt-1 flex items-center text-xs">
                                      <span>
                                        Weight: {(weight * 100).toFixed(0)}%
                                      </span>
                                      <span className="mx-1">•</span>
                                      <span>
                                        Contribution:{" "}
                                        {(contribution * 100).toFixed(0)}%
                                      </span>
                                    </div>
                                  </div>
                                  <span className="flex items-center text-lg font-bold">
                                    {rawValue.toFixed(4)}
                                    {isMax ? (
                                      <ArrowUp className="ml-1 size-4 text-green-500" />
                                    ) : (
                                      <ArrowDown className="ml-1 size-4 text-green-500" />
                                    )}
                                  </span>
                                </div>
                                {/* Progress bar showing normalized value */}
                                <div className="bg-muted-foreground/20 mt-2 h-1.5 w-full rounded-full">
                                  <div
                                    className="bg-primary h-1.5 rounded-full"
                                    style={{
                                      width: `${normalizedValue * 100}%`
                                    }}
                                  ></div>
                                </div>
                              </div>
                            )
                          }
                        )}
                      </div>
                    </div>
                  )}

                <div className="space-y-2">
                  <h4 className="font-medium">Best Parameters:</h4>
                  <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3">
                    {/* Display parameters in the same order as defined in the Parameter Configuration */}
                    {parameterNames.map((paramName: string) => {
                      // Check if best_parameters exists
                      if (!bestPoint.best_parameters) return null

                      const value = bestPoint.best_parameters[paramName]
                      // Skip if parameter is not in best_parameters
                      if (value === undefined) return null

                      return (
                        <div
                          key={paramName}
                          className="bg-card rounded-md border p-3"
                        >
                          <div className="text-sm font-medium">{paramName}</div>
                          <div className="mt-1 text-sm">
                            {typeof value === "number"
                              ? value.toFixed(value % 1 === 0 ? 0 : 2)
                              : String(value)}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-muted-foreground py-8 text-center">
                {isLoadingBest ? (
                  <div className="flex flex-col items-center">
                    <RefreshCw className="mb-2 size-8 animate-spin" />
                    <p>Loading best point...</p>
                  </div>
                ) : (
                  <p>
                    No best point available yet. Add measurements to get
                    started.
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Progress Chart Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Optimization Progress</CardTitle>
            <CardDescription>
              {isMultiTarget
                ? "Target values over iterations"
                : "Target value improvement over iterations"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {chartData.length > 0 ? (
              <div className="h-[300px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={chartData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 30 }} // Increased bottom margin for legend
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                    <XAxis
                      dataKey="experimentLabel"
                      label={{
                        value: "Experiment",
                        position: "insideBottom",
                        offset: -5
                      }}
                      tickFormatter={value => value}
                    />
                    <YAxis
                      label={{
                        value: isMultiTarget
                          ? "Target Values"
                          : optimization.targetName,
                        angle: -90,
                        position: "insideLeft",
                        offset: 0, // Adjust offset to center vertically
                        style: { textAnchor: "middle" } // Center text
                      }}
                    />
                    <Tooltip labelFormatter={value => `Experiment ${value}`} />
                    <Legend
                      verticalAlign="bottom"
                      height={36}
                      wrapperStyle={{
                        paddingTop: "10px",
                        marginBottom: "10px"
                      }}
                    />
                    {/* Always show the primary target line */}
                    <Line
                      type="monotone"
                      dataKey={
                        isMultiTarget ? optimization.targetName : "targetValue"
                      }
                      stroke="#8884d8"
                      name={optimization.targetName}
                      activeDot={{ r: 8 }}
                      isAnimationActive={false}
                      connectNulls
                      strokeWidth={2}
                    />

                    {/* For single-target, show best value line */}
                    {!isMultiTarget && (
                      <Line
                        type="monotone"
                        dataKey="bestValue"
                        stroke="#82ca9d"
                        name={`Best ${optimization.targetName}`}
                        strokeWidth={2}
                        isAnimationActive={false}
                        connectNulls
                      />
                    )}

                    {/* For multi-target, show other target lines */}
                    {isMultiTarget &&
                      otherTargets.map((targetName, index) => (
                        <Line
                          key={targetName}
                          type="monotone"
                          dataKey={targetName}
                          stroke={
                            [
                              "#82ca9d",
                              "#ff7300",
                              "#0088FE",
                              "#00C49F",
                              "#FFBB28"
                            ][index % 5]
                          }
                          name={targetName}
                          activeDot={{ r: 6 }}
                          isAnimationActive={false}
                          connectNulls
                        />
                      ))}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="text-muted-foreground py-8 text-center">
                <p>No measurement data available yet.</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Parameter Impact Analysis */}
        {measurements.length > 0 && (
          <ParameterImpactChart
            measurements={measurements}
            parameterNames={parameterNames}
            targetName={optimization.targetName}
            targetMode={isMultiTarget ? "MULTI" : primaryTargetMode}
            availableTargets={
              isMultiTarget ? [optimization.targetName, ...otherTargets] : []
            }
          />
        )}

        {/* Summary Stats Card */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Total Measurements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{measurements.length}</div>
              <p className="text-muted-foreground text-xs">
                Experiments conducted
              </p>
            </CardContent>
          </Card>

          <OptimizationStatusControl
            optimization={optimizationState}
            onStatusChange={newStatus => {
              // Update the local state with the new status
              setOptimizationState(prev => ({
                ...prev,
                status: newStatus
              }))

              // Show a toast notification
              toast({
                title: "Status Updated",
                description: `Optimization status changed to ${newStatus}`
              })
            }}
          />

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Target Objective
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <div className="text-2xl font-bold">
                  {optimization.targetMode}
                </div>
                {optimization.targetMode === "MAX" ||
                optimization.targetMode === "MULTI" ||
                (typeof optimization.targetMode === "string" &&
                  optimization.targetMode.includes("MAX")) ? (
                  <ArrowUp className="ml-2 size-5 text-green-500" />
                ) : (
                  <ArrowDown className="ml-2 size-5 text-green-500" />
                )}
              </div>
              {optimization.targetMode === "MULTI" ? (
                <div>
                  <p className="text-muted-foreground text-xs">
                    Multi-target optimization
                  </p>
                  <div className="mt-2 flex items-center gap-1">
                    <Target className="text-muted-foreground size-3" />
                    <span className="text-xs">{targetCount} targets</span>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground text-xs">
                  {optimization.targetMode === "MAX"
                    ? "Maximizing"
                    : "Minimizing"}{" "}
                  {optimization.targetName}
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">API ID</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-muted-foreground truncate text-sm font-medium">
                {optimization.optimizerId}
              </div>
              <p className="mt-2 text-xs">
                <Button
                  variant="link"
                  size="sm"
                  className="h-auto p-0"
                  onClick={() =>
                    router.push(
                      `/dashboard/optimizations/${optimization.id}/run`
                    )
                  }
                >
                  Run experiments <ChevronRight className="ml-1 size-3" />
                </Button>
              </p>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="history" className="space-y-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div className="space-y-1">
              <CardTitle className="flex items-center text-lg">
                <History className="mr-2 size-5" />
                Measurement History
              </CardTitle>
              <CardDescription>
                All recorded measurements for this optimization
              </CardDescription>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <label
                  htmlFor="decimal-precision"
                  className="whitespace-nowrap text-sm"
                >
                  Decimal Places:
                </label>
                <select
                  id="decimal-precision"
                  className="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"
                  value={decimalPrecision}
                  onChange={e => setDecimalPrecision(Number(e.target.value))}
                >
                  {[0, 1, 2, 3, 4, 5, 6].map(num => (
                    <option key={num} value={num}>
                      {num}
                    </option>
                  ))}
                </select>
              </div>
              {batchIds.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleAllBatches}
                  disabled={isLoadingHistory}
                  className="px-2"
                >
                  <ChevronRight
                    className={`mr-1 size-4 transition-transform ${allExpanded ? "rotate-90" : ""}`}
                  />
                  {allExpanded ? "Collapse All" : "Expand All"}
                </Button>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={
                      isLoadingHistory ||
                      isExporting ||
                      measurements.length === 0
                    }
                  >
                    {isExporting ? (
                      <RotateCw className="mr-2 size-4 animate-spin" />
                    ) : (
                      <Download className="mr-2 size-4" />
                    )}
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleExportCSV}>
                    <Download className="mr-2 size-4" />
                    <span>Export as CSV</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleExportExcel}>
                    <FileSpreadsheet className="mr-2 size-4" />
                    <span>Export as Excel (.xlsx)</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshMeasurements}
                disabled={isLoadingHistory}
              >
                {isLoadingHistory ? (
                  <RotateCw className="mr-2 size-4 animate-spin" />
                ) : (
                  <RefreshCw className="mr-2 size-4" />
                )}
                Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {measurements.length > 0 ? (
              <div className="rounded-md border">
                {/* Table with horizontal scrolling */}
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {/* Fixed columns - always visible */}
                        <TableHead className="bg-background sticky left-0 z-20 w-[100px]">
                          Experiment #
                        </TableHead>
                        <TableHead className="bg-background sticky left-[100px] z-20">
                          Date
                        </TableHead>

                        {/* Target columns */}
                        <TableHead className="border-l text-right">
                          {optimization.targetName}
                          {isMultiTarget && (
                            <span className="text-muted-foreground block text-xs">
                              (Avg, Min, Max)
                            </span>
                          )}
                        </TableHead>

                        {/* Other targets for multi-target optimization */}
                        {isMultiTarget &&
                          otherTargets.map(targetName => (
                            <TableHead key={targetName} className="text-right">
                              {targetName}
                              <span className="text-muted-foreground block text-xs">
                                (Avg, Min, Max)
                              </span>
                            </TableHead>
                          ))}

                        {/* All parameter columns */}
                        {parameterNames.map((name: string) => (
                          <TableHead key={name} className="min-w-[120px]">
                            {name}
                          </TableHead>
                        ))}

                        {/* Fixed columns at the end */}
                        <TableHead className="bg-background sticky right-[120px] z-20 border-l text-right">
                          Source
                        </TableHead>
                        <TableHead className="bg-background sticky right-0 z-20 text-right">
                          Suggestion Type
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {/* Measurements with simple batch detection */}
                      {(() => {
                        // Group measurements into batches based on their batch ID
                        const batches: Array<Array<(typeof measurements)[0]>> =
                          []

                        // Debug output with more details
                        console.log("All measurements (raw):", measurements)
                        console.log(
                          "All measurements (formatted):",
                          measurements.map(m => ({
                            id: m.id,
                            createdAt: formatDate(new Date(m.createdAt)),
                            batchId: m.batchId,
                            targetValue: m.targetValue,
                            isRecommended: m.isRecommended,
                            parameters: m.parameters
                          }))
                        )

                        // Sort measurements by creation time (newest first)
                        const sortedMeasurements = [...measurements].sort(
                          (a, b) =>
                            new Date(b.createdAt).getTime() -
                            new Date(a.createdAt).getTime()
                        )

                        // Group measurements into batches based on their batch ID
                        // In Bayesian optimization, a batch is a set of measurements that were suggested together
                        // and are part of the same iteration of the optimization loop

                        // First, separate measurements by source (API vs Manual)
                        const apiMeasurements: typeof measurements = []
                        const manualMeasurements: typeof measurements = []

                        sortedMeasurements.forEach(m => {
                          if (m.isRecommended) {
                            apiMeasurements.push(m)
                          } else {
                            manualMeasurements.push(m)
                          }
                        })

                        // Create a map to track which measurements have been assigned to batches
                        const assignedMeasurements = new Set<string>()

                        // Group API measurements by batch ID
                        if (apiMeasurements.length > 0) {
                          // First, group by batch ID
                          const batchGroups: Record<
                            string,
                            typeof measurements
                          > = {}

                          // Process all API measurements
                          for (const measurement of apiMeasurements) {
                            // IMPORTANT: Use the exact batch ID as the key if available
                            // This ensures measurements with the same batch ID are grouped together
                            const batchKey = measurement.batchId
                              ? measurement.batchId
                              : `single_${measurement.id}`

                            // Log for debugging
                            console.log(
                              `Processing measurement ${measurement.id} with batchKey ${batchKey}`
                            )

                            if (!batchGroups[batchKey]) {
                              batchGroups[batchKey] = []
                            }

                            batchGroups[batchKey].push(measurement)
                            assignedMeasurements.add(measurement.id)
                          }

                          // Log batch groups for debugging
                          console.log(
                            "Batch groups:",
                            Object.keys(batchGroups).map(key => ({
                              key,
                              count: batchGroups[key].length,
                              ids: batchGroups[key].map(m => m.id),
                              batchIds: batchGroups[key].map(m => m.batchId)
                            }))
                          )

                          // Convert the groups to batches
                          for (const batchKey in batchGroups) {
                            // Sort measurements within a batch by creation time
                            const sortedBatch = batchGroups[batchKey].sort(
                              (a, b) =>
                                new Date(a.createdAt).getTime() -
                                new Date(b.createdAt).getTime()
                            )

                            batches.push(sortedBatch)
                          }
                        }

                        // Group manual measurements by batch ID, similar to API measurements
                        if (manualMeasurements.length > 0) {
                          // First, group by batch ID
                          const manualBatchGroups: Record<
                            string,
                            typeof measurements
                          > = {}

                          // Process all manual measurements
                          for (const measurement of manualMeasurements) {
                            if (!assignedMeasurements.has(measurement.id)) {
                              // Use the batch ID as the key if available, otherwise use a unique key
                              const batchKey = measurement.batchId
                                ? measurement.batchId
                                : `single_${measurement.id}`

                              // Log for debugging
                              console.log(
                                `Processing manual measurement ${measurement.id} with batchKey ${batchKey}`
                              )

                              if (!manualBatchGroups[batchKey]) {
                                manualBatchGroups[batchKey] = []
                              }

                              manualBatchGroups[batchKey].push(measurement)
                              assignedMeasurements.add(measurement.id)
                            }
                          }

                          // Log manual batch groups for debugging
                          console.log(
                            "Manual batch groups:",
                            Object.keys(manualBatchGroups).map(key => ({
                              key,
                              count: manualBatchGroups[key].length,
                              ids: manualBatchGroups[key].map(m => m.id),
                              batchIds: manualBatchGroups[key].map(
                                m => m.batchId
                              )
                            }))
                          )

                          // Convert the groups to batches
                          for (const batchKey in manualBatchGroups) {
                            // Sort measurements within a batch by creation time
                            const sortedBatch = manualBatchGroups[
                              batchKey
                            ].sort(
                              (a, b) =>
                                new Date(a.createdAt).getTime() -
                                new Date(b.createdAt).getTime()
                            )

                            batches.push(sortedBatch)
                          }
                        }

                        // Sort batches by the timestamp of their first measurement (newest first)
                        batches.sort((a, b) => {
                          const aTime = new Date(a[0].createdAt).getTime()
                          const bTime = new Date(b[0].createdAt).getTime()
                          return bTime - aTime
                        })

                        // We no longer create artificial batches
                        // This ensures that only real batches (with the same batch ID) are shown as batches

                        // Render all measurements with collapsible batches
                        // In Bayesian optimization, iteration refers to the optimization loop, not individual measurements
                        // We'll use the batch index as the iteration number (newest first)
                        // For batch items, we use a consistent format: ExperimentNumber.ItemNumber (e.g., 3.1, 3.2, 3.3)
                        let currentIteration = batches.length
                        const rows: React.ReactElement[] = []

                        // Create a unique ID for each batch based on its content
                        const getBatchId = (
                          batch: typeof measurements,
                          index: number
                        ) => {
                          // Create a unique ID based on the first measurement's ID and the batch index
                          return `batch-${batch[0]?.id || index}-${index}`
                        }

                        // Determine if a batch is from the API (suggested points) or manual entry
                        const isBatchFromAPI = (batch: typeof measurements) => {
                          return batch.every(m => m.isRecommended)
                        }

                        // Determine if a batch is a true batch (has multiple measurements) or sequential
                        const isTrueBatch = (batch: typeof measurements) => {
                          // For a batch to be considered a "true batch", it must have multiple measurements
                          // This aligns with the requirement that batch suggestions are only when multiple suggestions are generated

                          // Check if the batch has more than one measurement
                          const hasMultipleMeasurements = batch.length > 1

                          // Log for debugging
                          console.log(
                            `Batch check: batchSize=${batch.length}, hasMultipleMeasurements=${hasMultipleMeasurements}, batchIds=${JSON.stringify(batch.map(m => m.batchId))}`
                          )

                          // Only return true if there are multiple measurements
                          return hasMultipleMeasurements
                        }

                        // Debug output
                        console.log(
                          "Batches detected:",
                          batches.map(batch => {
                            // Get all batch IDs (excluding null/undefined)
                            const batchIds = batch
                              .map(m => m.batchId)
                              .filter(id => id !== null && id !== undefined)

                            // Count occurrences of each batch ID
                            const batchIdCounts: Record<string, number> = {}
                            batchIds.forEach(id => {
                              if (id) {
                                batchIdCounts[id] = (batchIdCounts[id] || 0) + 1
                              }
                            })

                            return {
                              size: batch.length,
                              batchIds: batch.map(m => m.batchId),
                              batchIdCounts,
                              isTrueBatch: isTrueBatch(batch),
                              timestamps: batch.map(m =>
                                formatDate(new Date(m.createdAt))
                              ),
                              ids: batch.map(m => m.id)
                            }
                          })
                        )

                        // Collect all batch IDs for expand/collapse all functionality
                        const allBatchIds: string[] = []
                        batches.forEach((batch, batchIndex) => {
                          if (batch.length > 1) {
                            const batchId = getBatchId(batch, batchIndex)
                            allBatchIds.push(batchId)
                          }
                        })

                        // Update the batchIds state if it has changed
                        if (
                          JSON.stringify(allBatchIds) !==
                          JSON.stringify(batchIds)
                        ) {
                          setBatchIds(allBatchIds)
                        }

                        batches.forEach((batch, batchIndex) => {
                          const isBatch = batch.length > 1
                          const batchId = getBatchId(batch, batchIndex)
                          const isExpanded = expandedBatches[batchId] || false

                          if (isBatch) {
                            // For batches, render a collapsible header
                            const firstMeasurement = batch[0]
                            const lastIteration = currentIteration
                            const firstIteration =
                              currentIteration - batch.length + 1

                            // Calculate average target value for the primary target
                            let avgTargetValue = 0
                            if (isMultiTarget) {
                              // For multi-target, use the targetValues field if available
                              const validValues = batch
                                .map(m => {
                                  if (
                                    m.targetValues &&
                                    typeof m.targetValues === "object" &&
                                    (m.targetValues as Record<string, number>)[
                                      optimization.targetName
                                    ] !== undefined
                                  ) {
                                    return (
                                      m.targetValues as Record<string, number>
                                    )[optimization.targetName]
                                  }
                                  return parseFloat(m.targetValue)
                                })
                                .filter(v => !isNaN(v))

                              avgTargetValue =
                                validValues.length > 0
                                  ? validValues.reduce((sum, v) => sum + v, 0) /
                                    validValues.length
                                  : 0
                            } else {
                              // For single target, use the targetValue field
                              avgTargetValue =
                                batch.reduce(
                                  (sum, m) => sum + parseFloat(m.targetValue),
                                  0
                                ) / batch.length
                            }

                            // Calculate statistics for other targets
                            const targetStats: Record<
                              string,
                              { avg: number; min: number; max: number }
                            > = {}

                            if (isMultiTarget && otherTargets.length > 0) {
                              otherTargets.forEach(targetName => {
                                // Get all values for this target from the batch
                                const values = batch
                                  .map(m => {
                                    if (
                                      m.targetValues &&
                                      typeof m.targetValues === "object"
                                    ) {
                                      const targetValues =
                                        m.targetValues as Record<string, number>
                                      return targetValues[targetName]
                                    }
                                    return NaN
                                  })
                                  .filter(v => !isNaN(v))

                                if (values.length > 0) {
                                  targetStats[targetName] = {
                                    avg:
                                      values.reduce((sum, v) => sum + v, 0) /
                                      values.length,
                                    min: Math.min(...values),
                                    max: Math.max(...values)
                                  }
                                }
                              })
                            }

                            // Determine if this is an API batch or manual batch
                            const isAPIBatch = isBatchFromAPI(batch)

                            // Batch header row
                            rows.push(
                              <TableRow
                                key={`header-${batchId}`}
                                className={`hover:bg-muted/50 cursor-pointer border-t-2 ${isAPIBatch ? "border-primary/20" : "border-orange-300/30"}`}
                                onClick={() => {
                                  // Update the expanded state for this batch
                                  const newExpandedState =
                                    !expandedBatches[batchId]
                                  setExpandedBatches(prev => ({
                                    ...prev,
                                    [batchId]: newExpandedState
                                  }))

                                  // Check if all batches are now expanded or collapsed
                                  // and update the allExpanded state accordingly
                                  if (batchIds.length > 0) {
                                    const expandedCount = Object.values({
                                      ...expandedBatches,
                                      [batchId]: newExpandedState
                                    }).filter(Boolean).length

                                    // If all batches are expanded, set allExpanded to true
                                    // If no batches are expanded, set allExpanded to false
                                    if (expandedCount === batchIds.length) {
                                      setAllExpanded(true)
                                    } else if (expandedCount === 0) {
                                      setAllExpanded(false)
                                    }
                                  }
                                }}
                              >
                                <TableCell className="bg-background sticky left-0 z-10 font-medium">
                                  <div className="flex items-center">
                                    <ChevronRight
                                      className={`mr-1 size-4 transition-transform ${isExpanded ? "rotate-90" : ""}`}
                                    />
                                    <span>Experiment {currentIteration}</span>
                                  </div>
                                </TableCell>
                                <TableCell className="bg-background sticky left-[100px] z-10">
                                  <div className="flex flex-col">
                                    <span>
                                      {formatDate(
                                        new Date(firstMeasurement.createdAt)
                                      )}
                                    </span>
                                    <span className="text-muted-foreground text-xs">
                                      {isExpanded
                                        ? "Click to collapse"
                                        : "Click to expand"}
                                    </span>
                                  </div>
                                </TableCell>
                                {/* First target summary */}
                                <TableCell className="text-right font-medium">
                                  <div className="flex flex-col">
                                    <span>
                                      Avg: {formatNumber(avgTargetValue)}
                                    </span>
                                    <span className="text-muted-foreground text-xs">
                                      Min:{" "}
                                      {formatNumber(
                                        Math.min(
                                          ...batch.map(m => {
                                            if (
                                              isMultiTarget &&
                                              m.targetValues &&
                                              typeof m.targetValues ===
                                                "object" &&
                                              (
                                                m.targetValues as Record<
                                                  string,
                                                  number
                                                >
                                              )[optimization.targetName] !==
                                                undefined
                                            ) {
                                              return (
                                                m.targetValues as Record<
                                                  string,
                                                  number
                                                >
                                              )[optimization.targetName]
                                            }
                                            return parseFloat(m.targetValue)
                                          })
                                        )
                                      )}
                                      {" | "}
                                      Max:{" "}
                                      {formatNumber(
                                        Math.max(
                                          ...batch.map(m => {
                                            if (
                                              isMultiTarget &&
                                              m.targetValues &&
                                              typeof m.targetValues ===
                                                "object" &&
                                              (
                                                m.targetValues as Record<
                                                  string,
                                                  number
                                                >
                                              )[optimization.targetName] !==
                                                undefined
                                            ) {
                                              return (
                                                m.targetValues as Record<
                                                  string,
                                                  number
                                                >
                                              )[optimization.targetName]
                                            }
                                            return parseFloat(m.targetValue)
                                          })
                                        )
                                      )}
                                    </span>
                                  </div>
                                </TableCell>

                                {/* Other target statistics */}
                                {isMultiTarget &&
                                  otherTargets.map(targetName => (
                                    <TableCell
                                      key={targetName}
                                      className="text-right font-medium"
                                    >
                                      {targetStats[targetName] ? (
                                        <div className="flex flex-col">
                                          <span>
                                            Avg:{" "}
                                            {formatNumber(
                                              targetStats[targetName].avg
                                            )}
                                          </span>
                                          <span className="text-muted-foreground text-xs">
                                            Min:{" "}
                                            {formatNumber(
                                              targetStats[targetName].min
                                            )}
                                            {" | "}
                                            Max:{" "}
                                            {formatNumber(
                                              targetStats[targetName].max
                                            )}
                                          </span>
                                        </div>
                                      ) : (
                                        <span className="text-muted-foreground">
                                          No data
                                        </span>
                                      )}
                                    </TableCell>
                                  ))}

                                {/* Placeholder cells for all parameters */}
                                {parameterNames.map((name: string) => (
                                  <TableCell key={name}></TableCell>
                                ))}

                                <TableCell className="bg-background sticky right-[120px] z-10 text-right">
                                  <Badge
                                    variant="outline"
                                    className={
                                      isAPIBatch
                                        ? "bg-primary/10 text-primary border-primary/20"
                                        : "border-orange-200 bg-orange-100 text-orange-700"
                                    }
                                  >
                                    {isAPIBatch ? "API" : "Manual Entry"}
                                  </Badge>
                                </TableCell>
                                <TableCell className="bg-background sticky right-0 z-10 text-right">
                                  <Badge
                                    variant="outline"
                                    className={
                                      firstMeasurement.parameters &&
                                      typeof firstMeasurement.parameters ===
                                        "object" &&
                                      (firstMeasurement.parameters as any)
                                        ?._sampleClass === "exploratory"
                                        ? "border-purple-200 bg-purple-100 text-purple-800"
                                        : isTrueBatch(batch)
                                          ? "border-green-200 bg-green-100 text-green-700"
                                          : "border-blue-200 bg-blue-100 text-blue-700"
                                    }
                                  >
                                    {firstMeasurement.parameters &&
                                    typeof firstMeasurement.parameters ===
                                      "object" &&
                                    (firstMeasurement.parameters as any)
                                      ?._sampleClass === "exploratory"
                                      ? "Exploratory"
                                      : isTrueBatch(batch)
                                        ? `Batch${batch.length > 1 ? ` (${batch.length})` : ""}`
                                        : "Sequential"}
                                  </Badge>
                                </TableCell>
                              </TableRow>
                            )

                            // If expanded, show all measurements in the batch
                            if (isExpanded) {
                              batch.forEach((measurement, measurementIndex) => {
                                // Use consistent numbering: currentIteration.1, currentIteration.2, etc.
                                // This ensures all items in a batch have the same experiment number prefix

                                rows.push(
                                  <TableRow
                                    key={`${batchId}-item-${measurement.id}`}
                                    className="bg-muted/30"
                                  >
                                    <TableCell className="bg-muted/30 sticky left-0 z-10 pl-6 font-medium">
                                      <span className="flex items-center">
                                        <span className="text-muted-foreground mr-1">
                                          └
                                        </span>
                                        <span>
                                          {currentIteration}.
                                          {measurementIndex + 1}
                                        </span>
                                      </span>
                                    </TableCell>
                                    <TableCell className="bg-muted/30 sticky left-[100px] z-10">
                                      {formatDate(
                                        new Date(measurement.createdAt)
                                      )}
                                    </TableCell>
                                    {/* First target value */}
                                    <TableCell className="text-right font-medium">
                                      {isMultiTarget &&
                                      measurement.targetValues &&
                                      typeof measurement.targetValues ===
                                        "object" &&
                                      (
                                        measurement.targetValues as Record<
                                          string,
                                          number
                                        >
                                      )[optimization.targetName] !== undefined
                                        ? formatNumber(
                                            (
                                              measurement.targetValues as Record<
                                                string,
                                                number
                                              >
                                            )[optimization.targetName]
                                          )
                                        : formatNumber(
                                            parseFloat(measurement.targetValue)
                                          )}
                                    </TableCell>

                                    {/* Other target values for multi-target optimization */}
                                    {isMultiTarget &&
                                      otherTargets.map(targetName => {
                                        // Get the target value from targetValues if available
                                        let value = "N/A"
                                        if (measurement.targetValues) {
                                          const targetValues =
                                            measurement.targetValues as Record<
                                              string,
                                              number
                                            >
                                          if (
                                            targetValues[targetName] !==
                                            undefined
                                          ) {
                                            const numValue =
                                              targetValues[targetName]
                                            value =
                                              typeof numValue === "number"
                                                ? formatNumber(numValue)
                                                : String(numValue)
                                          }
                                        }
                                        return (
                                          <TableCell
                                            key={targetName}
                                            className="text-right font-medium"
                                          >
                                            {value}
                                          </TableCell>
                                        )
                                      })}

                                    {/* All parameter values */}
                                    {parameterNames.map((name: string) => {
                                      const params =
                                        measurement.parameters as Record<
                                          string,
                                          any
                                        >
                                      return (
                                        <TableCell key={name}>
                                          {typeof params[name] === "number"
                                            ? formatNumber(params[name])
                                            : String(params[name])}
                                        </TableCell>
                                      )
                                    })}

                                    <TableCell className="bg-muted/30 sticky right-[120px] z-10 text-right">
                                      <Badge
                                        variant={
                                          measurement.isRecommended
                                            ? "default"
                                            : "outline"
                                        }
                                        className="opacity-75"
                                      >
                                        {measurement.isRecommended
                                          ? "API"
                                          : "Manual"}
                                      </Badge>
                                    </TableCell>
                                    <TableCell className="bg-muted/30 sticky right-0 z-10 text-right">
                                      <Badge
                                        variant="outline"
                                        className={
                                          measurement.parameters &&
                                          typeof measurement.parameters ===
                                            "object" &&
                                          (measurement.parameters as any)
                                            ?._sampleClass === "exploratory"
                                            ? "border-purple-200 bg-purple-100 text-purple-800 opacity-75"
                                            : "border-green-200 bg-green-100 text-green-700 opacity-75"
                                        }
                                      >
                                        {measurement.parameters &&
                                        typeof measurement.parameters ===
                                          "object" &&
                                        (measurement.parameters as any)
                                          ?._sampleClass === "exploratory"
                                          ? "Exploratory"
                                          : "Batch Item"}
                                      </Badge>
                                    </TableCell>
                                  </TableRow>
                                )
                              })
                            }

                            // Update the iteration counter
                            currentIteration--
                          } else {
                            // For single measurements, treat them as their own iteration
                            const measurement = batch[0]
                            const isAPIEntry = measurement.isRecommended

                            rows.push(
                              <TableRow
                                key={measurement.id}
                                className={
                                  isAPIEntry
                                    ? ""
                                    : "border-t-2 border-orange-300/30"
                                }
                              >
                                <TableCell className="bg-background sticky left-0 z-10 font-medium">
                                  Experiment {currentIteration}
                                </TableCell>
                                <TableCell className="bg-background sticky left-[100px] z-10">
                                  {formatDate(new Date(measurement.createdAt))}
                                </TableCell>
                                {/* First target value */}
                                <TableCell className="text-right font-medium">
                                  {isMultiTarget &&
                                  measurement.targetValues &&
                                  typeof measurement.targetValues ===
                                    "object" &&
                                  (
                                    measurement.targetValues as Record<
                                      string,
                                      number
                                    >
                                  )[optimization.targetName] !== undefined
                                    ? formatNumber(
                                        (
                                          measurement.targetValues as Record<
                                            string,
                                            number
                                          >
                                        )[optimization.targetName]
                                      )
                                    : formatNumber(
                                        parseFloat(measurement.targetValue)
                                      )}
                                </TableCell>

                                {/* Other target values for multi-target optimization */}
                                {isMultiTarget &&
                                  otherTargets.map(targetName => {
                                    // Get the target value from targetValues if available
                                    let value = "N/A"
                                    if (measurement.targetValues) {
                                      const targetValues =
                                        measurement.targetValues as Record<
                                          string,
                                          number
                                        >
                                      if (
                                        targetValues[targetName] !== undefined
                                      ) {
                                        const numValue =
                                          targetValues[targetName]
                                        value =
                                          typeof numValue === "number"
                                            ? formatNumber(numValue)
                                            : String(numValue)
                                      }
                                    }
                                    return (
                                      <TableCell
                                        key={targetName}
                                        className="text-right font-medium"
                                      >
                                        {value}
                                      </TableCell>
                                    )
                                  })}

                                {/* All parameter values */}
                                {parameterNames.map((name: string) => {
                                  const params =
                                    measurement.parameters as Record<
                                      string,
                                      any
                                    >
                                  return (
                                    <TableCell key={name}>
                                      {typeof params[name] === "number"
                                        ? formatNumber(params[name])
                                        : String(params[name])}
                                    </TableCell>
                                  )
                                })}

                                <TableCell className="bg-background sticky right-[120px] z-10 text-right">
                                  <Badge
                                    variant="outline"
                                    className={
                                      isAPIEntry
                                        ? "bg-primary/10 text-primary border-primary/20"
                                        : "border-orange-200 bg-orange-100 text-orange-700"
                                    }
                                  >
                                    {isAPIEntry ? "API" : "Manual Entry"}
                                  </Badge>
                                </TableCell>
                                <TableCell className="bg-background sticky right-0 z-10 text-right">
                                  <Badge
                                    variant="outline"
                                    className={
                                      measurement.parameters &&
                                      typeof measurement.parameters ===
                                        "object" &&
                                      (measurement.parameters as any)
                                        ?._sampleClass === "exploratory"
                                        ? "border-purple-200 bg-purple-100 text-purple-800"
                                        : "border-blue-200 bg-blue-100 text-blue-700"
                                    }
                                  >
                                    {measurement.parameters &&
                                    typeof measurement.parameters ===
                                      "object" &&
                                    (measurement.parameters as any)
                                      ?._sampleClass === "exploratory"
                                      ? "Exploratory"
                                      : "Sequential"}
                                  </Badge>
                                </TableCell>
                              </TableRow>
                            )

                            // Update the iteration counter for single measurements
                            currentIteration--
                          }
                        })

                        console.log("Rows to render:", rows.length)
                        return rows
                      })()}
                    </TableBody>
                  </Table>
                </div>
              </div>
            ) : (
              <div className="text-muted-foreground py-8 text-center">
                {isLoadingHistory ? (
                  <div className="flex flex-col items-center">
                    <RefreshCw className="mb-2 size-8 animate-spin" />
                    <p>Loading measurement history...</p>
                  </div>
                ) : (
                  <p>No measurement history available yet.</p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="parameters" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Beaker className="mr-2 size-5" />
              Parameter Configuration
            </CardTitle>
            <CardDescription>
              Parameters being optimized in this experiment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[180px]">Name</TableHead>
                      <TableHead className="w-[120px]">Type</TableHead>
                      <TableHead>Values/Range</TableHead>
                      <TableHead className="w-[120px] text-right">
                        Options
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {(optimization.config as any).parameters.map(
                      (param: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">
                            {param.name}
                          </TableCell>
                          <TableCell>{param.type}</TableCell>
                          <TableCell>
                            {param.type === "NumericalDiscrete" &&
                              Array.isArray(param.values) &&
                              param.values.join(", ")}
                            {param.type === "NumericalContinuous" &&
                              param.bounds &&
                              `${param.bounds[0]} to ${param.bounds[1]}`}
                            {param.type === "CategoricalParameter" &&
                              Array.isArray(param.values) &&
                              param.values.join(", ")}
                          </TableCell>
                          <TableCell className="text-right">
                            {param.type === "NumericalDiscrete" &&
                              param.tolerance &&
                              `Tolerance: ${param.tolerance}`}
                            {param.type === "CategoricalParameter" &&
                              param.encoding &&
                              `Encoding: ${param.encoding}`}
                          </TableCell>
                        </TableRow>
                      )
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Target Configuration */}
              <div className="space-y-2">
                <h3 className="flex items-center text-lg font-medium">
                  <Target className="mr-2 size-5" />
                  Target Configuration
                </h3>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[180px]">Name</TableHead>
                        <TableHead className="w-[120px]">Mode</TableHead>
                        <TableHead>Bounds</TableHead>
                        <TableHead className="w-[120px] text-right">
                          Weight
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {/* For multi-target optimization */}
                      {isMultiTarget ? (
                        // Map through all targets in the target_config array
                        Array.isArray(
                          (optimization.config as any).target_config
                        ) &&
                        (optimization.config as any).target_config.map(
                          (target: any, index: number) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">
                                {target.name}
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  {target.mode}
                                  {target.mode === "MAX" ||
                                  (typeof target.mode === "string" &&
                                    target.mode
                                      .toUpperCase()
                                      .includes("MAX")) ? (
                                    <ArrowUp className="ml-1 size-4 text-green-500" />
                                  ) : (
                                    <ArrowDown className="ml-1 size-4 text-green-500" />
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                {target.bounds ? (
                                  Array.isArray(target.bounds) ? (
                                    `${target.bounds[0] !== undefined ? target.bounds[0] : "None"} to ${target.bounds[1] !== undefined ? target.bounds[1] : "None"}`
                                  ) : (
                                    `${target.bounds.lower !== undefined ? target.bounds.lower : "None"} to ${target.bounds.upper !== undefined ? target.bounds.upper : "None"}`
                                  )
                                ) : (
                                  <span className="text-muted-foreground">
                                    None
                                  </span>
                                )}
                              </TableCell>
                              <TableCell className="text-right">
                                {target.weight !== undefined
                                  ? `${(target.weight * 100).toFixed(0)}%`
                                  : "100%"}
                              </TableCell>
                            </TableRow>
                          )
                        )
                      ) : (
                        // For single-target optimization
                        <TableRow>
                          <TableCell className="font-medium">
                            {optimization.targetName}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              {optimization.targetMode}
                              {optimization.targetMode === "MAX" ? (
                                <ArrowUp className="ml-1 size-4 text-green-500" />
                              ) : (
                                <ArrowDown className="ml-1 size-4 text-green-500" />
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {(optimization.config as any).target_config
                              .bounds ? (
                              `${
                                (optimization.config as any).target_config
                                  .bounds.lower !== undefined
                                  ? (optimization.config as any).target_config
                                      .bounds.lower
                                  : "None"
                              } to ${
                                (optimization.config as any).target_config
                                  .bounds.upper !== undefined
                                  ? (optimization.config as any).target_config
                                      .bounds.upper
                                  : "None"
                              }`
                            ) : (
                              <span className="text-muted-foreground">
                                None
                              </span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            {(optimization.config as any).target_config
                              .weight !== undefined
                              ? `${(
                                  (optimization.config as any).target_config
                                    .weight * 100
                                ).toFixed(0)}%`
                              : "100%"}
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* Constraints */}
              {(optimization.config as any).constraints &&
                (optimization.config as any).constraints.length > 0 && (
                  <div className="space-y-2">
                    <h3 className="flex items-center text-lg font-medium">
                      <Check className="mr-2 size-5" />
                      Constraints
                    </h3>
                    <div className="rounded-md border p-4">
                      <pre className="bg-muted overflow-auto rounded p-2 text-xs">
                        {JSON.stringify(
                          (optimization.config as any).constraints,
                          null,
                          2
                        )}
                      </pre>
                    </div>
                  </div>
                )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="analysis" className="space-y-6">
        <AnalysisTab
          optimization={optimization}
          measurements={measurements}
          parameterNames={parameterNames}
          isMultiTarget={isMultiTarget}
          otherTargets={otherTargets}
          initialSection={activeAnalysisSection}
        />
      </TabsContent>

      <TabsContent value="status" className="space-y-6">
        <OptimizationStatusHistory
          optimizationId={optimization.id}
          optimizationName={optimization.name}
        />
      </TabsContent>
    </Tabs>
  )
}
