"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { GitBranch, LineChart, Network } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { TradeOffExplorer } from "./trade-off-explorer"
import { ParallelCoordinates } from "./parallel-coordinates"
import { TargetCorrelations } from "./target-correlations"

interface MultiTargetAnalysisProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  isMultiTarget: boolean
  targetName: string
  otherTargets: string[]
}

export function MultiTargetAnalysis({
  optimization,
  measurements,
  isMultiTarget,
  targetName,
  otherTargets
}: MultiTargetAnalysisProps) {
  const [activeTab, setActiveTab] = useState("tradeoff")
  const [selectedTarget, setSelectedTarget] = useState<string | undefined>(
    undefined
  )

  // Get all target names
  const allTargetNames = isMultiTarget
    ? [targetName, ...otherTargets]
    : [targetName]

  // Handle target selection from any visualization
  const handleTargetSelect = (target: string) => {
    console.log("Selected target:", target)
    setSelectedTarget(target)
  }

  // If not a multi-target optimization, show a message
  if (!isMultiTarget) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <GitBranch className="text-primary mr-2 size-5" />
            Multi-Target Analysis
          </CardTitle>
          <CardDescription>
            Understand trade-offs between multiple objectives
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <GitBranch className="size-4" />
            <AlertTitle>Single-Target Optimization</AlertTitle>
            <AlertDescription>
              This optimization has only one target variable ({targetName}).
              Multi-target analysis is available for optimizations with multiple
              objectives.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <GitBranch className="text-primary mr-2 size-5" />
          Multi-Target Analysis
        </CardTitle>
        <CardDescription>
          Understand trade-offs between multiple objectives
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4 grid grid-cols-3">
            <TabsTrigger value="tradeoff">Trade-off Explorer</TabsTrigger>
            <TabsTrigger value="parallel">Parallel Coordinates</TabsTrigger>
            <TabsTrigger value="correlation">Target Correlations</TabsTrigger>
          </TabsList>

          {/* Trade-off Explorer */}
          <TabsContent value="tradeoff" className="space-y-4">
            <TradeOffExplorer
              optimization={optimization}
              measurements={measurements}
              targetNames={allTargetNames}
              onTargetSelect={handleTargetSelect}
              selectedTarget={selectedTarget}
            />
          </TabsContent>

          {/* Parallel Coordinates */}
          <TabsContent value="parallel" className="space-y-4">
            <ParallelCoordinates
              optimization={optimization}
              measurements={measurements}
              parameterNames={getParameterNames(optimization)}
              targetNames={allTargetNames}
              onTargetSelect={handleTargetSelect}
              selectedTarget={selectedTarget}
            />
          </TabsContent>

          {/* Target Correlations */}
          <TabsContent value="correlation" className="space-y-4">
            <TargetCorrelations
              optimization={optimization}
              measurements={measurements}
              targetNames={allTargetNames}
              onTargetSelect={handleTargetSelect}
              selectedTarget={selectedTarget}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

// Helper function to extract parameter names from optimization config
function getParameterNames(optimization: SelectOptimization): string[] {
  if (!optimization.config) return []

  try {
    const config = optimization.config as any
    if (config.parameters && Array.isArray(config.parameters)) {
      return config.parameters.map((param: any) => param.name)
    }
  } catch (error) {
    console.error("Error extracting parameter names:", error)
  }

  return []
}
