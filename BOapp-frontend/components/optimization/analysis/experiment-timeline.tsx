"use client"

import { useState } from "react"
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { Clock, TrendingUp, Workflow, BarChart } from "lucide-react"
import { ProgressVisualization } from "./experiment-timeline/progress-visualization"
import { StrategyEvolution } from "./experiment-timeline/strategy-evolution"

interface ExperimentTimelineProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
}

export function ExperimentTimeline({
  optimization,
  measurements
}: ExperimentTimelineProps) {
  const [activeTab, setActiveTab] = useState("progress")
  const [selectedExperiment, setSelectedExperiment] = useState<
    number | undefined
  >(undefined)

  // Get all target names
  const targetNames = getTargetNames(optimization, measurements)

  // Handle experiment selection
  const handleExperimentSelect = (experimentIndex: number) => {
    console.log("Selected experiment:", experimentIndex)
    setSelectedExperiment(experimentIndex)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Clock className="text-primary mr-2 size-5" />
          Experiment Timeline
        </CardTitle>
        <CardDescription>
          Visualize the optimization journey over time
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4 grid grid-cols-3">
            <TabsTrigger value="progress">Progress Visualization</TabsTrigger>
            <TabsTrigger value="strategy">Strategy Evolution</TabsTrigger>
            <TabsTrigger value="learning">Learning Rate</TabsTrigger>
          </TabsList>

          {/* Progress Visualization */}
          <TabsContent value="progress" className="space-y-4">
            <ProgressVisualization
              optimization={optimization}
              measurements={measurements}
              targetNames={targetNames}
              onExperimentSelect={handleExperimentSelect}
              selectedExperiment={selectedExperiment}
            />
          </TabsContent>

          {/* Strategy Evolution */}
          <TabsContent value="strategy" className="space-y-4">
            <StrategyEvolution
              optimization={optimization}
              measurements={measurements}
              onExperimentSelect={handleExperimentSelect}
              selectedExperiment={selectedExperiment}
            />
          </TabsContent>

          {/* Learning Rate */}
          <TabsContent value="learning" className="space-y-4">
            <div className="bg-muted/50 flex h-[400px] w-full items-center justify-center rounded-md border">
              <div className="p-6 text-center">
                <BarChart className="text-muted-foreground mx-auto mb-4 size-12" />
                <h3 className="text-lg font-medium">Learning Rate</h3>
                <p className="text-muted-foreground mt-2 max-w-md text-sm">
                  This visualization will analyze how quickly the optimization
                  is converging, helping you understand the efficiency of the
                  learning process.
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

// Helper function to extract target names from optimization and measurements
function getTargetNames(
  optimization: SelectOptimization,
  measurements: SelectMeasurement[]
): string[] {
  const targetNames: string[] = []

  // Add primary target
  if (optimization.targetName) {
    targetNames.push(optimization.targetName)
  }

  // Add additional targets from multi-target measurements
  if (measurements.length > 0 && measurements[0].targetValues) {
    const firstMeasurement = measurements[0]
    const targetValuesObj = firstMeasurement.targetValues as Record<
      string,
      number
    >

    Object.keys(targetValuesObj).forEach(target => {
      if (!targetNames.includes(target)) {
        targetNames.push(target)
      }
    })
  }

  // Check if target configuration is available in optimization config
  if (optimization.config && typeof optimization.config === "object") {
    const config = optimization.config as any
    if (config.target_config && Array.isArray(config.target_config)) {
      config.target_config.forEach((targetConfig: any) => {
        if (targetConfig.name && !targetNames.includes(targetConfig.name)) {
          targetNames.push(targetConfig.name)
        }
      })
    }
  }

  return targetNames
}
