"use client"

import { useState } from "react"
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { BarChart3, Grid3X3, Line<PERSON>hart } from "lucide-react"
import { ParameterImportance } from "./parameter-importance"
import { CorrelationMatrix } from "./correlation-matrix"
import { ParameterDistributions } from "./parameter-distributions"

interface ParameterAnalysisProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  parameterNames: string[]
}

export function ParameterAnalysis({
  optimization,
  measurements,
  parameterNames
}: ParameterAnalysisProps) {
  const [activeTab, setActiveTab] = useState("importance")
  const [selectedParameter, setSelectedParameter] = useState<
    string | undefined
  >(undefined)

  // Handle parameter selection from any visualization
  const handleParameterSelect = (parameter: string) => {
    console.log("Selected parameter:", parameter)
    setSelectedParameter(parameter)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <BarChart3 className="text-primary mr-2 size-5" />
          Parameter Analysis
        </CardTitle>
        <CardDescription>
          Understand parameter relationships and importance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4 grid grid-cols-3">
            <TabsTrigger value="importance">Parameter Importance</TabsTrigger>
            <TabsTrigger value="correlation">Correlation Matrix</TabsTrigger>
            <TabsTrigger value="distribution">
              Parameter Distributions
            </TabsTrigger>
          </TabsList>

          {/* Parameter Importance */}
          <TabsContent value="importance" className="space-y-4">
            <ParameterImportance optimization={optimization} topN={10} />
          </TabsContent>

          {/* Correlation Matrix */}
          <TabsContent value="correlation" className="space-y-4">
            <CorrelationMatrix
              optimization={optimization}
              measurements={measurements}
              parameterNames={parameterNames}
              onParameterSelect={handleParameterSelect}
              selectedParameter={selectedParameter}
            />
          </TabsContent>

          {/* Parameter Distributions */}
          <TabsContent value="distribution" className="space-y-4">
            <ParameterDistributions
              optimization={optimization}
              measurements={measurements}
              parameterNames={parameterNames}
              onParameterSelect={handleParameterSelect}
              selectedParameter={selectedParameter}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
