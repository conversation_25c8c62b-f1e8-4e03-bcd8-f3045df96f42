"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { SurfaceAnalysis } from "@/components/optimization/surface-analysis"

interface ResponseSurfaceAnalysisProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  parameterNames: string[]
  targetName: string
  targetMode: "MAX" | "MIN" | "MULTI"
  availableTargets: string[]
}

export function ResponseSurfaceAnalysis({
  optimization,
  measurements,
  parameterNames,
  targetName,
  targetMode,
  availableTargets
}: ResponseSurfaceAnalysisProps) {
  // Default to surface tab since we moved SOBOL to main tabs
  const [activeTab, setActiveTab] = useState("surface")

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Layers className="text-primary mr-2 size-5" />
          Response Surface Analysis
        </CardTitle>
        <CardDescription>
          Visualize how parameters affect target variables
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4 grid grid-cols-2">
            <TabsTrigger value="surface">3D Surface</TabsTrigger>
            <TabsTrigger value="cross-section">Cross-Sections</TabsTrigger>
          </TabsList>

          {/* 3D Surface Plot */}
          <TabsContent value="surface" className="space-y-4">
            <SurfaceAnalysis
              measurements={measurements}
              parameterNames={parameterNames}
              targetName={targetName}
              targetMode={targetMode}
              availableTargets={availableTargets}
            />
          </TabsContent>

          {/* Cross-Section Plot */}
          <TabsContent value="cross-section" className="space-y-4">
            <div className="bg-muted/50 flex h-[500px] w-full items-center justify-center rounded-md border">
              <div className="p-6 text-center">
                <LineChart className="text-muted-foreground mx-auto mb-4 size-12" />
                <h3 className="text-lg font-medium">Cross-Section Plot</h3>
                <p className="text-muted-foreground mt-2 max-w-md text-sm">
                  This visualization will show cross-sections through the
                  response surface, allowing detailed analysis of parameter
                  effects at specific values.
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
