"use client"

import { useState, use<PERSON>ffect, use<PERSON>em<PERSON>, use<PERSON><PERSON>back } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Workflow, RefreshCw, AlertCircle, Maximize2, Info } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { StrategyEvolutionDocumentation } from "@/components/documentation/strategy-evolution-docs"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import dynamic from "next/dynamic"

// Import Plotly dynamically to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

// Types
interface StrategyMetrics {
  parameter_diversity: Record<string, number>
  improvement_rates: Record<string, number>
  ucb_components: {
    experiments: Array<{
      experiment_index: number
      mean: number
      std: number
      exploration: number
      exploitation: number
      balance: number
      ucb_value: number
    }>
  }
  exploration_score: number
  exploitation_score: number
  normalized_exploration: number
  normalized_exploitation: number
  balance: number
  dominant_strategy: string
  strategy_shifts: Array<{
    experiment_index: number
    previous_balance: number
    current_balance: number
    shift_magnitude: number
  }>
}

interface StrategyEvolutionChartProps {
  strategyMetrics: StrategyMetrics
  showStrategyShifts: boolean
  onExperimentSelect?: (experimentIndex: number) => void
  selectedExperiment?: number
  height?: number
}

interface StrategyEvolutionProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  onExperimentSelect?: (experimentIndex: number) => void
  selectedExperiment?: number
}

// Strategy Evolution Chart Component
function StrategyEvolutionChart({
  strategyMetrics,
  showStrategyShifts,
  onExperimentSelect,
  selectedExperiment,
  height = 400
}: StrategyEvolutionChartProps) {
  // Generate plot data
  const plotData = useMemo(() => {
    if (
      !strategyMetrics ||
      !strategyMetrics.ucb_components ||
      !strategyMetrics.ucb_components.experiments
    ) {
      return []
    }

    const experiments = strategyMetrics.ucb_components.experiments
    // Use the actual experiment numbers for display
    const experimentIndices = experiments.map(exp => exp.experiment_index + 1) // Keep using 1-based indices for now

    // Create traces for exploration and exploitation
    const traces = [
      // Exploration area
      {
        x: experimentIndices,
        y: experiments.map(exp => exp.exploration),
        name: "Exploration",
        type: "scatter" as const,
        mode: "none" as const,
        fill: "tozeroy" as const,
        fillcolor: "rgba(31, 119, 180, 0.5)",
        line: {
          color: "rgba(31, 119, 180, 0.8)",
          width: 0
        },
        hoverinfo: "text" as const,
        text: experiments.map(
          exp =>
            `Experiment ${exp.experiment_index + 1}<br>Exploration: ${exp.exploration.toFixed(4)}`
        )
      },
      // Exploitation area
      {
        x: experimentIndices,
        y: experiments.map(exp => exp.exploitation),
        name: "Exploitation",
        type: "scatter" as const,
        mode: "none" as const,
        fill: "tozeroy" as const,
        fillcolor: "rgba(255, 127, 14, 0.5)",
        line: {
          color: "rgba(255, 127, 14, 0.8)",
          width: 0
        },
        hoverinfo: "text" as const,
        text: experiments.map(
          exp =>
            `Experiment ${exp.experiment_index + 1}<br>Exploitation: ${exp.exploitation.toFixed(4)}`
        )
      },
      // Balance line
      {
        x: experimentIndices,
        y: experiments.map(exp => exp.balance),
        name: "Balance",
        type: "scatter" as const,
        mode: "lines+markers" as const,
        line: {
          color: "rgba(44, 160, 44, 1)",
          width: 2
        },
        marker: {
          size: 6,
          color: experiments.map(exp => {
            // Color based on balance value
            if (exp.balance > 0.2) return "rgba(31, 119, 180, 1)" // Blue for exploration
            if (exp.balance < -0.2) return "rgba(255, 127, 14, 1)" // Orange for exploitation
            return "rgba(44, 160, 44, 1)" // Green for balanced
          })
        },
        hoverinfo: "text" as const,
        text: experiments.map(exp => {
          let strategy = "Balanced"
          if (exp.balance > 0.2) strategy = "Exploration"
          if (exp.balance < -0.2) strategy = "Exploitation"

          return `Experiment ${exp.experiment_index + 1}<br>Balance: ${exp.balance.toFixed(4)}<br>Strategy: ${strategy}`
        })
      }
    ]

    // Add strategy shift markers if enabled
    if (
      showStrategyShifts &&
      strategyMetrics.strategy_shifts &&
      strategyMetrics.strategy_shifts.length > 0
    ) {
      const shifts = strategyMetrics.strategy_shifts

      // Add vertical lines for strategy shifts
      shifts.forEach(shift => {
        traces.push({
          x: [shift.experiment_index + 1, shift.experiment_index + 1],
          y: [-1, 1],
          name: `Strategy Shift at Exp ${shift.experiment_index + 1}`,
          type: "scatter" as const,
          mode: "lines+markers" as const,
          line: {
            color: "rgba(214, 39, 40, 0.7)",
            width: 2
            // Removed dash property as it's not in the type definition
          },
          marker: {
            size: 6,
            color: ["rgba(44, 160, 44, 1)", "rgba(44, 160, 44, 1)"]
          },
          hoverinfo: "text" as const,
          text: [
            `Strategy shift at Experiment ${shift.experiment_index + 1}<br>Magnitude: ${shift.shift_magnitude.toFixed(4)}`
          ]
          // Removed showlegend property as it's not in the type definition
        })
      })
    }

    // Add marker for selected experiment if any
    if (selectedExperiment !== undefined) {
      const expIndex = experiments.findIndex(
        exp => exp.experiment_index === selectedExperiment
      )

      if (expIndex >= 0) {
        traces.push({
          x: [experimentIndices[expIndex]],
          y: [experiments[expIndex].balance],
          name: "Selected Experiment",
          type: "scatter" as const,
          mode: "lines+markers" as const,
          line: {
            color: "rgba(44, 160, 44, 1)",
            width: 2
          },
          marker: {
            size: 12,
            color: ["rgba(44, 160, 44, 1)"]
            // Removed symbol and line properties as they're not in the type definition
          },
          hoverinfo: "text" as const,
          text: [`Selected Experiment ${selectedExperiment + 1}`]
        })
      }
    }

    return traces
  }, [strategyMetrics, showStrategyShifts, selectedExperiment])

  // Generate plot layout
  const plotLayout = useMemo(() => {
    return {
      title: "Exploration-Exploitation Balance",
      autosize: true,
      height: height,
      margin: { l: 60, r: 20, t: 50, b: 60 },
      xaxis: {
        title: "Experiment",
        tickmode: "auto" as const,
        nticks: 10
      },
      yaxis: {
        title: "Balance",
        range: [-1, 1],
        zeroline: true,
        zerolinecolor: "rgba(0, 0, 0, 0.2)",
        zerolinewidth: 1,
        automargin: true
      },
      showlegend: true,
      legend: {
        orientation: "h" as const,
        y: -0.2
      },
      hovermode: "closest" as const,
      annotations: [
        {
          x: 0,
          y: 0.9,
          xref: "paper" as const,
          yref: "paper" as const,
          text: "Exploration",
          showarrow: false,
          font: {
            color: "rgba(31, 119, 180, 1)"
          }
        },
        {
          x: 0,
          y: -0.9,
          xref: "paper" as const,
          yref: "paper" as const,
          text: "Exploitation",
          showarrow: false,
          font: {
            color: "rgba(255, 127, 14, 1)"
          }
        }
      ]
    }
  }, [height])

  // Handle experiment selection
  const handlePlotClick = (event: any) => {
    if (event && event.points && event.points.length > 0) {
      const point = event.points[0]
      const pointIndex = point.pointIndex

      // Get the actual experiment index from the strategy metrics
      if (
        pointIndex >= 0 &&
        pointIndex < strategyMetrics.ucb_components.experiments.length &&
        onExperimentSelect
      ) {
        const experimentIndex =
          strategyMetrics.ucb_components.experiments[pointIndex]
            .experiment_index
        console.log(`Selected experiment ${experimentIndex + 1}`)
        onExperimentSelect(experimentIndex)
      }
    }
  }

  return (
    <Plot
      data={plotData}
      layout={plotLayout}
      config={{ responsive: true, displayModeBar: false }}
      style={{ width: "100%", height: "100%" }}
      onClick={handlePlotClick}
    />
  )
}

// Main Strategy Evolution Component
export function StrategyEvolution({
  optimization,
  measurements,
  onExperimentSelect,
  selectedExperiment
}: StrategyEvolutionProps) {
  // State
  const [strategyMetrics, setStrategyMetrics] =
    useState<StrategyMetrics | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [betaValue, setBetaValue] = useState<number>(0.2)
  const [windowSize, setWindowSize] = useState<number>(5)
  const [showStrategyShifts, setShowStrategyShifts] = useState<boolean>(true)

  // Calculate strategy metrics on the client side
  const calculateClientSideMetrics = useCallback((): StrategyMetrics => {
    // Initialize metrics structure
    const metrics: StrategyMetrics = {
      parameter_diversity: {},
      improvement_rates: {},
      ucb_components: {
        experiments: []
      },
      exploration_score: 0,
      exploitation_score: 0,
      normalized_exploration: 0.5,
      normalized_exploitation: 0.5,
      balance: 0,
      dominant_strategy: "balanced",
      strategy_shifts: []
    }

    // Extract parameter names
    const parameterNames: string[] = []
    if (measurements.length > 0) {
      const firstMeasurement = measurements[0]
      // Add type assertion to handle unknown type
      const params = firstMeasurement.parameters as Record<
        string,
        string | number
      >
      parameterNames.push(...Object.keys(params))
    }

    // Calculate parameter diversity
    parameterNames.forEach(param => {
      const values = measurements.map(m => {
        const params = m.parameters as Record<string, string | number>
        return parseFloat(String(params[param]))
      })
      const validValues = values.filter(v => !isNaN(v))

      if (validValues.length > 1) {
        // Calculate variance
        const mean =
          validValues.reduce((sum, val) => sum + val, 0) / validValues.length
        const variance =
          validValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) /
          validValues.length

        // Normalize variance
        const min = Math.min(...validValues)
        const max = Math.max(...validValues)
        const range = max - min

        metrics.parameter_diversity[param] =
          range > 0 ? variance / Math.pow(range, 2) : 0
      } else {
        metrics.parameter_diversity[param] = 0
      }
    })

    // Calculate improvement rates
    const targetName = optimization.targetName
    const isMaximize = optimization.targetMode !== "MIN"

    let bestSoFar = isMaximize ? -Infinity : Infinity
    let improvements: number[] = []

    measurements.forEach(m => {
      const value = parseFloat(m.targetValue)
      if (!isNaN(value)) {
        const isBetter = isMaximize ? value > bestSoFar : value < bestSoFar

        if (isBetter) {
          if (bestSoFar !== (isMaximize ? -Infinity : Infinity)) {
            const improvement = isMaximize
              ? value - bestSoFar
              : bestSoFar - value
            improvements.push(improvement)
          }
          bestSoFar = value
        }
      }
    })

    // Calculate average improvement rate
    if (improvements.length > 0) {
      const avgImprovement =
        improvements.reduce((sum, val) => sum + val, 0) / improvements.length
      metrics.improvement_rates[targetName] = avgImprovement
    } else {
      metrics.improvement_rates[targetName] = 0
    }

    // Generate UCB components for each experiment
    measurements.forEach((m, index) => {
      // Simple heuristic for exploration and exploitation
      // Early experiments have higher exploration, later ones have higher exploitation
      const progress = index / Math.max(1, measurements.length - 1)
      const exploration = Math.max(0, 0.8 - progress * 0.6) * betaValue
      const exploitation = Math.min(1, 0.2 + progress * 0.6)

      // Calculate balance
      const total = exploration + exploitation
      const balance = total > 0 ? (exploration - exploitation) / total : 0

      metrics.ucb_components.experiments.push({
        experiment_index: index,
        mean: parseFloat(m.targetValue) || 0,
        std: 0.1, // Placeholder
        exploration,
        exploitation,
        balance,
        ucb_value: exploitation + exploration
      })
    })

    // Calculate overall scores
    metrics.exploration_score =
      Object.values(metrics.parameter_diversity).reduce(
        (sum, val) => sum + val,
        0
      ) / Math.max(1, Object.keys(metrics.parameter_diversity).length)

    metrics.exploitation_score =
      Object.values(metrics.improvement_rates).reduce(
        (sum, val) => sum + val,
        0
      ) / Math.max(1, Object.keys(metrics.improvement_rates).length)

    // Normalize scores
    const total = metrics.exploration_score + metrics.exploitation_score
    if (total > 0) {
      metrics.normalized_exploration = metrics.exploration_score / total
      metrics.normalized_exploitation = metrics.exploitation_score / total
    }

    // Calculate balance
    metrics.balance =
      metrics.normalized_exploration - metrics.normalized_exploitation

    // Determine dominant strategy
    if (metrics.balance > 0.2) {
      metrics.dominant_strategy = "exploration"
    } else if (metrics.balance < -0.2) {
      metrics.dominant_strategy = "exploitation"
    } else {
      metrics.dominant_strategy = "balanced"
    }

    // Detect strategy shifts
    if (metrics.ucb_components.experiments.length > 1) {
      for (let i = 1; i < metrics.ucb_components.experiments.length; i++) {
        const prev = metrics.ucb_components.experiments[i - 1]
        const curr = metrics.ucb_components.experiments[i]

        if (Math.abs(curr.balance - prev.balance) > 0.3) {
          metrics.strategy_shifts.push({
            experiment_index: curr.experiment_index,
            previous_balance: prev.balance,
            current_balance: curr.balance,
            shift_magnitude: curr.balance - prev.balance
          })
        }
      }
    }

    return metrics
  }, [
    optimization.targetName,
    optimization.targetMode,
    measurements,
    betaValue
  ])

  // Fetch strategy metrics from the backend
  const fetchStrategyMetrics = useCallback(async () => {
    if (!optimization.id) return

    setIsLoading(true)
    setError(null)

    try {
      // Try to fetch from backend API
      try {
        const response = await fetch(
          `/api/optimizations/${optimization.id}/strategy-metrics?window_size=${windowSize}&beta=${betaValue}`
        )

        if (response.ok) {
          const data = await response.json()

          if (data.status === "success" && data.metrics) {
            setStrategyMetrics(data.metrics)
            console.log("Strategy metrics from API:", data.metrics)
            setIsLoading(false)
            return
          }
        }
        // If we get here, the API call failed or returned invalid data
        // We'll fall through to the client-side implementation
        console.log("API call failed, using client-side implementation")
      } catch (apiError) {
        console.log("API error, using client-side implementation:", apiError)
        // We'll fall through to the client-side implementation
      }

      // Client-side implementation as fallback
      console.log("Using client-side implementation for strategy metrics")
      const clientSideMetrics = calculateClientSideMetrics()
      setStrategyMetrics(clientSideMetrics)
    } catch (err) {
      console.error("Error calculating strategy metrics:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
    } finally {
      setIsLoading(false)
    }
  }, [optimization.id, betaValue, windowSize, calculateClientSideMetrics])

  // Effect to fetch strategy metrics when optimization changes
  useEffect(() => {
    if (optimization.id && measurements.length > 0) {
      fetchStrategyMetrics()
    }
  }, [optimization.id, measurements.length, fetchStrategyMetrics])

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="flex items-center text-lg font-medium">
            <Workflow className="text-primary mr-2 size-5" />
            Strategy Evolution
            <StrategyEvolutionDocumentation />
          </h3>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchStrategyMetrics}
              disabled={isLoading}
            >
              {isLoading ? (
                <RefreshCw className="size-4 animate-spin" />
              ) : (
                <RefreshCw className="size-4" />
              )}
              <span className="ml-2 hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>

        {/* Controls */}
        <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="space-y-2">
            <Label htmlFor="beta-value" className="text-xs">
              Exploration Weight (β): {betaValue.toFixed(2)}
            </Label>
            <Slider
              id="beta-value"
              min={0}
              max={1}
              step={0.05}
              value={[betaValue]}
              onValueChange={value => setBetaValue(value[0])}
              className="mt-1"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="window-size" className="text-xs">
              Window Size: {windowSize}
            </Label>
            <Slider
              id="window-size"
              min={3}
              max={15}
              step={2}
              value={[windowSize]}
              onValueChange={value => setWindowSize(value[0])}
              className="mt-1"
            />
          </div>
          <div className="flex items-center space-y-2">
            <div className="flex items-center space-x-2">
              <Switch
                id="show-strategy-shifts"
                checked={showStrategyShifts}
                onCheckedChange={setShowStrategyShifts}
              />
              <Label htmlFor="show-strategy-shifts">Show Strategy Shifts</Label>
            </div>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="size-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex h-[400px] items-center justify-center">
            <Skeleton className="size-full rounded-md" />
          </div>
        ) : strategyMetrics ? (
          <div className="space-y-4">
            <div className="h-[400px] w-full overflow-hidden rounded-md border">
              <StrategyEvolutionChart
                strategyMetrics={strategyMetrics}
                showStrategyShifts={showStrategyShifts}
                onExperimentSelect={onExperimentSelect}
                selectedExperiment={selectedExperiment}
              />
            </div>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full">
                  <Maximize2 className="mr-2 size-4" />
                  Expand View
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>Strategy Evolution</DialogTitle>
                </DialogHeader>
                <div className="h-[600px] w-full">
                  <StrategyEvolutionChart
                    strategyMetrics={strategyMetrics}
                    showStrategyShifts={showStrategyShifts}
                    onExperimentSelect={onExperimentSelect}
                    selectedExperiment={selectedExperiment}
                    height={600}
                  />
                </div>
              </DialogContent>
            </Dialog>

            <Alert className="mt-2">
              <Info className="size-4" />
              <AlertTitle>Strategy Evolution</AlertTitle>
              <AlertDescription>
                This visualization shows the balance between exploration (trying
                new areas) and exploitation (focusing on promising areas). A
                positive balance indicates more exploration, while a negative
                balance indicates more exploitation.
                {strategyMetrics.strategy_shifts &&
                  strategyMetrics.strategy_shifts.length > 0 &&
                  showStrategyShifts && (
                    <span>
                      {" "}
                      {strategyMetrics.strategy_shifts.length} strategy shifts
                      detected (shown as vertical dashed lines).
                    </span>
                  )}
              </AlertDescription>
            </Alert>
          </div>
        ) : (
          <div className="flex h-[400px] flex-col items-center justify-center p-4 text-center">
            <Workflow className="text-muted-foreground mb-4 size-12" />
            <h3 className="text-lg font-medium">No strategy data</h3>
            <p className="text-muted-foreground mt-2 max-w-md text-sm">
              There might not be enough measurements to analyze strategy
              evolution, or the backend might not support strategy metrics.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
