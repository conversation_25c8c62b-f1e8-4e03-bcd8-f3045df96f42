"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import {
  GitBranch,
  RefreshCw,
  AlertCircle,
  Maximize2,
  Info
} from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import dynamic from "next/dynamic"

// Import Plotly dynamically to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

interface TradeOffExplorerProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  targetNames: string[]
  onTargetSelect?: (target: string) => void
  selectedTarget?: string
}

interface TradeOffData {
  targetValues: Record<string, number[]>
  targetRanges: Record<
    string,
    { min: number; max: number; isMaximize: boolean }
  >
  experimentIndices: number[]
  paretoFrontIndices: number[]
}

export function TradeOffExplorer({
  optimization,
  measurements,
  targetNames,
  onTargetSelect,
  selectedTarget
}: TradeOffExplorerProps) {
  const [tradeOffData, setTradeOffData] = useState<TradeOffData | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedTargets, setSelectedTargets] = useState<[string, string]>([
    "",
    ""
  ])
  const [highlightPareto, setHighlightPareto] = useState<boolean>(true)

  // Calculate trade-off data
  const calculateTradeOffData = () => {
    setIsLoading(true)
    setError(null)

    try {
      if (targetNames.length < 2) {
        throw new Error(
          "At least two targets are required for trade-off analysis"
        )
      }

      // Extract target values
      const targetValues: Record<string, number[]> = {}
      const targetRanges: Record<
        string,
        { min: number; max: number; isMaximize: boolean }
      > = {}
      const experimentIndices: number[] = []

      // Initialize target arrays
      targetNames.forEach(target => {
        targetValues[target] = []

        // Determine if target should be maximized or minimized
        // Default to maximize if not specified
        let isMaximize = true

        // Check if target configuration is available in optimization config
        if (optimization.config && typeof optimization.config === "object") {
          const config = optimization.config as any
          if (config.target_config && Array.isArray(config.target_config)) {
            const targetConfig = config.target_config.find(
              (t: any) => t.name === target
            )
            if (targetConfig && targetConfig.mode) {
              isMaximize = targetConfig.mode.toUpperCase() === "MAX"
            }
          }
        }

        targetRanges[target] = { min: Infinity, max: -Infinity, isMaximize }
      })

      // Collect values
      measurements.forEach((measurement, index) => {
        experimentIndices.push(index)

        // Extract target values
        if (measurement.targetValues) {
          const targetValuesObj = measurement.targetValues as Record<
            string,
            number
          >
          targetNames.forEach(target => {
            // Check if the value is a number or a string and handle accordingly
            const rawValue = targetValuesObj[target]
            const value =
              typeof rawValue === "number"
                ? rawValue
                : parseFloat(String(rawValue))
            if (!isNaN(value)) {
              targetValues[target].push(value)

              // Update ranges
              targetRanges[target].min = Math.min(
                targetRanges[target].min,
                value
              )
              targetRanges[target].max = Math.max(
                targetRanges[target].max,
                value
              )
            } else {
              // Push NaN to maintain index alignment
              targetValues[target].push(NaN)
            }
          })
        } else if (targetNames.includes(optimization.targetName)) {
          // Single target case
          const rawValue = measurement.targetValue
          const value =
            typeof rawValue === "number"
              ? rawValue
              : parseFloat(String(rawValue))
          if (!isNaN(value)) {
            targetValues[optimization.targetName].push(value)

            // Update ranges
            targetRanges[optimization.targetName].min = Math.min(
              targetRanges[optimization.targetName].min,
              value
            )
            targetRanges[optimization.targetName].max = Math.max(
              targetRanges[optimization.targetName].max,
              value
            )

            // Push NaN for other targets to maintain index alignment
            targetNames.forEach(target => {
              if (target !== optimization.targetName) {
                targetValues[target].push(NaN)
              }
            })
          }
        }
      })

      // Calculate Pareto front
      const paretoFrontIndices = calculateParetoFront(
        targetValues,
        targetRanges
      )

      setTradeOffData({
        targetValues,
        targetRanges,
        experimentIndices,
        paretoFrontIndices
      })

      // Initialize selected targets if empty
      if (
        !selectedTargets[0] &&
        !selectedTargets[1] &&
        targetNames.length >= 2
      ) {
        setSelectedTargets([targetNames[0], targetNames[1]])
      }

      console.log("Trade-off data calculated:", {
        targetCount: Object.keys(targetValues).length,
        measurementCount: measurements.length,
        paretoFrontSize: paretoFrontIndices.length,
        targetRanges
      })
    } catch (err) {
      console.error("Error calculating trade-off data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  // Calculate Pareto front
  const calculateParetoFront = (
    targetValues: Record<string, number[]>,
    targetRanges: Record<
      string,
      { min: number; max: number; isMaximize: boolean }
    >
  ): number[] => {
    const paretoIndices: number[] = []
    const n = measurements.length

    // For each experiment, check if it's dominated by any other experiment
    for (let i = 0; i < n; i++) {
      let isDominated = false

      // Check against all other experiments
      for (let j = 0; j < n; j++) {
        if (i === j) continue

        // Check if j dominates i
        let jDominatesI = true
        let atLeastOneBetter = false

        for (const target of targetNames) {
          const valueI = targetValues[target][i]
          const valueJ = targetValues[target][j]

          // Skip if either value is NaN
          if (isNaN(valueI) || isNaN(valueJ)) {
            jDominatesI = false
            break
          }

          const isMaximize = targetRanges[target].isMaximize

          if (isMaximize) {
            // For maximization, j should be >= i to dominate
            if (valueJ < valueI) {
              jDominatesI = false
              break
            }
            if (valueJ > valueI) {
              atLeastOneBetter = true
            }
          } else {
            // For minimization, j should be <= i to dominate
            if (valueJ > valueI) {
              jDominatesI = false
              break
            }
            if (valueJ < valueI) {
              atLeastOneBetter = true
            }
          }
        }

        // j dominates i if it's at least as good in all targets and better in at least one
        if (jDominatesI && atLeastOneBetter) {
          isDominated = true
          break
        }
      }

      // If i is not dominated by any other experiment, it's on the Pareto front
      if (!isDominated) {
        paretoIndices.push(i)
      }
    }

    return paretoIndices
  }

  // Generate scatter plot data
  const scatterPlotData = useMemo(() => {
    if (!tradeOffData || !selectedTargets[0] || !selectedTargets[1]) return []

    const {
      targetValues,
      targetRanges,
      experimentIndices,
      paretoFrontIndices
    } = tradeOffData
    const [xTarget, yTarget] = selectedTargets

    // Get values for the selected targets
    const xValues: number[] = []
    const yValues: number[] = []
    const experimentIds: number[] = []
    const hoverTexts: string[] = []

    // Collect values for all experiments
    experimentIndices.forEach(index => {
      const xValue = targetValues[xTarget][index]
      const yValue = targetValues[yTarget][index]

      // Skip if either value is NaN
      if (!isNaN(xValue) && !isNaN(yValue)) {
        xValues.push(xValue)
        yValues.push(yValue)
        experimentIds.push(index + 1) // 1-based indices for display

        // Create hover text with all target values
        let hoverText = `Experiment ${index + 1}<br>`
        targetNames.forEach(target => {
          const value = targetValues[target][index]
          if (!isNaN(value)) {
            hoverText += `${target}: ${value.toFixed(2)}<br>`
          }
        })
        hoverTexts.push(hoverText)
      }
    })

    // Create scatter plot for all experiments
    const allExperimentsTrace = {
      x: xValues,
      y: yValues,
      mode: "markers" as const,
      type: "scatter" as const,
      marker: {
        color: "rgba(128, 128, 128, 0.7)",
        size: 10
      },
      name: "All Experiments",
      hoverinfo: "text" as const,
      text: hoverTexts,
      customdata: experimentIds
    }

    // If highlighting Pareto front, create a separate trace
    if (highlightPareto && paretoFrontIndices.length > 0) {
      const paretoXValues: number[] = []
      const paretoYValues: number[] = []
      const paretoHoverTexts: string[] = []
      const paretoExperimentIds: number[] = []

      // Collect values for Pareto front experiments
      paretoFrontIndices.forEach(index => {
        const xValue = targetValues[xTarget][index]
        const yValue = targetValues[yTarget][index]

        // Skip if either value is NaN
        if (!isNaN(xValue) && !isNaN(yValue)) {
          paretoXValues.push(xValue)
          paretoYValues.push(yValue)
          paretoExperimentIds.push(index + 1) // 1-based indices for display

          // Create hover text with all target values
          let hoverText = `Experiment ${index + 1} (Pareto optimal)<br>`
          targetNames.forEach(target => {
            const value = targetValues[target][index]
            if (!isNaN(value)) {
              hoverText += `${target}: ${value.toFixed(2)}<br>`
            }
          })
          paretoHoverTexts.push(hoverText)
        }
      })

      // Create scatter plot for Pareto front
      const paretoFrontTrace = {
        x: paretoXValues,
        y: paretoYValues,
        mode: "markers" as const,
        type: "scatter" as const,
        marker: {
          color: "rgba(255, 0, 0, 0.8)",
          size: 12,
          line: {
            color: "black",
            width: 1
          }
        },
        name: "Pareto Front",
        hoverinfo: "text" as const,
        text: paretoHoverTexts,
        customdata: paretoExperimentIds
      }

      // If there are enough points, add a line connecting the Pareto front
      if (paretoXValues.length > 1) {
        // Sort points for line connection
        // For minimization problems, sort in ascending order
        // For maximization problems, sort in descending order
        const sortedIndices = [...Array(paretoXValues.length).keys()]

        const xIsMaximize = targetRanges[xTarget].isMaximize

        sortedIndices.sort((a, b) => {
          return xIsMaximize
            ? paretoXValues[b] - paretoXValues[a]
            : paretoXValues[a] - paretoXValues[b]
        })

        const sortedX = sortedIndices.map(i => paretoXValues[i])
        const sortedY = sortedIndices.map(i => paretoYValues[i])

        const paretoLineTrace = {
          x: sortedX,
          y: sortedY,
          mode: "lines" as const,
          type: "scatter" as const,
          line: {
            color: "rgba(255, 0, 0, 0.5)",
            width: 2,
            dash: "dash" as const
          },
          name: "Pareto Front Line",
          hoverinfo: "skip" as const
        }

        return [allExperimentsTrace, paretoFrontTrace, paretoLineTrace]
      }

      return [allExperimentsTrace, paretoFrontTrace]
    }

    return [allExperimentsTrace]
  }, [tradeOffData, selectedTargets, highlightPareto, targetNames])

  // Handle target selection
  const handleTargetSelect = (target: string, index: 0 | 1) => {
    const newSelectedTargets = [...selectedTargets] as [string, string]
    newSelectedTargets[index] = target
    setSelectedTargets(newSelectedTargets)

    if (onTargetSelect) {
      onTargetSelect(target)
    }
  }

  // Handle plot click
  const handlePlotClick = (event: any) => {
    if (event && event.points && event.points.length > 0) {
      const point = event.points[0]
      const experimentId = point.customdata

      console.log(`Clicked on experiment ${experimentId}`)

      // Additional handling if needed
    }
  }

  // Effect to calculate trade-off data when measurements change
  useEffect(() => {
    if (measurements.length > 0 && targetNames.length >= 2) {
      calculateTradeOffData()
    }
  }, [measurements.length, targetNames.length])

  // Effect to update selected targets when selectedTarget changes
  useEffect(() => {
    if (selectedTarget && !selectedTargets.includes(selectedTarget)) {
      // Replace the first target with the selected one
      const newSelectedTargets = [...selectedTargets] as [string, string]
      newSelectedTargets[0] = selectedTarget
      setSelectedTargets(newSelectedTargets)
    }
  }, [selectedTarget])

  // Get plot layout
  const getPlotLayout = () => {
    if (!tradeOffData || !selectedTargets[0] || !selectedTargets[1]) {
      return {
        autosize: true,
        title: "Trade-off Explorer",
        margin: { l: 60, r: 20, t: 50, b: 60 }
      }
    }

    const { targetRanges } = tradeOffData
    const [xTarget, yTarget] = selectedTargets

    // Determine axis titles with optimization direction
    const xAxisTitle = `${xTarget} ${targetRanges[xTarget].isMaximize ? "(Maximize)" : "(Minimize)"}`
    const yAxisTitle = `${yTarget} ${targetRanges[yTarget].isMaximize ? "(Maximize)" : "(Minimize)"}`

    return {
      autosize: true,
      title: "Trade-off Explorer",
      margin: { l: 60, r: 20, t: 50, b: 60 },
      xaxis: {
        title: xAxisTitle,
        automargin: true
      },
      yaxis: {
        title: yAxisTitle,
        automargin: true
      },
      showlegend: true,
      legend: {
        orientation: "h" as const,
        y: -0.2
      }
    }
  }

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="flex items-center text-lg font-medium">
            <GitBranch className="text-primary mr-2 size-5" />
            Trade-off Explorer
          </h3>
          <div className="flex gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setHighlightPareto(!highlightPareto)}
                  >
                    {highlightPareto ? "Hide Pareto" : "Show Pareto"}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Toggle Pareto front highlighting</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Button
              variant="outline"
              size="sm"
              onClick={calculateTradeOffData}
              disabled={isLoading}
            >
              {isLoading ? (
                <RefreshCw className="size-4 animate-spin" />
              ) : (
                <RefreshCw className="size-4" />
              )}
              <span className="ml-2 hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="size-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {targetNames.length < 2 && (
          <Alert className="mb-4">
            <Info className="size-4" />
            <AlertTitle>Single Target Optimization</AlertTitle>
            <AlertDescription>
              Trade-off analysis requires at least two target variables. This
              optimization has only one target.
            </AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex h-[400px] items-center justify-center">
            <Skeleton className="size-full rounded-md" />
          </div>
        ) : tradeOffData && targetNames.length >= 2 ? (
          <div className="space-y-4">
            <div className="mb-4 grid grid-cols-2 gap-2">
              <div>
                <label className="mb-1 block text-sm font-medium">
                  X-Axis Target
                </label>
                <Select
                  value={selectedTargets[0]}
                  onValueChange={value => handleTargetSelect(value, 0)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select target" />
                  </SelectTrigger>
                  <SelectContent>
                    {targetNames.map(target => (
                      <SelectItem key={target} value={target}>
                        {target}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium">
                  Y-Axis Target
                </label>
                <Select
                  value={selectedTargets[1]}
                  onValueChange={value => handleTargetSelect(value, 1)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select target" />
                  </SelectTrigger>
                  <SelectContent>
                    {targetNames.map(target => (
                      <SelectItem key={target} value={target}>
                        {target}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="h-[400px] w-full overflow-hidden rounded-md border">
              <Plot
                data={scatterPlotData}
                layout={getPlotLayout()}
                config={{ responsive: true, displayModeBar: false }}
                style={{ width: "100%", height: "100%" }}
                onClick={handlePlotClick}
              />
            </div>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full">
                  <Maximize2 className="mr-2 size-4" />
                  Expand View
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>Trade-off Explorer</DialogTitle>
                </DialogHeader>
                <div className="h-[600px] w-full">
                  <Plot
                    data={scatterPlotData}
                    layout={{
                      ...getPlotLayout(),
                      height: 600
                    }}
                    config={{ responsive: true }}
                    style={{ width: "100%", height: "100%" }}
                    onClick={handlePlotClick}
                  />
                </div>
              </DialogContent>
            </Dialog>

            {highlightPareto && tradeOffData.paretoFrontIndices.length > 0 && (
              <Alert className="mt-2">
                <Info className="size-4" />
                <AlertTitle>Pareto Front</AlertTitle>
                <AlertDescription>
                  {tradeOffData.paretoFrontIndices.length} experiments are on
                  the Pareto front (highlighted in red). These represent optimal
                  trade-offs between the selected targets.
                </AlertDescription>
              </Alert>
            )}
          </div>
        ) : (
          <div className="flex h-[400px] flex-col items-center justify-center p-4 text-center">
            <GitBranch className="text-muted-foreground mb-4 size-12" />
            <h3 className="text-lg font-medium">No trade-off data</h3>
            <p className="text-muted-foreground mt-2 max-w-md text-sm">
              There might not be enough measurements with multiple targets, or
              the targets might not have numeric values.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
