// components/optimization/parameter-impact-chart.tsx
// This is a new implementation of the ParameterImpactChart component
// that was missing from the original codebase.
"use client"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { SelectMeasurement } from "@/db/schema/optimizations-schema"
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts"
import { useState } from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"

interface ParameterImpactChartProps {
  measurements: SelectMeasurement[]
  parameterNames: string[]
  targetName: string
  targetMode: "MAX" | "MIN" | "MULTI"
  availableTargets?: string[]
}

export function ParameterImpactChart({
  measurements,
  parameterNames,
  targetName,
  targetMode,
  availableTargets = []
}: ParameterImpactChartProps) {
  const [selectedParameter, setSelectedParameter] = useState<string>(
    parameterNames.length > 0 ? parameterNames[0] : ""
  )

  // For multi-target optimizations, add a state for the selected target
  const isMultiTarget = targetMode === "MULTI"

  // Use the provided availableTargets if available, otherwise use the primary target
  const allAvailableTargets =
    isMultiTarget && availableTargets.length > 0
      ? availableTargets
      : [targetName]

  const [selectedTarget, setSelectedTarget] = useState<string>(targetName)

  // Skip if we don't have enough data or parameters
  if (measurements.length < 5 || parameterNames.length === 0) {
    return null
  }

  // Calculate impact scores for the selected parameter and target
  const calculateParameterImpact = (
    paramName: string,
    targetToAnalyze: string
  ) => {
    // Get unique values for the parameter
    const uniqueValues = Array.from(
      new Set(
        measurements.map(m => {
          const params = m.parameters as Record<string, any>
          return typeof params[paramName] === "number"
            ? Math.round(params[paramName] * 100) / 100
            : params[paramName]
        })
      )
    ).sort((a, b) => {
      if (typeof a === "number" && typeof b === "number") {
        return a - b
      }
      return String(a).localeCompare(String(b))
    })

    // Skip if we don't have enough unique values
    if (uniqueValues.length < 2) {
      return []
    }

    // Calculate average target value for each parameter value
    return uniqueValues.map(value => {
      const relevantMeasurements = measurements.filter(m => {
        const params = m.parameters as Record<string, any>
        const paramValue = params[paramName]
        if (typeof paramValue === "number" && typeof value === "number") {
          return Math.abs(paramValue - value) < 0.001
        }
        return paramValue === value
      })

      // Get the target value based on the selected target
      const getTargetValue = (measurement: SelectMeasurement): number => {
        // For multi-target optimizations, get the value from targetValues
        if (
          isMultiTarget &&
          measurement.targetValues &&
          typeof measurement.targetValues === "object"
        ) {
          // If the selected target is in targetValues, use that value
          const targetValues = measurement.targetValues as Record<
            string,
            number
          >
          if (targetValues[targetToAnalyze] !== undefined) {
            return targetValues[targetToAnalyze]
          }
        }

        // If this is the primary target or we don't have targetValues
        if (targetToAnalyze === targetName || !measurement.targetValues) {
          return parseFloat(measurement.targetValue)
        }

        // Fallback
        return parseFloat(measurement.targetValue)
      }

      // Calculate average target value
      const avgTargetValue =
        relevantMeasurements.reduce((sum, m) => sum + getTargetValue(m), 0) /
        relevantMeasurements.length

      return {
        parameterValue: value,
        averageTargetValue: avgTargetValue,
        count: relevantMeasurements.length
      }
    })
  }

  const impactData = calculateParameterImpact(selectedParameter, selectedTarget)

  // Format parameter value for display
  const formatParameterValue = (value: any) => {
    if (typeof value === "number") {
      return value.toFixed(2)
    }
    if (typeof value === "boolean") {
      return value ? "True" : "False"
    }
    return String(value)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Parameter Impact Analysis</CardTitle>
        <CardDescription>
          How different parameter values affect the target outcome
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6 space-y-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex flex-col gap-1">
              <label className="text-sm font-medium">
                Parameter to Analyze:
              </label>
              <Select
                value={selectedParameter}
                onValueChange={value => setSelectedParameter(value)}
              >
                <SelectTrigger className="w-[240px]">
                  <SelectValue placeholder="Select parameter" />
                </SelectTrigger>
                <SelectContent>
                  {parameterNames.map(param => (
                    <SelectItem key={param} value={param}>
                      {param}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Target selector for multi-target optimizations */}
            {isMultiTarget && allAvailableTargets.length > 1 && (
              <div className="flex flex-col gap-1">
                <label className="text-sm font-medium">
                  Target to Analyze:
                </label>
                <Select
                  value={selectedTarget}
                  onValueChange={value => setSelectedTarget(value)}
                >
                  <SelectTrigger className="w-[240px]">
                    <SelectValue placeholder="Select target" />
                  </SelectTrigger>
                  <SelectContent>
                    {allAvailableTargets.map(target => (
                      <SelectItem key={target} value={target}>
                        {target}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {isMultiTarget && allAvailableTargets.length > 1 && (
            <div className="text-muted-foreground bg-muted/50 rounded-md p-2 text-sm">
              <p>
                This is a multi-target optimization. Select which target you
                want to analyze.
              </p>
            </div>
          )}
        </div>

        {impactData.length > 0 ? (
          <div className="h-[600px] w-full">
            {" "}
            {/* Increased height from 400px to 600px for maximum space utilization */}
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={impactData}
                margin={{ top: 5, right: 30, left: 20, bottom: 25 }} // Reduced bottom margin further to keep legend within card
              >
                <CartesianGrid strokeDasharray="3 3" opacity={0.2} />{" "}
                {/* Reduced opacity for cleaner look */}
                <XAxis
                  dataKey="parameterValue"
                  tickFormatter={formatParameterValue}
                  label={{
                    value: selectedParameter,
                    position: "insideBottom",
                    offset: -5, // Reduced negative offset to move label up
                    dy: 10, // Reduced downward offset to bring x-axis title closer to axis
                    style: {
                      fontSize: "14px", // Slightly larger font for better readability in the larger chart
                      fontWeight: 500 // Medium weight for better visibility
                    }
                  }}
                  tick={{ fontSize: 12 }} // Slightly larger tick labels
                />
                <YAxis
                  label={{
                    value: `Average ${isMultiTarget ? selectedTarget : targetName}`,
                    angle: -90,
                    position: "insideLeft",
                    offset: 0, // Adjust offset to center vertically
                    dy: 0, // Center vertically
                    style: {
                      textAnchor: "middle", // Center text
                      fontSize: "14px", // Slightly larger font for better readability in the larger chart
                      fontWeight: 500 // Medium weight for better visibility
                    }
                  }}
                  tickFormatter={value => value.toFixed(2)} // Format tick values for cleaner appearance
                />
                <Tooltip
                  formatter={(value: number) => [
                    value.toFixed(4),
                    `Average ${isMultiTarget ? selectedTarget : targetName}`
                  ]}
                  labelFormatter={formatParameterValue}
                />
                <Legend
                  verticalAlign="bottom"
                  height={15} // Minimized height
                  wrapperStyle={{
                    paddingTop: "5px", // Minimal padding to position just below x-axis title
                    marginBottom: "0px",
                    marginTop: "0px",
                    position: "relative",
                    bottom: "5px" // Pull legend up slightly
                  }}
                />
                <Bar
                  name={`Average ${isMultiTarget ? selectedTarget : targetName}`}
                  dataKey="averageTargetValue"
                  fill="#8884d8"
                  radius={[6, 6, 0, 0]} // Slightly rounded top corners for better aesthetics
                  animationDuration={500} // Add a subtle animation
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <div className="text-muted-foreground py-8 text-center">
            <p>Not enough data to analyze parameter impact.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
