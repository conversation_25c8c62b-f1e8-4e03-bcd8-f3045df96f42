"use client"

import { useState, useEffect, useMemo } from "react"
import dynamic from "next/dynamic"

// UI components
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { SelectMeasurement } from "@/db/schema/optimizations-schema"
import {
  Download,
  RefreshCw,
  Layers,
  Grid3X3,
  Scissors,
  BarChart,
  Eye,
  Box
} from "lucide-react"

// Dynamically import Plotly to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot = dynamic(() => import("react-plotly.js"), { ssr: false })

// Using custom PlotlySurface component instead of direct Plotly import

interface SurfaceAnalysisProps {
  measurements: SelectMeasurement[]
  parameterNames: string[]
  targetName: string
  targetMode: "MAX" | "MIN" | "MULTI"
  availableTargets?: string[]
}

export function SurfaceAnalysis({
  measurements,
  parameterNames,
  targetName,
  targetMode,
  availableTargets = []
}: SurfaceAnalysisProps) {
  // State for selected parameters and targets
  const [xParameter, setXParameter] = useState<string>("")
  const [yParameter, setYParameter] = useState<string>("")
  const [selectedTarget, setSelectedTarget] = useState<string>("")
  const [showCrossSection, setShowCrossSection] = useState<boolean>(false)
  const [crossSectionAxis, setCrossSectionAxis] = useState<"x" | "y">("x")
  const [crossSectionValue, setCrossSectionValue] = useState<number | null>(
    null
  )
  const [viewMode, setViewMode] = useState<"3d" | "cross-section">("3d")

  // State for plot configuration
  const [resolution, setResolution] = useState<number>(20)
  const [smoothing, setSmoothing] = useState<boolean>(true)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  // Fixed color scale - not user configurable
  const colorScale = "viridis"

  // For multi-target optimizations, use the available targets
  const allAvailableTargets = useMemo(() => {
    return targetMode === "MULTI" && availableTargets.length > 0
      ? availableTargets
      : [targetName]
  }, [targetMode, availableTargets, targetName])

  // Initialize selections when component mounts
  useEffect(() => {
    if (parameterNames.length >= 2) {
      setXParameter(parameterNames[0])
      setYParameter(parameterNames[1])
    }

    if (allAvailableTargets.length > 0) {
      // Initialize with the first target
      setSelectedTarget(allAvailableTargets[0])
    }
  }, [parameterNames, allAvailableTargets])

  // Function to interpolate missing points in the z-grid
  const interpolateMissingPoints = (zGrid: number[][]) => {
    const rows = zGrid.length
    const cols = zGrid[0].length

    // Simple linear interpolation
    // First pass: interpolate horizontally
    for (let i = 0; i < rows; i++) {
      let lastValidValue = NaN
      let lastValidIndex = -1

      // Forward pass
      for (let j = 0; j < cols; j++) {
        if (!isNaN(zGrid[i][j])) {
          if (lastValidIndex !== -1 && j - lastValidIndex > 1) {
            // Interpolate between lastValidIndex and j
            for (let k = lastValidIndex + 1; k < j; k++) {
              const ratio = (k - lastValidIndex) / (j - lastValidIndex)
              zGrid[i][k] =
                lastValidValue + ratio * (zGrid[i][j] - lastValidValue)
            }
          }
          lastValidValue = zGrid[i][j]
          lastValidIndex = j
        }
      }

      // Backward pass for remaining NaN values
      lastValidValue = NaN
      lastValidIndex = cols

      for (let j = cols - 1; j >= 0; j--) {
        if (!isNaN(zGrid[i][j])) {
          lastValidValue = zGrid[i][j]
          lastValidIndex = j
        } else if (lastValidIndex < cols) {
          zGrid[i][j] = lastValidValue
        }
      }
    }

    // Second pass: interpolate vertically
    for (let j = 0; j < cols; j++) {
      let lastValidValue = NaN
      let lastValidIndex = -1

      // Forward pass
      for (let i = 0; i < rows; i++) {
        if (!isNaN(zGrid[i][j])) {
          if (lastValidIndex !== -1 && i - lastValidIndex > 1) {
            // Interpolate between lastValidIndex and i
            for (let k = lastValidIndex + 1; k < i; k++) {
              const ratio = (k - lastValidIndex) / (i - lastValidIndex)
              zGrid[k][j] =
                lastValidValue + ratio * (zGrid[i][j] - lastValidValue)
            }
          }
          lastValidValue = zGrid[i][j]
          lastValidIndex = i
        }
      }

      // Backward pass for remaining NaN values
      lastValidValue = NaN
      lastValidIndex = rows

      for (let i = rows - 1; i >= 0; i--) {
        if (!isNaN(zGrid[i][j])) {
          lastValidValue = zGrid[i][j]
          lastValidIndex = i
        } else if (lastValidIndex < rows) {
          zGrid[i][j] = lastValidValue
        }
      }
    }
  }

  // Process data for surface plot
  const surfaceData = useMemo(() => {
    // Debug logging
    console.log("Surface Analysis - Data Check:", {
      xParameter,
      yParameter,
      selectedTarget,
      measurementsLength: measurements.length,
      sampleMeasurement:
        measurements.length > 0
          ? {
              parameters: measurements[0].parameters,
              targetValue: measurements[0].targetValue,
              targetValues: measurements[0].targetValues,
              parameterKeys:
                measurements.length > 0 && measurements[0].parameters
                  ? Object.keys(
                      measurements[0].parameters as Record<string, any>
                    )
                  : [],
              hasXParameter:
                measurements.length > 0 &&
                measurements[0].parameters &&
                xParameter in
                  (measurements[0].parameters as Record<string, any>),
              hasYParameter:
                measurements.length > 0 &&
                measurements[0].parameters &&
                yParameter in
                  (measurements[0].parameters as Record<string, any>)
            }
          : null
    })

    // Check if we have all required data for the plot
    if (
      !xParameter ||
      !yParameter ||
      !selectedTarget ||
      measurements.length < 1
    ) {
      console.log("Surface Analysis - Missing required data:", {
        hasXParameter: !!xParameter,
        hasYParameter: !!yParameter,
        hasSelectedTarget: !!selectedTarget,
        measurementsLength: measurements.length
      })
      return null
    }

    setIsLoading(true)

    try {
      // Extract unique x and y values
      const xValues = Array.from(
        new Set(
          measurements
            .map(m => {
              const params = m.parameters as Record<string, any>
              return typeof params[xParameter] === "number"
                ? params[xParameter]
                : null
            })
            .filter(Boolean) as number[]
        )
      ).sort((a, b) => a - b)

      const yValues = Array.from(
        new Set(
          measurements
            .map(m => {
              const params = m.parameters as Record<string, any>
              return typeof params[yParameter] === "number"
                ? params[yParameter]
                : null
            })
            .filter(Boolean) as number[]
        )
      ).sort((a, b) => a - b)

      // Create a grid of points for interpolation
      const xStep =
        (Math.max(...xValues) - Math.min(...xValues)) / (resolution - 1)
      const yStep =
        (Math.max(...yValues) - Math.min(...yValues)) / (resolution - 1)

      const xGrid = Array.from(
        { length: resolution },
        (_, i) => Math.min(...xValues) + i * xStep
      )

      const yGrid = Array.from(
        { length: resolution },
        (_, i) => Math.min(...yValues) + i * yStep
      )

      // Process each selected target
      console.log("Surface Analysis - Processing grid:", {
        xGridLength: xGrid.length,
        yGridLength: yGrid.length,
        xRange: [Math.min(...xGrid), Math.max(...xGrid)],
        yRange: [Math.min(...yGrid), Math.max(...yGrid)]
      })

      // Create surface for the selected target
      const surfaces = [selectedTarget]
        .map(targetName => {
          // Initialize z-value grid with NaN
          const zGrid = Array.from({ length: yGrid.length }, () =>
            Array.from({ length: xGrid.length }, () => NaN)
          )

          // Fill in known points
          let validPointsCount = 0
          let invalidPointsCount = 0

          measurements.forEach(measurement => {
            // Get parameter values, with error handling
            let x, y

            try {
              // Check if parameters exist and are accessible
              if (!measurement.parameters) {
                console.error("Measurement has no parameters:", measurement)
                invalidPointsCount++
                return
              }

              // Get parameter values
              const params = measurement.parameters as Record<string, any>
              x = params[xParameter]
              y = params[yParameter]

              // Try to convert string values to numbers if needed
              if (typeof x === "string") x = parseFloat(x)
              if (typeof y === "string") y = parseFloat(y)

              // Skip if x or y is not a number
              if (
                typeof x !== "number" ||
                typeof y !== "number" ||
                isNaN(x) ||
                isNaN(y)
              ) {
                console.log(`Invalid parameter values for measurement:`, {
                  id: measurement.id,
                  xParam: xParameter,
                  yParam: yParameter,
                  xValue: x,
                  yValue: y
                })
                invalidPointsCount++
                return
              }
            } catch (error) {
              console.error("Error accessing parameter values:", error)
              invalidPointsCount++
              return
            }

            // Get target value
            let z: number | undefined

            try {
              if (targetMode === "MULTI" && measurement.targetValues) {
                // For multi-target, get the specific target value
                const targetValues = measurement.targetValues as Record<
                  string,
                  number
                >
                z = targetValues[targetName]

                // If target value is missing, try to use the main target value as fallback
                if (
                  z === undefined &&
                  targetName === (measurement as any).targetName
                ) {
                  z = parseFloat(measurement.targetValue)
                }
              } else {
                // For single-target, use the main target value
                z = parseFloat(measurement.targetValue)
              }

              // Log if we found a valid target value
              if (!isNaN(z)) {
                console.log(`Found valid target value for ${targetName}:`, z)
              }
            } catch (error) {
              console.error(
                `Error getting target value for ${targetName}:`,
                error
              )
              z = undefined
            }

            if (z === undefined || isNaN(z)) {
              invalidPointsCount++
              return
            }

            // Find closest grid points
            const xIndex = Math.round((x - Math.min(...xValues)) / xStep)
            const yIndex = Math.round((y - Math.min(...yValues)) / yStep)

            // Ensure indices are within bounds
            if (
              xIndex >= 0 &&
              xIndex < xGrid.length &&
              yIndex >= 0 &&
              yIndex < yGrid.length
            ) {
              zGrid[yIndex][xIndex] = z
              validPointsCount++
            }
          })

          console.log(`Surface Analysis - Target ${targetName} data points:`, {
            validPoints: validPointsCount,
            invalidPoints: invalidPointsCount,
            totalMeasurements: measurements.length
          })

          // Check if we have any valid points
          if (validPointsCount === 0) {
            console.warn(`No valid data points found for target ${targetName}`)
            // Return null to indicate no data for this target
            return null
          }

          // Apply interpolation for missing points if smoothing is enabled
          if (smoothing) {
            interpolateMissingPoints(zGrid)
          }

          return {
            targetName,
            zGrid
          }
        })
        .filter(Boolean) // Filter out null values

      // Generate cross-section data if needed
      let crossSections = null
      if (showCrossSection && crossSectionValue !== null) {
        crossSections = [selectedTarget]
          .map(targetName => {
            const targetSurface = surfaces.find(
              s => s && s.targetName === targetName
            )
            if (!targetSurface) return null

            if (crossSectionAxis === "x") {
              // Find the closest x value
              const xIndex = xGrid.findIndex(
                x =>
                  Math.abs(x - crossSectionValue) ===
                  Math.min(
                    ...xGrid.map(val => Math.abs(val - crossSectionValue))
                  )
              )

              if (xIndex >= 0) {
                // Extract the column at xIndex
                return {
                  targetName,
                  axis: yGrid,
                  values: targetSurface.zGrid.map(row => row[xIndex])
                }
              }
            } else {
              // y-axis
              // Find the closest y value
              const yIndex = yGrid.findIndex(
                y =>
                  Math.abs(y - crossSectionValue) ===
                  Math.min(
                    ...yGrid.map(val => Math.abs(val - crossSectionValue))
                  )
              )

              if (yIndex >= 0 && yIndex < targetSurface.zGrid.length) {
                // Extract the row at yIndex
                return {
                  targetName,
                  axis: xGrid,
                  values: targetSurface.zGrid[yIndex]
                }
              }
            }

            return null
          })
          .filter(Boolean)
      }

      // Final data check before returning
      console.log("Surface Analysis - Final data:", {
        surfacesCount: surfaces.length,
        firstSurfacePoints:
          surfaces.length > 0 && surfaces[0]
            ? surfaces[0].zGrid.flat().filter(z => !isNaN(z)).length
            : 0,
        hasCrossSections: crossSections !== null && crossSections.length > 0
      })

      // Check if we have any valid surfaces
      if (surfaces.length === 0) {
        console.error("No valid surfaces generated from the data")
        return null
      }

      return {
        x: xGrid,
        y: yGrid,
        surfaces,
        crossSections
      }
    } catch (error) {
      console.error("Error processing surface data:", error)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [
    xParameter,
    yParameter,
    selectedTarget,
    measurements,
    resolution,
    smoothing,
    targetMode,
    showCrossSection,
    crossSectionAxis,
    crossSectionValue
  ])

  // Function to handle plot download
  const handleDownload = () => {
    try {
      // Find the plot element
      const plotElement = document.querySelector(
        ".js-plotly-plot"
      ) as HTMLElement
      if (!plotElement) {
        console.error("Plot element not found for download")
        return
      }

      // Generate a descriptive filename
      let filename = ""
      if (viewMode === "3d") {
        filename = `surface-plot-${xParameter}-${yParameter}-${selectedTarget}.png`
      } else {
        filename = `cross-section-${crossSectionAxis === "x" ? xParameter : yParameter}-${crossSectionValue?.toFixed(2)}.png`
      }

      // Use Plotly's toImage function
      // @ts-ignore - Plotly is added to window by react-plotly
      if (window.Plotly && typeof window.Plotly.toImage === "function") {
        window.Plotly.toImage(plotElement, {
          format: "png",
          width: 1200,
          height: 800
        })
          .then((dataUrl: string) => {
            const link = document.createElement("a")
            link.download = filename
            link.href = dataUrl
            link.click()
          })
          .catch((err: any) => {
            console.error("Error generating plot image:", err)
          })
      } else {
        console.error("Plotly.toImage function not available")
      }
    } catch (error) {
      console.error("Error in handleDownload:", error)
    }
  }

  // Function to refresh the plot
  const handleRefresh = () => {
    // Force re-computation of surfaceData
    setResolution(resolution => resolution + 1)
    setTimeout(() => setResolution(resolution => resolution - 1), 10)
  }

  // No color scale change effect needed - using fixed color scale

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="space-y-1">
          <CardTitle className="flex items-center text-lg">
            {viewMode === "3d" ? (
              <Layers className="text-primary mr-2 size-5" />
            ) : (
              <Scissors className="text-primary mr-2 size-5" />
            )}
            {viewMode === "3d" ? "Surface Analysis" : "Cross-Section Analysis"}
          </CardTitle>
          <CardDescription>
            {viewMode === "3d"
              ? "Analyze how two parameters influence target variables"
              : `View a cross-section along the ${crossSectionAxis === "x" ? xParameter : yParameter} axis`}
          </CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            disabled={!surfaceData}
          >
            <Download className="mr-2 size-4" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-3">
          {/* Parameter selection */}
          <div className="space-y-2">
            <Label htmlFor="x-parameter">X-Axis Parameter</Label>
            <Select value={xParameter} onValueChange={setXParameter}>
              <SelectTrigger id="x-parameter">
                <SelectValue placeholder="Select parameter" />
              </SelectTrigger>
              <SelectContent>
                {parameterNames.map(param => (
                  <SelectItem key={param} value={param}>
                    {param}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="y-parameter">Y-Axis Parameter</Label>
            <Select value={yParameter} onValueChange={setYParameter}>
              <SelectTrigger id="y-parameter">
                <SelectValue placeholder="Select parameter" />
              </SelectTrigger>
              <SelectContent>
                {parameterNames.map(param => (
                  <SelectItem key={param} value={param}>
                    {param}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="target-select">Target Variable</Label>
            {allAvailableTargets.length > 1 ? (
              <Select value={selectedTarget} onValueChange={setSelectedTarget}>
                <SelectTrigger id="target-select">
                  <SelectValue placeholder="Select target" />
                </SelectTrigger>
                <SelectContent>
                  {allAvailableTargets.map(target => (
                    <SelectItem key={`target-${target}`} value={target}>
                      {target}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <div className="text-muted-foreground rounded-md border px-3 py-2 text-sm">
                {allAvailableTargets[0] || targetName}
              </div>
            )}
          </div>
        </div>

        <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-3">
          {/* Plot configuration */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="resolution">Resolution</Label>
              <span className="text-muted-foreground text-xs">
                {resolution}×{resolution}
              </span>
            </div>
            <Slider
              id="resolution"
              min={10}
              max={50}
              step={5}
              value={[resolution]}
              onValueChange={values => setResolution(values[0])}
            />
          </div>

          <div className="flex items-center justify-between space-x-2">
            <div className="flex items-center space-x-2">
              <Switch
                id="smoothing"
                checked={smoothing}
                onCheckedChange={setSmoothing}
              />
              <Label htmlFor="smoothing">Smoothing</Label>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              {isLoading ? (
                <RefreshCw className="size-4 animate-spin" />
              ) : (
                <RefreshCw className="size-4" />
              )}
              <span className="sr-only">Refresh</span>
            </Button>
          </div>

          <div className="flex items-center justify-between space-x-2">
            <div className="flex items-center space-x-2">
              <Switch
                id="cross-section"
                checked={showCrossSection}
                onCheckedChange={value => {
                  setShowCrossSection(value)
                  setViewMode(value ? "cross-section" : "3d")
                }}
              />
              <Label htmlFor="cross-section">Cross-Section</Label>
            </div>

            {showCrossSection && (
              <Select
                value={crossSectionAxis}
                onValueChange={value => setCrossSectionAxis(value as "x" | "y")}
              >
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="x">X-Axis</SelectItem>
                  <SelectItem value="y">Y-Axis</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </div>

        {/* Cross-section slider */}
        {showCrossSection && surfaceData && (
          <div className="mb-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="cross-section-value">
                {crossSectionAxis === "x"
                  ? `${xParameter} Value`
                  : `${yParameter} Value`}
              </Label>
              <span className="text-muted-foreground text-xs">
                {crossSectionValue?.toFixed(2) || "Not set"}
              </span>
            </div>
            <Slider
              id="cross-section-value"
              min={
                crossSectionAxis === "x"
                  ? Math.min(...surfaceData.x)
                  : Math.min(...surfaceData.y)
              }
              max={
                crossSectionAxis === "x"
                  ? Math.max(...surfaceData.x)
                  : Math.max(...surfaceData.y)
              }
              step={
                (crossSectionAxis === "x"
                  ? Math.max(...surfaceData.x) - Math.min(...surfaceData.x)
                  : Math.max(...surfaceData.y) - Math.min(...surfaceData.y)) /
                20
              }
              value={
                crossSectionValue !== null
                  ? [crossSectionValue]
                  : [
                      crossSectionAxis === "x"
                        ? (Math.max(...surfaceData.x) +
                            Math.min(...surfaceData.x)) /
                          2
                        : (Math.max(...surfaceData.y) +
                            Math.min(...surfaceData.y)) /
                          2
                    ]
              }
              onValueChange={values => setCrossSectionValue(values[0])}
            />
          </div>
        )}

        {/* Plot container */}
        <div className="h-[500px] w-full rounded-md border">
          {surfaceData ? (
            viewMode === "3d" ? (
              /* 3D Surface Plot */
              <Plot
                // @ts-ignore - Ignoring type issues with the Plot component
                data={surfaceData.surfaces
                  .map((surface, index) =>
                    surface
                      ? {
                          type: "surface",
                          x: surfaceData.x,
                          y: surfaceData.y,
                          z: surface.zGrid,
                          colorscale: colorScale,
                          opacity: surfaceData.surfaces.length > 1 ? 0.7 : 1,
                          showscale: index === 0, // Only show color scale for the first surface
                          contours: {
                            z: {
                              show: true,
                              usecolormap: true,
                              highlightcolor: "#fff",
                              project: { z: true }
                            }
                          },
                          name: surface.targetName,
                          hovertemplate:
                            `${xParameter}: %{x}<br>` +
                            `${yParameter}: %{y}<br>` +
                            `${surface.targetName}: %{z}<extra></extra>`
                        }
                      : null
                  )
                  .filter(Boolean)}
                layout={{
                  title: `Effect of ${xParameter} and ${yParameter} on Target Variables`,
                  autosize: true,
                  margin: { l: 65, r: 50, b: 65, t: 90 },
                  scene: {
                    xaxis: { title: xParameter },
                    yaxis: { title: yParameter },
                    zaxis: { title: "Target Value" },
                    camera: {
                      eye: { x: 1.5, y: 1.5, z: 1 }
                    }
                  },
                  legend: {
                    x: 0,
                    y: 1,
                    traceorder: "normal",
                    bgcolor: "rgba(255,255,255,0.5)"
                  }
                }}
                config={{
                  displayModeBar: true,
                  responsive: true
                }}
                style={{ width: "100%", height: "100%" }}
              />
            ) : /* Cross-Section Plot */
            surfaceData.crossSections &&
              surfaceData.crossSections.length > 0 ? (
              <Plot
                // @ts-ignore - Ignoring type issues with the Plot component
                data={surfaceData.crossSections.map((section: any) => ({
                  type: "scatter",
                  x: section.axis,
                  y: section.values,
                  mode: "lines+markers",
                  name: section.targetName,
                  line: { width: 3 },
                  marker: { size: 6 }
                }))}
                layout={{
                  title: `Cross-Section at ${crossSectionAxis === "x" ? xParameter : yParameter} = ${crossSectionValue?.toFixed(2)}`,
                  autosize: true,
                  margin: { l: 65, r: 50, b: 65, t: 90 },
                  xaxis: {
                    title: crossSectionAxis === "x" ? yParameter : xParameter
                  },
                  yaxis: {
                    title: "Target Value"
                  },
                  legend: {
                    x: 0,
                    y: 1,
                    traceorder: "normal",
                    bgcolor: "rgba(255,255,255,0.5)"
                  }
                }}
                config={{
                  displayModeBar: true,
                  responsive: true
                }}
                style={{ width: "100%", height: "100%" }}
              />
            ) : (
              <div className="flex h-full flex-col items-center justify-center p-4 text-center">
                <Scissors className="text-muted-foreground mb-4 size-12" />
                <h3 className="text-lg font-medium">
                  No cross-section data available
                </h3>
                <p className="text-muted-foreground mt-2 max-w-md text-sm">
                  Adjust the cross-section position to view data.
                </p>
              </div>
            )
          ) : (
            <div className="flex h-full flex-col items-center justify-center p-4 text-center">
              <Grid3X3 className="text-muted-foreground mb-4 size-12" />
              <h3 className="text-lg font-medium">
                No data available for plot
              </h3>
              <p className="text-muted-foreground mt-2 max-w-md text-sm">
                {measurements.length < 1
                  ? "No measurements available. Add experimental data to create a plot."
                  : "Select parameters and target variables to visualize their relationship."}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
