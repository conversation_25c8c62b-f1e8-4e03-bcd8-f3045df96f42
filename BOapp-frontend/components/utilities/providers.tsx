/*
This client component provides the providers for the app.
*/

"use client"

import { TooltipProvider } from "@/components/ui/tooltip"
import {
  ThemeProvider as NextThemesProvider,
  ThemeProviderProps
} from "next-themes"
import { LoadingProviderWrapper } from "./loading-provider-wrapper"

export const Providers = ({ children, ...props }: ThemeProviderProps) => {
  return (
    <NextThemesProvider {...props}>
      <TooltipProvider>
        <LoadingProviderWrapper>{children}</LoadingProviderWrapper>
      </TooltipProvider>
    </NextThemesProvider>
  )
}
