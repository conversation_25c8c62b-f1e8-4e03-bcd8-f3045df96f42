"use client"

import { useEffect } from "react"
import { LoadingProvider, useLoading } from "@/contexts/loading-context"
import { LoadingOverlay } from "@/components/ui/loading-overlay"
import { initializeLoadingFunctions } from "@/lib/api/loading-fetch"

function LoadingInitializer() {
  const { startLoading, stopLoading } = useLoading()

  useEffect(() => {
    // Initialize the loading functions for the fetchWithLoading wrapper
    initializeLoadingFunctions(startLoading, stopLoading)
  }, [startLoading, stopLoading])

  return null
}

export function LoadingProviderWrapper({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <LoadingProvider>
      <LoadingInitializer />
      <LoadingOverlay />
      {children}
    </LoadingProvider>
  )
}
