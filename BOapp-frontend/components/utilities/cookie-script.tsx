"use client"

import Script from "next/script"

interface CookieScriptProps {
  cookieScriptId?: string
  enableGoogleConsentMode?: boolean
  enableDebugMode?: boolean
}

export function CookieScript({
  cookieScriptId,
  enableGoogleConsentMode = true,
  enableDebugMode = false
}: CookieScriptProps) {
  // Get CookieScript ID from environment variable or props
  const scriptId = cookieScriptId || process.env.NEXT_PUBLIC_COOKIESCRIPT_ID

  // Don't render if no script ID is provided
  if (!scriptId || scriptId === "your_cookiescript_id_here") {
    if (enableDebugMode) {
      console.warn(
        "CookieScript: No valid script ID provided. Please set NEXT_PUBLIC_COOKIESCRIPT_ID in your environment variables."
      )
    }
    return null
  }

  return (
    <>
      {/* Google Consent Mode initialization (if enabled) */}
      {enableGoogleConsentMode && (
        <Script
          id="google-consent-mode-init"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}

              // Set default consent state
              gtag('consent', 'default', {
                'analytics_storage': 'denied',
                'ad_storage': 'denied',
                'ad_user_data': 'denied',
                'ad_personalization': 'denied',
                'functionality_storage': 'granted',
                'security_storage': 'granted'
              });
            `
          }}
        />
      )}

      {/* CookieScript widget */}
      <Script
        id="cookiescript"
        src={`https://cdn.cookie-script.com/s/${scriptId}.js`}
        strategy="afterInteractive"
        onLoad={() => {
          if (enableDebugMode) {
            console.log("CookieScript loaded successfully")
          }

          // Optional: Add custom event listeners for cookie consent changes
          if (typeof window !== "undefined" && window.CookieScript) {
            // Listen for consent changes
            window.addEventListener("CookieScriptAccept", () => {
              if (enableDebugMode) {
                console.log("CookieScript: User accepted cookies")
              }
            })

            window.addEventListener("CookieScriptReject", () => {
              if (enableDebugMode) {
                console.log("CookieScript: User rejected cookies")
              }
            })
          }
        }}
        onError={e => {
          console.error("Failed to load CookieScript:", e)
        }}
      />
    </>
  )
}
