"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  BarChart,
  ChevronDown,
  ChevronUp,
  Play,
  Pause,
  RefreshCw
} from "lucide-react"
import { OptimizationVisualization } from "./optimization-visualization"
import { cn } from "@/lib/utils"

interface EnhancedVisualizationCardProps {
  title: string
  description: string
  icon: React.ReactNode
  className?: string
}

export function EnhancedVisualizationCard({
  title,
  description,
  icon,
  className
}: EnhancedVisualizationCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  // Handle click outside to collapse
  useEffect(() => {
    if (!isExpanded) return

    const handleClickOutside = (event: MouseEvent) => {
      if (cardRef.current && !cardRef.current.contains(event.target as Node)) {
        setIsExpanded(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isExpanded])

  return (
    <motion.div
      ref={cardRef}
      layout
      className={cn("relative z-10", className)}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{
        layout: { type: "spring", stiffness: 300, damping: 30 },
        opacity: { duration: 0.4 }
      }}
    >
      <Card
        className={cn(
          "h-full overflow-hidden transition-all",
          isExpanded ? "shadow-lg" : "hover:shadow-md"
        )}
      >
        <CardContent
          className={cn(
            "flex flex-col items-center p-6 text-center",
            isExpanded && "pb-0"
          )}
        >
          {/* Card header - always visible */}
          <motion.div layout="position" className="flex flex-col items-center">
            <motion.div
              layout="position"
              className="bg-primary/10 mb-4 rounded-full p-3"
            >
              {icon}
            </motion.div>
            <motion.h3 layout="position" className="mb-2 text-xl font-semibold">
              {title}
            </motion.h3>
            <motion.p layout="position" className="text-muted-foreground">
              {description}
            </motion.p>

            <motion.div layout="position" className="mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="group text-xs sm:text-sm"
              >
                {isExpanded ? (
                  <>
                    <span className="xs:inline hidden">Hide Visualization</span>
                    <span className="xs:hidden">Hide</span>
                    <ChevronUp className="ml-1 size-3 transition-transform group-hover:-translate-y-1 sm:ml-2 sm:size-4" />
                  </>
                ) : (
                  <>
                    <span className="xs:inline hidden">Show Visualization</span>
                    <span className="xs:hidden">Show</span>
                    <ChevronDown className="ml-1 size-3 transition-transform group-hover:translate-y-1 sm:ml-2 sm:size-4" />
                  </>
                )}
              </Button>
            </motion.div>
          </motion.div>

          {/* Expanded content with visualization */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                layout
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{
                  opacity: { duration: 0.3 },
                  height: { duration: 0.4 }
                }}
                className="mt-6 w-full"
              >
                <div className="border-t pb-4 pt-6">
                  <OptimizationVisualization />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  )
}
