/*
This client component provides the hero section for the landing page.
*/

"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Section } from "@/components/ui/section"
import { motion, useScroll, useTransform, AnimatePresence } from "framer-motion"
import {
  Rocket,
  LayoutDashboard,
  ArrowRight,
  Zap,
  LineChart,
  Lightbulb,
  Target,
  ExternalLink
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { SignedIn, SignedOut } from "@clerk/nextjs"
import { useRef, useState, useEffect } from "react"
import { useLoading } from "@/contexts/loading-context"
import { BRAND } from "@/lib/constants"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi
} from "@/components/ui/carousel"

// Animation variants
const titleVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.8,
      delay: 0.4,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  }
}

const subtitleVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      delay: 0.6,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  }
}

const buttonVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      delay: 0.8,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  },
  hover: {
    scale: 1.05,
    boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.5)",
    transition: {
      duration: 0.3,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  },
  tap: {
    scale: 0.98,
    boxShadow: "0 5px 15px -5px rgba(59, 130, 246, 0.4)",
    transition: {
      duration: 0.15,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  }
}

const featureBadgeVariants = {
  hidden: { opacity: 0, scale: 0.8, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.5,
      delay: 0.8 + i * 0.1,
      ease: "easeOut"
    }
  }),
  hover: {
    scale: 1.05,
    y: -5,
    boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.2)",
    transition: { duration: 0.2 }
  }
}

// Key features to highlight
const keyFeatures = [
  {
    icon: <Zap className="size-4 text-blue-500" />,
    text: "AI-Powered"
  },
  {
    icon: <LineChart className="size-4 text-indigo-500" />,
    text: "Data-Driven"
  },
  {
    icon: <Lightbulb className="size-4 text-purple-500" />,
    text: "Intelligent"
  },
  {
    icon: <Target className="size-4 text-pink-500" />,
    text: "Precise"
  }
]

// Peer-reviewed publications data with journal information and REAL article titles
const publications = [
  {
    url: "https://doi.org/10.3390/e27010058",
    journal: "Entropy",
    publisher: "MDPI",
    title:
      "Advanced Monte Carlo for Acquisition Sampling in Bayesian Optimization"
  },
  {
    url: "https://pubs.acs.org/doi/10.1021/acs.iecr.4c02915",
    journal: "Ind. Eng. Chem. Res.",
    publisher: "ACS",
    title:
      "Implementation of a Bayesian Optimization Framework for Interconnected Systems"
  },
  {
    url: "https://doi.org/10.1002/bit.28960",
    journal: "Biotechnol. Bioeng.",
    publisher: "Wiley",
    title:
      "Bayesian Optimization in Bioprocess Engineering—Where Do We Stand Today?"
  },
  {
    url: "https://doi.org/10.26434/chemrxiv-2024-44ft2",
    journal: "ChemRxiv",
    publisher: "ChemRxiv",
    title: "Cost-Informed Bayesian Reaction Optimization"
  },
  {
    url: "https://doi.org/10.1007/978-1-0716-3449-3_5",
    journal: "Methods Mol. Biol.",
    publisher: "Springer",
    title: "Bayesian Optimization in Drug Discovery"
  },
  {
    url: "https://doi.org/10.1021/acs.iecr.3c04665",
    journal: "Ind. Eng. Chem. Res.",
    publisher: "ACS",
    title:
      "Kinetic Parameter Estimation of the Polyethylene Process by Bayesian Optimization"
  },
  {
    url: "https://doi.org/10.1021/jacs.4c03789",
    journal: "J. Am. Chem. Soc.",
    publisher: "ACS",
    title:
      "Multivariate Bayesian Optimization of CoO Nanoparticles for CO2 Hydrogenation Catalysis"
  },
  {
    url: "https://doi.org/10.3390/microorganisms12081568",
    journal: "Microorganisms",
    publisher: "MDPI",
    title:
      "Design of Fragrance Formulations with Antiviral Activity Using Bayesian Optimization"
  },
  {
    url: "https://doi.org/10.3390/microorganisms12081569",
    journal: "Microorganisms",
    publisher: "MDPI",
    title:
      "Microbial Diversity of Soil in a Mediterranean Biodiversity Hotspot: Parque Nacional La Campana, Chile"
  },
  {
    url: "https://doi.org/10.1039/d3mh01474f",
    journal: "Mater. Horiz.",
    publisher: "RSC",
    title:
      "PAL 2.0: a physics-driven bayesian optimization framework for material discovery"
  },
  {
    url: "https://doi.org/10.1039/d3sc05607d",
    journal: "Chem. Sci.",
    publisher: "RSC",
    title:
      "Combining Bayesian optimization and automation to simultaneously optimize reaction conditions and routes"
  },
  {
    url: "https://doi.org/10.1002/smll.202309579",
    journal: "Small",
    publisher: "Wiley",
    title:
      "Bayesian Optimization of Environmentally Sustainable Graphene Inks Produced by Wet Jet Milling"
  },
  {
    url: "https://doi.org/10.1038/s41428-024-00923-8",
    journal: "Polym. J.",
    publisher: "Nature",
    title:
      "Development of Pd-immobilized porous polymer catalysts via Bayesian optimization"
  },
  {
    url: "https://doi.org/10.1021/acssuschemeng.4c03253",
    journal: "ACS Sustain. Chem. Eng.",
    publisher: "ACS",
    title:
      "Self-Optimizing Flow Reactions for Sustainability: An Experimental Bayesian Optimization Study"
  },
  {
    url: "https://doi.org/10.1080/27660400.2024.2425178",
    journal: "Chem. Eng. J. Adv.",
    publisher: "Taylor & Francis",
    title:
      "Bayesian optimization of radical polymerization reactions in a flow synthesis system"
  },
  {
    url: "https://doi.org/10.1016/j.compchemeng.2024.108813",
    journal: "Comput. Chem. Eng.",
    publisher: "Elsevier",
    title:
      "Operating condition design with a Bayesian optimization approach for pharmaceutical intermediate batch concentration"
  },
  {
    url: "https://doi.org/10.1016/j.oceram.2024.100705",
    journal: "Open Ceram.",
    publisher: "Elsevier",
    title:
      "Development of aluminum oxide slurries for additive manufacturing by Bayesian optimization"
  },
  {
    url: "https://doi.org/10.3390/ma17205019",
    journal: "Materials",
    publisher: "MDPI",
    title:
      "Leveraging Bayesian Optimization Software for Atomic Layer Deposition: Single-Objective Optimization of TiO2 Layers"
  },
  {
    url: "https://doi.org/10.1039/d4nr00915k",
    journal: "Nanoscale",
    publisher: "RSC",
    title:
      "Bayesian optimization of glycopolymer structures for the interaction with cholera toxin B subunit"
  },
  {
    url: "https://doi.org/10.1039/d4cp02533d",
    journal: "Phys. Chem. Chem. Phys.",
    publisher: "RSC",
    title:
      "First-principles study on the lithiation process of amorphous SiO anode for Li-ion batteries with Bayesian optimization"
  },
  {
    url: "https://doi.org/10.1039/d3ta06651g",
    journal: "J. Mater. Chem. A",
    publisher: "RSC",
    title:
      "Navigating the unknown with AI: multiobjective Bayesian optimization of non-noble acidic OER catalysts"
  },
  {
    url: "https://doi.org/10.1021/acsami.4c14279",
    journal: "ACS Appl. Mater. Interfaces",
    publisher: "ACS",
    title:
      "Continuous Flow Chemistry and Bayesian Optimization for Polymer-Functionalized Carbon Nanotube-Based Chemiresistive Methane Sensors"
  },
  {
    url: "https://doi.org/10.26434/chemrxiv-2024-m0kf5",
    journal: "ChemRxiv",
    publisher: "ChemRxiv",
    title:
      "Optimization of heterogeneous continuous flow hydrogenation using FTIR inline analysis: a comparative study of multi-objective Bayesian optimization and kinetic modeling"
  },
  {
    url: "https://doi.org/10.1016/j.polymer.2023.126554",
    journal: "Polymer",
    publisher: "Elsevier",
    title:
      "Bayesian optimization of HDPE copolymerization process based on polymer product-process integration"
  },
  {
    url: "https://doi.org/10.1016/j.compchemeng.2024.108810",
    journal: "Comput. Chem. Eng.",
    publisher: "Elsevier",
    title:
      "Human-algorithm collaborative Bayesian optimization for engineering systems"
  },
  {
    url: "https://doi.org/10.1002/cctc.202400777",
    journal: "ChemCatChem",
    publisher: "Wiley",
    title:
      "Avoiding Replicates in Biocatalysis Experiments: Machine Learning for Enzyme Cascade Optimization"
  }
]

export const HeroSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null)
  const [isHovering, setIsHovering] = useState(false)
  const [carouselApi, setCarouselApi] = useState<CarouselApi>()
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)
  const router = useRouter()
  const { startLoading } = useLoading()
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"]
  })

  // Handle dashboard navigation with loading overlay
  const handleDashboardClick = (
    e: React.MouseEvent<HTMLAnchorElement>,
    path: string
  ) => {
    e.preventDefault()
    startLoading()

    // Add a small delay before navigation to ensure the loading overlay appears
    setTimeout(() => {
      router.push(path)
    }, 100) // 100ms delay (reduced from 300ms)
  }

  // Parallax effects
  const titleY = useTransform(scrollYProgress, [0, 0.5], [0, -30])

  // Autoplay functionality for carousel
  useEffect(() => {
    if (!carouselApi) return

    const interval = setInterval(() => {
      carouselApi.scrollNext()
    }, 3000) // Auto-scroll every 3 seconds

    return () => clearInterval(interval)
  }, [carouselApi])

  return (
    <Section
      ref={sectionRef}
      className="pb-0 pt-20 md:pt-28"
      containerSize="lg"
      containerClassName="text-center"
    >
      <div className="hero-content flex flex-col items-center justify-center">
        <motion.div
          initial="hidden"
          animate="visible"
          className="mx-auto flex max-w-2xl flex-col items-center justify-center gap-6"
          style={{ y: titleY }}
        >
          {/* Animated badge above title */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-2"
          >
            <span className="bg-primary/10 text-primary inline-flex items-center rounded-full px-3 py-1 text-sm font-medium backdrop-blur-sm">
              <span className="mr-1 flex size-2">
                <span className="bg-primary absolute inline-flex size-2 animate-ping rounded-full opacity-75"></span>
                <span className="bg-primary relative inline-flex size-2 rounded-full"></span>
              </span>
              New: Advanced Optimization Engine
            </span>
          </motion.div>

          <motion.div
            variants={titleVariants}
            className="relative text-balance text-5xl font-bold md:text-7xl"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
          >
            <span className="from-primary bg-gradient-to-r to-blue-600 bg-clip-text text-transparent">
              {BRAND.NAME}
            </span>
            {/* Subtle icon animations on hover */}
            <AnimatePresence>
              {isHovering && (
                <>
                  <motion.div
                    className="absolute -right-6 top-1/2 -translate-y-1/2 text-blue-500/70"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Target className="size-5" />
                  </motion.div>
                  <motion.div
                    className="absolute -left-6 top-1/2 -translate-y-1/2 text-indigo-500/70"
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <LineChart className="size-5" />
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </motion.div>

          <motion.div
            variants={subtitleVariants}
            className="text-muted-foreground max-w-xl text-balance text-lg md:text-xl"
          >
            <span className="text-foreground font-medium">
              Revolutionize your research
            </span>{" "}
            with intelligent experimental design that delivers breakthrough
            results
            <span className="text-blue-500"> up to 70% faster</span>.
          </motion.div>

          {/* Feature badges */}
          <motion.div className="mt-2 flex flex-wrap justify-center gap-2">
            {keyFeatures.map((feature, i) => (
              <motion.div
                key={i}
                custom={i}
                variants={featureBadgeVariants}
                whileHover="hover"
                className="bg-background/50 flex items-center gap-1.5 rounded-full px-3 py-1 text-sm backdrop-blur-sm"
              >
                {feature.icon}
                {feature.text}
              </motion.div>
            ))}
          </motion.div>

          <motion.div variants={buttonVariants} className="mt-2 flex gap-4">
            <SignedOut>
              <Link href="/signup" prefetch={false}>
                <motion.div whileHover="hover" whileTap="tap">
                  <Button className="from-primary hover:from-primary/90 min-w-40 bg-gradient-to-r to-blue-600 px-6 py-5 text-base font-semibold shadow-lg shadow-blue-500/30 hover:to-blue-700 md:min-w-48 md:px-8 md:py-6 md:text-lg">
                    <Rocket className="mr-2 size-5" />
                    Start for Free
                    <ArrowRight className="ml-2 size-4" />
                  </Button>
                </motion.div>
              </Link>
            </SignedOut>

            <SignedIn>
              <Link
                href="/dashboard/optimizations"
                prefetch={false}
                onClick={e =>
                  handleDashboardClick(e, "/dashboard/optimizations")
                }
              >
                <motion.div whileHover="hover" whileTap="tap">
                  <Button className="from-primary hover:from-primary/90 min-w-40 bg-gradient-to-r to-blue-600 px-6 py-5 text-base font-semibold shadow-lg shadow-blue-500/30 hover:to-blue-700 md:min-w-48 md:px-8 md:py-6 md:text-lg">
                    <LayoutDashboard className="mr-2 size-5" />
                    Go to Dashboard
                  </Button>
                </motion.div>
              </Link>
            </SignedIn>
          </motion.div>

          {/* Publications section */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.2 }}
            className="text-muted-foreground mt-6 max-w-5xl text-sm"
          >
            <div className="mb-6 text-center">
              Bayesian Optimization often outperforms DOE and is well trusted by
              many leading academic institutions
              <br />
              Peer-reviewed publications can be found here
            </div>

            {/* Publications carousel */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.4 }}
              className="relative w-full"
            >
              {/* Clean container with space for tooltips */}
              <div className="relative overflow-visible">
                <Carousel
                  setApi={setCarouselApi}
                  opts={{
                    align: "start",
                    loop: true
                  }}
                  className="relative w-full py-4"
                >
                  <CarouselContent className="-ml-3 overflow-visible px-20 md:-ml-4">
                    {publications.map((pub, index) => (
                      <CarouselItem
                        key={index}
                        className="basis-1/2 pl-3 md:basis-1/3 md:pl-4 lg:basis-1/4 xl:basis-1/5"
                      >
                        <div className="relative">
                          <motion.a
                            href={pub.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block"
                            whileHover={{ y: -2 }}
                            whileTap={{ y: 0 }}
                            transition={{ duration: 0.2, ease: "easeOut" }}
                            onMouseEnter={() => setHoveredCard(index)}
                            onMouseLeave={() => setHoveredCard(null)}
                          >
                            <div className="relative h-20 overflow-hidden rounded-lg bg-gradient-to-br from-white via-gray-50/50 to-gray-100/30 p-3 shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/10">
                              {/* Publisher name watermark - embedded in background */}
                              <div className="absolute inset-0 flex items-start justify-center pt-2">
                                <div
                                  className={`select-none text-xs font-black uppercase tracking-wider transition-colors duration-300 ${
                                    hoveredCard === index
                                      ? "text-foreground/15"
                                      : "text-foreground/10"
                                  }`}
                                >
                                  {pub.publisher}
                                </div>
                              </div>

                              {/* Gradient overlay for depth */}
                              <div
                                className={`from-primary/5 absolute inset-0 bg-gradient-to-br via-transparent to-blue-500/5 transition-opacity duration-300 ${
                                  hoveredCard === index
                                    ? "opacity-100"
                                    : "opacity-0"
                                }`}
                              />

                              {/* Content container */}
                              <div className="relative z-10 flex h-full flex-col justify-between">
                                {/* Top section */}
                                <div className="flex items-start justify-end">
                                  {/* External link indicator */}
                                  <div
                                    className={`transition-colors duration-300 ${
                                      hoveredCard === index
                                        ? "text-primary/70"
                                        : "text-muted-foreground/50"
                                    }`}
                                  >
                                    <ExternalLink className="size-3" />
                                  </div>
                                </div>

                                {/* Journal name - prominent */}
                                <div className="mt-auto">
                                  <h3 className="text-foreground line-clamp-2 text-sm font-bold leading-tight tracking-tight">
                                    {pub.journal}
                                  </h3>
                                </div>
                              </div>
                            </div>
                          </motion.a>
                        </div>
                      </CarouselItem>
                    ))}
                  </CarouselContent>
                </Carousel>
              </div>

              {/* Title Display Area - Below Carousel */}
              <div className="relative flex h-16 w-full items-center justify-center">
                {hoveredCard !== null && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    transition={{ duration: 0.3 }}
                    className="pointer-events-none z-50 flex w-full justify-center"
                  >
                    <div className="text-muted-foreground max-w-2xl px-4 text-center text-sm">
                      {publications[hoveredCard]?.title}
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </Section>
  )
}
