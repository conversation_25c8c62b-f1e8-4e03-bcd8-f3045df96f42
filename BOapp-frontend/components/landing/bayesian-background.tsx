"use client"

import { useEffect, useRef } from "react"

// Using a key to ensure consistent rendering
export const BayesianBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas size to match container with proper aspect ratio
    const resizeCanvas = () => {
      // Get the parent container dimensions
      const parentWidth = canvas.parentElement?.clientWidth || window.innerWidth

      // Calculate height based on a 16:9 aspect ratio (or adjust as needed)
      // For hero section, we want a taller ratio like 16:10 or 16:11
      const aspectRatio = 16 / 11
      const calculatedHeight = parentWidth / aspectRatio

      // Set canvas dimensions
      canvas.width = parentWidth
      canvas.height = calculatedHeight
    }

    // Call resize initially and on window resize
    resizeCanvas()
    window.addEventListener("resize", resizeCanvas)

    // Simple Gaussian kernel function
    function gaussianKernel(
      x1: number,
      y1: number,
      x2: number,
      y2: number,
      lengthScale = 100
    ) {
      const dx = x1 - x2
      const dy = y1 - y2
      const distance2 = dx * dx + dy * dy
      return Math.exp(-distance2 / (2 * lengthScale * lengthScale))
    }

    // Class to handle Gaussian Process calculations
    class GaussianProcess {
      lengthScale: number
      noiseLevel: number
      trainingPoints: [number, number][]
      trainingValues: number[]
      initialized: boolean

      constructor(lengthScale = 100, noiseLevel = 0.1) {
        this.lengthScale = lengthScale
        this.noiseLevel = noiseLevel
        this.trainingPoints = []
        this.trainingValues = []
        this.initialized = false
      }

      // Add a new observation
      addObservation(x: number, y: number, value: number) {
        this.trainingPoints.push([x, y])
        this.trainingValues.push(value)
        this.initialized = true
      }

      // Predict value and uncertainty at a point
      predict(x: number, y: number): [number, number] {
        if (!this.initialized || this.trainingPoints.length === 0) {
          return [0, 1] // Default value and max uncertainty
        }

        // Calculate kernel vector between test point and all training points
        const k_star = this.trainingPoints.map(point =>
          gaussianKernel(x, y, point[0], point[1], this.lengthScale)
        )

        // Calculate kernel matrix for training points
        const n = this.trainingPoints.length
        const K: number[][] = Array(n)
          .fill(0)
          .map(() => Array(n).fill(0))

        for (let i = 0; i < n; i++) {
          for (let j = 0; j <= i; j++) {
            const kValue = gaussianKernel(
              this.trainingPoints[i][0],
              this.trainingPoints[i][1],
              this.trainingPoints[j][0],
              this.trainingPoints[j][1],
              this.lengthScale
            )
            K[i][j] = kValue
            K[j][i] = kValue // Symmetric matrix
          }
          // Add noise to diagonal
          K[i][i] += this.noiseLevel
        }

        // Simple matrix operations (for a full implementation, use a proper linear algebra library)
        // This is a simplified version for demonstration

        // Calculate mean prediction (simplified)
        let meanPrediction = 0
        for (let i = 0; i < n; i++) {
          meanPrediction +=
            (k_star[i] * this.trainingValues[i]) / (n * (1 + this.noiseLevel))
        }

        // Calculate variance (simplified)
        // In a real implementation, this would involve matrix inversion
        let variance = 1
        if (n > 0) {
          // Simplified variance calculation
          let sumKernel = 0
          for (let i = 0; i < n; i++) {
            sumKernel += k_star[i]
          }
          variance = 1 - sumKernel / (n * (1 + this.lengthScale))
          variance = Math.max(0.05, Math.min(1, variance)) // Clamp between 0.05 and 1
        }

        return [meanPrediction, variance]
      }
    }

    // Create a Gaussian Process instance
    const gp = new GaussianProcess(150, 0.1)

    // Add some initial observations
    function addInitialObservations() {
      if (!canvas) return

      // Add a few random points
      for (let i = 0; i < 5; i++) {
        const x = Math.random() * canvas.width
        const y = Math.random() * canvas.height
        // Create an interesting function to sample from
        const value =
          Math.sin(x / 100) * Math.cos(y / 100) + Math.sin((x + y) / 200)
        gp.addObservation(x, y, value)
      }
    }

    // Sample points to visualize
    const gridSize = 30 // Adjust for performance
    let gridPoints: [number, number][] = []

    function generateGridPoints() {
      if (!canvas) return

      gridPoints = []
      const stepX = canvas.width / gridSize
      const stepY = canvas.height / gridSize

      for (let i = 0; i <= gridSize; i++) {
        for (let j = 0; j <= gridSize; j++) {
          const x = i * stepX
          const y = j * stepY
          gridPoints.push([x, y])
        }
      }
    }

    // Animation variables
    let animationFrame = 0
    const maxAnimationFrames = 10000 // Much longer cycle to avoid noticeable repeats

    interface SamplingPoint {
      x: number
      y: number
      value: number
      age: number
      rippleSize: number
      rippleOpacity: number
    }

    let samplingPoints: SamplingPoint[] = []

    // Add a new sampling point occasionally
    function addNewSamplingPoint() {
      // Reduce frequency of new points to avoid too many overlapping ripples
      if (Math.random() < 0.02 || samplingPoints.length < 6) {
        // Create a new point with even distribution across the canvas
        // Use a different approach for each new point to ensure good coverage
        let x, y

        // Divide the canvas into a grid and place points in different cells
        const gridCells = 4 // 4x4 grid = 16 cells
        const cellWidth = canvas?.width ? canvas.width / gridCells : 100
        const cellHeight = canvas?.height ? canvas.height / gridCells : 100

        // Find a cell that doesn't have a recent point
        let cellX: number = 0
        let cellY: number = 0
        let attempts = 0
        let foundEmptyCell = false

        while (!foundEmptyCell && attempts < 20) {
          cellX = Math.floor(Math.random() * gridCells)
          cellY = Math.floor(Math.random() * gridCells)

          // Check if this cell already has a recent point
          const hasRecentPoint = samplingPoints.some(point => {
            const pointCellX = Math.floor(point.x / cellWidth)
            const pointCellY = Math.floor(point.y / cellHeight)
            return (
              pointCellX === cellX && pointCellY === cellY && point.age < 30
            )
          })

          if (!hasRecentPoint) {
            foundEmptyCell = true
          }

          attempts++
        }

        // Position within the cell with some randomness
        x = (cellX + 0.3 + Math.random() * 0.4) * cellWidth
        y = (cellY + 0.3 + Math.random() * 0.4) * cellHeight

        // Create a value for visualization
        const trueValue =
          Math.sin(x / 100) * Math.cos(y / 100) + Math.sin((x + y) / 200)

        // Add to GP model
        gp.addObservation(x, y, trueValue)

        // Add to visualization with animation properties
        samplingPoints.push({
          x: x,
          y: y,
          value: trueValue,
          age: 0,
          rippleSize: 0,
          rippleOpacity: 1
        })
      }

      // Update existing sampling points
      samplingPoints.forEach(point => {
        point.age += 1
        // Slower expansion of ripples
        point.rippleSize = Math.min(80, point.age * 0.8)
        // Much slower fade-out for longer-lasting ripples
        point.rippleOpacity = Math.max(0, 1 - point.age / 100)
      })

      // Remove old points from animation (but keep them in the GP model)
      samplingPoints = samplingPoints.filter(point => point.rippleOpacity > 0)
    }

    // Create a continuous, seamless Gaussian process curve
    // Store previous curve state to create smooth transitions
    const prevCurveState: { y: number; uncertainty: number }[] = Array(
      100
    ).fill({ y: 0, uncertainty: 0 })

    // Draw a Gaussian process curve that crosses horizontally
    function drawGPCurve() {
      if (!ctx) return

      const time = animationFrame / 100
      const curvePoints: { x: number; y: number; uncertainty: number }[] = []

      // Generate points for the curve
      const numPoints = 100
      for (let i = 0; i < numPoints; i++) {
        const x = (i / (numPoints - 1)) * (canvas?.width || 800)

        // Create a dynamic curve that changes over time
        // Base curve with multiple sine waves of different frequencies
        const baseCurve =
          Math.sin(i / 10 + time * 0.2) * 50 +
          Math.sin(i / 20 + time * 0.1) * 30 +
          Math.sin(i / 5 - time * 0.15) * 20

        // Add some noise that changes over time
        const noise = Math.sin(i * 0.3 + time) * 10

        // Calculate target y position (centered vertically)
        const targetY =
          (canvas?.height ? canvas.height / 2 : 300) + baseCurve + noise

        // Calculate target uncertainty (varies along the curve)
        const targetUncertainty =
          20 +
          Math.sin(i / 15 + time * 0.3) * 15 +
          Math.cos(i / 8 - time * 0.2) * 10

        // Smooth transition from previous state (interpolation)
        // This creates a continuous flow without visible restarts
        const transitionSpeed = 0.03 // Lower = smoother but slower transitions

        // Interpolate between previous and target values
        const y =
          prevCurveState[i].y +
          (targetY - prevCurveState[i].y) * transitionSpeed
        const uncertainty =
          prevCurveState[i].uncertainty +
          (targetUncertainty - prevCurveState[i].uncertainty) * transitionSpeed

        // Store the new state for next frame
        prevCurveState[i] = { y, uncertainty }

        curvePoints.push({ x, y, uncertainty })
      }

      // Draw uncertainty region (semi-transparent area)
      ctx.beginPath()

      // Top boundary of uncertainty region
      for (let i = 0; i < curvePoints.length; i++) {
        const point = curvePoints[i]
        if (i === 0) {
          ctx.moveTo(point.x, point.y - point.uncertainty)
        } else {
          ctx.lineTo(point.x, point.y - point.uncertainty)
        }
      }

      // Bottom boundary (in reverse)
      for (let i = curvePoints.length - 1; i >= 0; i--) {
        const point = curvePoints[i]
        ctx.lineTo(point.x, point.y + point.uncertainty)
      }

      // Create gradient for uncertainty region
      const gradient = ctx.createLinearGradient(0, 0, canvas?.width || 800, 0)
      gradient.addColorStop(0, "rgba(79, 70, 229, 0.05)") // Indigo (more transparent)
      gradient.addColorStop(0.5, "rgba(59, 130, 246, 0.05)") // Blue (more transparent)
      gradient.addColorStop(1, "rgba(79, 70, 229, 0.05)") // Indigo (more transparent)

      ctx.fillStyle = gradient
      ctx.fill()

      // Draw the main curve
      ctx.beginPath()
      for (let i = 0; i < curvePoints.length; i++) {
        const point = curvePoints[i]
        if (i === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          ctx.lineTo(point.x, point.y)
        }
      }

      // Create gradient for the curve
      const curveGradient = ctx.createLinearGradient(
        0,
        0,
        canvas?.width || 800,
        0
      )
      curveGradient.addColorStop(0, "rgba(79, 70, 229, 0.3)") // Indigo (more transparent)
      curveGradient.addColorStop(0.5, "rgba(59, 130, 246, 0.3)") // Blue (more transparent)
      curveGradient.addColorStop(1, "rgba(79, 70, 229, 0.3)") // Indigo (more transparent)

      ctx.strokeStyle = curveGradient
      ctx.lineWidth = 1.5
      ctx.stroke()
    }

    // Draw sampling points with ripple effects
    function drawSamplingPoints() {
      if (!ctx) return

      samplingPoints.forEach(point => {
        // Draw ripple effect
        ctx.beginPath()
        ctx.arc(point.x, point.y, point.rippleSize, 0, Math.PI * 2)
        ctx.strokeStyle = `rgba(99, 102, 241, ${point.rippleOpacity * 0.4})`
        ctx.lineWidth = 1.5
        ctx.stroke()

        // Draw point
        ctx.beginPath()
        ctx.arc(point.x, point.y, 4, 0, Math.PI * 2)
        ctx.fillStyle = "rgba(79, 70, 229, 0.3)" // Indigo color from Tailwind (even more transparent)
        ctx.fill()
      })
    }

    // Draw static dots
    function drawStaticDots() {
      if (!ctx) return

      // Create a few static dots
      const dotCount = 30
      const time = animationFrame / 100

      for (let i = 0; i < dotCount; i++) {
        // Position dots evenly across the entire space
        // Use a pseudo-random but deterministic distribution
        const x =
          (canvas?.width || 800) * ((Math.sin(i * 3.7 + time * 0.01) + 1) / 2)
        const y =
          (canvas?.height || 600) * ((Math.cos(i * 2.9 + time * 0.01) + 1) / 2)

        // Draw dot
        ctx.beginPath()
        ctx.arc(x, y, 3, 0, Math.PI * 2)
        ctx.fillStyle =
          i % 3 === 0 ? "rgba(59, 130, 246, 0.2)" : "rgba(99, 102, 241, 0.15)"
        ctx.fill()
      }
    }

    // Main animation loop
    function animate() {
      if (!ctx) return

      // Clear canvas with a semi-transparent overlay to create trail effect
      ctx.fillStyle = "rgba(255, 255, 255, 0.2)"
      ctx.fillRect(0, 0, canvas?.width || 800, canvas?.height || 600)

      // Add new sampling point occasionally
      addNewSamplingPoint()

      // Draw the visualization elements
      drawGPCurve()
      drawStaticDots()
      drawSamplingPoints()

      // Update animation frame
      animationFrame = (animationFrame + 1) % maxAnimationFrames

      // Continue animation loop
      requestAnimationFrame(animate)
    }

    // Initialize and start animation
    function init() {
      addInitialObservations()
      generateGridPoints()

      // Start animation
      animate()

      // Add interaction - add new points on click
      if (canvas) {
        canvas.addEventListener("click", event => {
          const rect = canvas.getBoundingClientRect()
          const x = event.clientX - rect.left
          const y = event.clientY - rect.top

          // Create an interesting function to sample from
          const value =
            Math.sin(x / 100) * Math.cos(y / 100) + Math.sin((x + y) / 200)

          // Add to GP model
          gp.addObservation(x, y, value)

          // Add to visualization with animation properties
          samplingPoints.push({
            x: x,
            y: y,
            value: value,
            age: 0,
            rippleSize: 0,
            rippleOpacity: 1
          })
        })
      }
    }

    // Start everything
    init()

    // Cleanup on unmount
    return () => {
      window.removeEventListener("resize", resizeCanvas)
    }
  }, [])

  return (
    <canvas
      ref={canvasRef}
      className="bg-background absolute left-0 top-0 -z-10 w-full object-cover"
      style={{
        maxHeight: "100%",
        display: "block"
      }}
      aria-hidden="true"
      suppressHydrationWarning={true}
    />
  )
}

export default BayesianBackground
