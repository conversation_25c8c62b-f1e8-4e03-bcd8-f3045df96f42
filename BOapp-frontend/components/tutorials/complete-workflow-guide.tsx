"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>aker,
  BarChart3,
  Target,
  Database,
  Brain,
  CheckCircle,
  ArrowRight,
  Play,
  Download,
  RefreshCw,
  TrendingUp,
  Lightbulb,
  AlertCircle,
  FileText,
  Layers
} from "lucide-react"

export function CompleteWorkflowGuide() {
  return (
    <div className="space-y-8">
      {/* Introduction */}
      <section>
        <h2 className="mb-4 text-2xl font-bold">
          Complete Workflow Guide: From Setup to Results
        </h2>
        <p className="mb-4">
          This comprehensive guide walks you through the entire process of using
          INNOptimizer™ for real optimization projects. You'll learn the
          practical steps, best practices, and how to interpret results
          effectively.
        </p>

        <div className="mb-6 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/20">
          <div className="flex items-start gap-3">
            <Lightbulb className="mt-0.5 size-5 text-blue-600 dark:text-blue-400" />
            <div>
              <h4 className="mb-1 font-semibold text-blue-900 dark:text-blue-100">
                What You'll Learn
              </h4>
              <ul className="space-y-1 text-sm text-blue-800 dark:text-blue-200">
                <li>• How to set up your first optimization project</li>
                <li>
                  • Best practices for parameter definition and initial sampling
                </li>
                <li>• Running experiments and interpreting AI suggestions</li>
                <li>• Analyzing results and making data-driven decisions</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Workflow Overview */}
      <section>
        <h3 className="mb-4 text-xl font-bold">Workflow Overview</h3>
        <p className="mb-6">
          The INNOptimizer™ workflow consists of four main phases, each
          building on the previous one:
        </p>

        <div className="mb-8 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Settings className="size-5 text-blue-500" />
                <CardTitle className="text-lg">1. Setup</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                Define parameters, targets, and optimization configuration
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Database className="size-5 text-green-500" />
                <CardTitle className="text-lg">2. Initialize</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                Generate initial samples to train the optimization model
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Beaker className="size-5 text-purple-500" />
                <CardTitle className="text-lg">3. Experiment</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                Get AI suggestions, run experiments, and add measurements
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <BarChart3 className="size-5 text-orange-500" />
                <CardTitle className="text-lg">4. Analyze</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                Interpret results, identify optimal parameters, and export data
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Detailed Workflow */}
      <section>
        <h3 className="mb-4 text-xl font-bold">Detailed Step-by-Step Guide</h3>

        <Tabs defaultValue="setup" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="setup">Setup</TabsTrigger>
            <TabsTrigger value="initialize">Initialize</TabsTrigger>
            <TabsTrigger value="experiment">Experiment</TabsTrigger>
            <TabsTrigger value="analyze">Analyze</TabsTrigger>
          </TabsList>

          <TabsContent value="setup" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="size-5" />
                  Phase 1: Setting Up Your Optimization
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="mb-3 font-semibold">
                    1.1 Create New Optimization
                  </h4>
                  <p className="mb-3">
                    Navigate to{" "}
                    <strong>
                      Dashboard → Optimizations → New Optimization
                    </strong>{" "}
                    to start the creation wizard.
                  </p>

                  <div className="mb-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-900">
                    <h5 className="mb-2 font-medium">Basic Information:</h5>
                    <ul className="space-y-1 text-sm">
                      <li>
                        • <strong>Name:</strong> Choose a descriptive name
                        (e.g., "Catalyst Temperature Optimization")
                      </li>
                      <li>
                        • <strong>Description:</strong> Add context about your
                        optimization goals
                      </li>
                    </ul>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-semibold">1.2 Define Parameters</h4>
                  <p className="mb-3">
                    Parameters are the variables you want to optimize.
                    INNOptimizer™ supports:
                  </p>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-950/20">
                      <h5 className="mb-2 font-medium text-blue-900 dark:text-blue-100">
                        Numerical Parameters
                      </h5>
                      <ul className="space-y-1 text-sm text-blue-800 dark:text-blue-200">
                        <li>
                          • <strong>Discrete:</strong> Specific values (e.g.,
                          50, 60, 70°C)
                        </li>
                        <li>
                          • <strong>Continuous:</strong> Range with bounds
                          (e.g., 50-100°C)
                        </li>
                      </ul>
                    </div>

                    <div className="rounded-lg bg-green-50 p-4 dark:bg-green-950/20">
                      <h5 className="mb-2 font-medium text-green-900 dark:text-green-100">
                        Categorical Parameters
                      </h5>
                      <ul className="space-y-1 text-sm text-green-800 dark:text-green-200">
                        <li>
                          • <strong>Options:</strong> Discrete choices (e.g.,
                          Catalyst A, B, C)
                        </li>
                        <li>
                          • <strong>Encoding:</strong> One-Hot or Ordinal
                          encoding
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-semibold">1.3 Configure Target</h4>
                  <p className="mb-3">Define what you want to optimize:</p>

                  <div className="rounded-lg bg-yellow-50 p-4 dark:bg-yellow-950/20">
                    <ul className="space-y-2 text-sm">
                      <li>
                        • <strong>Target Name:</strong> What you're measuring
                        (e.g., "Yield", "Efficiency")
                      </li>
                      <li>
                        • <strong>Optimization Mode:</strong> Maximize (MAX) or
                        Minimize (MIN)
                      </li>
                      <li>
                        • <strong>Multi-objective:</strong> Optimize multiple
                        targets simultaneously
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="flex items-center gap-2 rounded-lg bg-green-50 p-4 dark:bg-green-950/20">
                  <CheckCircle className="size-5 text-green-600" />
                  <span className="font-medium text-green-800 dark:text-green-200">
                    Best Practice: Start with 2-5 parameters for your first
                    optimization
                  </span>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="initialize" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="size-5" />
                  Phase 2: Initializing with Sample Data
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="mb-3 font-semibold">
                    2.1 Why Initial Samples Matter
                  </h4>
                  <p className="mb-4">
                    Before the AI can make intelligent suggestions, it needs
                    some initial data to understand your experimental space.
                    This is like giving the AI a "taste" of your problem.
                  </p>

                  <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/20">
                    <div className="flex items-start gap-3">
                      <Brain className="mt-0.5 size-5 text-blue-600 dark:text-blue-400" />
                      <div>
                        <h5 className="mb-1 font-semibold text-blue-900 dark:text-blue-100">
                          How It Works
                        </h5>
                        <p className="text-sm text-blue-800 dark:text-blue-200">
                          The system uses Latin Hypercube Sampling (LHS) to
                          generate well-distributed initial points across your
                          parameter space. This ensures good coverage and helps
                          the AI learn faster.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-semibold">
                    2.2 Generate Initial Samples
                  </h4>
                  <p className="mb-3">
                    Navigate to your optimization and click{" "}
                    <strong>"Run Experiments"</strong>, then go to the{" "}
                    <strong>"Sample Generation"</strong> tab.
                  </p>

                  <div className="space-y-4">
                    <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-900">
                      <h5 className="mb-2 font-medium">
                        Sample Generation Options:
                      </h5>
                      <ul className="space-y-2 text-sm">
                        <li>
                          • <strong>Number of Samples:</strong> Typically 5-10
                          for initial exploration
                        </li>
                        <li>
                          • <strong>Sampling Method:</strong> Latin Hypercube
                          Sampling (recommended)
                        </li>
                        <li>
                          • <strong>Sample Class:</strong> Choose "exploratory"
                          for broad coverage
                        </li>
                      </ul>
                    </div>

                    <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-950/20">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="mt-0.5 size-5 text-yellow-600 dark:text-yellow-400" />
                        <div>
                          <h5 className="mb-1 font-semibold text-yellow-900 dark:text-yellow-100">
                            Important
                          </h5>
                          <p className="text-sm text-yellow-800 dark:text-yellow-200">
                            You must run these initial experiments and add the
                            results before the AI can provide intelligent
                            suggestions.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-semibold">
                    2.3 Run Initial Experiments
                  </h4>
                  <p className="mb-3">
                    Take the generated sample points to your lab/process and run
                    the experiments:
                  </p>

                  <ol className="list-inside list-decimal space-y-2 text-sm">
                    <li>Download the sample parameters as CSV or Excel</li>
                    <li>
                      Run experiments using the specified parameter combinations
                    </li>
                    <li>
                      Measure your target values (yield, efficiency, etc.)
                    </li>
                    <li>Return to the platform and add your measurements</li>
                  </ol>
                </div>

                <div className="flex items-center gap-2 rounded-lg bg-green-50 p-4 dark:bg-green-950/20">
                  <CheckCircle className="size-5 text-green-600" />
                  <span className="font-medium text-green-800 dark:text-green-200">
                    Tip: The more accurate your initial measurements, the better
                    the AI suggestions will be
                  </span>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="experiment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Beaker className="size-5" />
                  Phase 3: Running AI-Guided Experiments
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="mb-3 font-semibold">
                    3.1 Getting AI Suggestions
                  </h4>
                  <p className="mb-4">
                    Once you have initial data, the AI can start making
                    intelligent suggestions. Go to the{" "}
                    <strong>"AI Suggestions"</strong> tab in your optimization.
                  </p>

                  <div className="mb-4 rounded-lg bg-purple-50 p-4 dark:bg-purple-950/20">
                    <h5 className="mb-2 font-medium text-purple-900 dark:text-purple-100">
                      Suggestion Options:
                    </h5>
                    <ul className="space-y-1 text-sm text-purple-800 dark:text-purple-200">
                      <li>
                        • <strong>Batch Size:</strong> Number of suggestions
                        (1-100)
                      </li>
                      <li>
                        • <strong>Sequential:</strong> Get one suggestion, run
                        experiment, repeat
                      </li>
                      <li>
                        • <strong>Parallel:</strong> Get multiple suggestions,
                        run in parallel
                      </li>
                    </ul>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-semibold">
                    3.2 Understanding Suggestions
                  </h4>
                  <p className="mb-3">
                    Each suggestion represents the AI's best guess for where to
                    experiment next:
                  </p>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-950/20">
                      <h5 className="mb-2 font-medium text-blue-900 dark:text-blue-100">
                        Exploration vs Exploitation
                      </h5>
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        The AI balances exploring unknown regions with
                        exploiting promising areas it has already discovered.
                      </p>
                    </div>

                    <div className="rounded-lg bg-green-50 p-4 dark:bg-green-950/20">
                      <h5 className="mb-2 font-medium text-green-900 dark:text-green-100">
                        Acquisition Function
                      </h5>
                      <p className="text-sm text-green-800 dark:text-green-200">
                        Uses Expected Improvement to find points likely to beat
                        your current best result.
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-semibold">
                    3.3 Adding Measurements
                  </h4>
                  <p className="mb-3">
                    After running experiments, add your results back to the
                    system:
                  </p>

                  <div className="space-y-3">
                    <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-900">
                      <h5 className="mb-2 font-medium">Measurement Types:</h5>
                      <ul className="space-y-1 text-sm">
                        <li>
                          • <strong>AI Suggestions:</strong> Results from
                          AI-recommended experiments
                        </li>
                        <li>
                          • <strong>Manual Experiments:</strong> Your own
                          experimental data
                        </li>
                        <li>
                          • <strong>Batch Submission:</strong> Add multiple
                          results at once
                        </li>
                      </ul>
                    </div>

                    <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-950/20">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="mt-0.5 size-5 text-yellow-600 dark:text-yellow-400" />
                        <div>
                          <h5 className="mb-1 font-semibold text-yellow-900 dark:text-yellow-100">
                            Data Quality Matters
                          </h5>
                          <p className="text-sm text-yellow-800 dark:text-yellow-200">
                            Accurate measurements are crucial. Double-check your
                            data entry and ensure measurements match the
                            suggested parameters exactly.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-semibold">3.4 Iterative Process</h4>
                  <p className="mb-3">
                    Repeat the suggestion → experiment → measurement cycle:
                  </p>

                  <div className="flex items-center justify-between rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 p-4 dark:from-blue-950/20 dark:to-purple-950/20">
                    <div className="flex items-center gap-2">
                      <Brain className="size-5 text-blue-600" />
                      <span className="font-medium">Get Suggestion</span>
                    </div>
                    <ArrowRight className="size-4 text-gray-400" />
                    <div className="flex items-center gap-2">
                      <Beaker className="size-5 text-green-600" />
                      <span className="font-medium">Run Experiment</span>
                    </div>
                    <ArrowRight className="size-4 text-gray-400" />
                    <div className="flex items-center gap-2">
                      <Database className="size-5 text-purple-600" />
                      <span className="font-medium">Add Measurement</span>
                    </div>
                    <ArrowRight className="size-4 text-gray-400" />
                    <div className="flex items-center gap-2">
                      <RefreshCw className="size-5 text-orange-600" />
                      <span className="font-medium">Repeat</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2 rounded-lg bg-green-50 p-4 dark:bg-green-950/20">
                  <CheckCircle className="size-5 text-green-600" />
                  <span className="font-medium text-green-800 dark:text-green-200">
                    Best Practice: Run 10-20 AI-guided experiments for most
                    optimization problems
                  </span>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analyze" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="size-5" />
                  Phase 4: Analyzing Results and Making Decisions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="mb-3 font-semibold">
                    4.1 Understanding Your Results
                  </h4>
                  <p className="mb-4">
                    Navigate to your optimization's main page to access
                    comprehensive analysis tools.
                  </p>

                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="rounded-lg bg-yellow-50 p-4 dark:bg-yellow-950/20">
                      <div className="mb-2 flex items-center gap-2">
                        <Target className="size-4 text-yellow-600" />
                        <h5 className="font-medium text-yellow-900 dark:text-yellow-100">
                          Best Results
                        </h5>
                      </div>
                      <p className="text-sm text-yellow-800 dark:text-yellow-200">
                        Current optimal parameter combination and target value
                      </p>
                    </div>

                    <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-950/20">
                      <div className="mb-2 flex items-center gap-2">
                        <TrendingUp className="size-4 text-blue-600" />
                        <h5 className="font-medium text-blue-900 dark:text-blue-100">
                          Convergence
                        </h5>
                      </div>
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        How your optimization is progressing over time
                      </p>
                    </div>

                    <div className="rounded-lg bg-green-50 p-4 dark:bg-green-950/20">
                      <div className="mb-2 flex items-center gap-2">
                        <Layers className="size-4 text-green-600" />
                        <h5 className="font-medium text-green-900 dark:text-green-100">
                          Parameter Impact
                        </h5>
                      </div>
                      <p className="text-sm text-green-800 dark:text-green-200">
                        Which parameters have the most influence on your target
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-semibold">
                    4.2 Key Analysis Features
                  </h4>

                  <div className="space-y-4">
                    <div className="rounded-lg border p-4">
                      <h5 className="mb-2 flex items-center gap-2 font-medium">
                        <BarChart3 className="size-4" />
                        Measurement History
                      </h5>
                      <p className="text-muted-foreground mb-2 text-sm">
                        View all your experimental data in chronological order,
                        with batch tracking and the ability to distinguish
                        between AI suggestions and manual experiments.
                      </p>
                    </div>

                    <div className="rounded-lg border p-4">
                      <h5 className="mb-2 flex items-center gap-2 font-medium">
                        <TrendingUp className="size-4" />
                        Convergence Analysis
                      </h5>
                      <p className="text-muted-foreground mb-2 text-sm">
                        Track how your best result improves over time. A
                        flattening curve suggests you're approaching the
                        optimum.
                      </p>
                    </div>

                    <div className="rounded-lg border p-4">
                      <h5 className="mb-2 flex items-center gap-2 font-medium">
                        <Layers className="size-4" />
                        Parameter Impact Analysis
                      </h5>
                      <p className="text-muted-foreground mb-2 text-sm">
                        Understand which parameters have the strongest influence
                        on your target. Focus future optimization efforts on
                        high-impact parameters.
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-semibold">4.3 Making Decisions</h4>

                  <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/20">
                    <h5 className="mb-2 font-semibold text-blue-900 dark:text-blue-100">
                      When to Stop Optimizing:
                    </h5>
                    <ul className="space-y-1 text-sm text-blue-800 dark:text-blue-200">
                      <li>
                        • Convergence curve has flattened for several iterations
                      </li>
                      <li>• You've reached your target performance goal</li>
                      <li>
                        • Diminishing returns don't justify additional
                        experiments
                      </li>
                      <li>
                        • You've explored the parameter space sufficiently
                      </li>
                    </ul>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-semibold">4.4 Exporting Results</h4>
                  <p className="mb-3">
                    Export your data for further analysis or reporting:
                  </p>

                  <div className="grid gap-3 md:grid-cols-2">
                    <div className="flex items-center gap-3 rounded-lg border p-3">
                      <FileText className="size-5 text-blue-600" />
                      <div>
                        <h5 className="font-medium">CSV Export</h5>
                        <p className="text-muted-foreground text-sm">
                          Raw data for analysis
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 rounded-lg border p-3">
                      <Download className="size-5 text-green-600" />
                      <div>
                        <h5 className="font-medium">Excel Export</h5>
                        <p className="text-muted-foreground text-sm">
                          Formatted reports
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2 rounded-lg bg-green-50 p-4 dark:bg-green-950/20">
                  <CheckCircle className="size-5 text-green-600" />
                  <span className="font-medium text-green-800 dark:text-green-200">
                    Success: You now have optimized parameters and understand
                    your experimental space!
                  </span>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </section>

      {/* Real-World Examples Section */}
      <section>
        <h3 className="mb-4 text-xl font-bold">
          Real-World Application Examples
        </h3>
        <p className="mb-6">
          See how the INNOptimizer™ workflow applies across different
          industries and use cases. These examples demonstrate the versatility
          and practical value of Bayesian optimization.
        </p>

        <Tabs defaultValue="chemical" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="chemical">Chemical Process</TabsTrigger>
            <TabsTrigger value="materials">Materials Science</TabsTrigger>
            <TabsTrigger value="manufacturing">Manufacturing</TabsTrigger>
            <TabsTrigger value="bioprocess">Bioprocess</TabsTrigger>
          </TabsList>

          <TabsContent value="chemical" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Beaker className="size-5 text-blue-600" />
                  Catalyst Performance Optimization
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-950/20">
                  <h4 className="mb-2 font-semibold">Challenge:</h4>
                  <p className="mb-3 text-sm">
                    A petrochemical company needed to optimize their catalytic
                    cracking process to maximize gasoline yield while minimizing
                    energy consumption.
                  </p>
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-blue-900 dark:text-blue-100">
                      Parameters
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• Temperature: 450-550°C</li>
                      <li>• Pressure: 1.5-3.0 bar</li>
                      <li>• Catalyst/Oil ratio: 5-15</li>
                      <li>• Residence time: 2-8 seconds</li>
                    </ul>
                  </div>
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-green-900 dark:text-green-100">
                      Targets
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• Gasoline yield (MAX)</li>
                      <li>• Energy consumption (MIN)</li>
                      <li>• Catalyst deactivation (MIN)</li>
                    </ul>
                  </div>
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-purple-900 dark:text-purple-100">
                      Results
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• 12% increase in yield</li>
                      <li>• 8% energy reduction</li>
                      <li>• 25 experiments total</li>
                      <li>• 6 weeks to completion</li>
                    </ul>
                  </div>
                </div>

                <div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-950/20">
                  <h5 className="mb-2 font-semibold text-green-900 dark:text-green-100">
                    Key Insights:
                  </h5>
                  <ul className="space-y-1 text-sm text-green-800 dark:text-green-200">
                    <li>
                      • Temperature had the highest impact on gasoline yield
                    </li>
                    <li>
                      • Optimal conditions were at higher temperature and lower
                      pressure than expected
                    </li>
                    <li>
                      • Multi-objective optimization revealed trade-offs between
                      yield and energy consumption
                    </li>
                    <li>
                      • Sequential approach was used due to expensive pilot
                      plant experiments
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="materials" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layers className="size-5 text-green-600" />
                  Alloy Composition Development
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="rounded-lg bg-green-50 p-4 dark:bg-green-950/20">
                  <h4 className="mb-2 font-semibold">Challenge:</h4>
                  <p className="mb-3 text-sm">
                    An aerospace materials lab needed to develop a new aluminum
                    alloy with optimal strength-to-weight ratio for aircraft
                    components.
                  </p>
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-blue-900 dark:text-blue-100">
                      Parameters
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• Copper content: 1-6%</li>
                      <li>• Magnesium content: 0.5-2.5%</li>
                      <li>• Silicon content: 0.2-1.2%</li>
                      <li>• Heat treatment temp: 450-550°C</li>
                      <li>• Aging time: 4-24 hours</li>
                    </ul>
                  </div>
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-green-900 dark:text-green-100">
                      Targets
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• Tensile strength (MAX)</li>
                      <li>• Density (MIN)</li>
                      <li>• Corrosion resistance (MAX)</li>
                      <li>• Machinability index (MAX)</li>
                    </ul>
                  </div>
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-purple-900 dark:text-purple-100">
                      Results
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• 18% strength improvement</li>
                      <li>• 5% weight reduction</li>
                      <li>• 42 experiments total</li>
                      <li>• 3 months to completion</li>
                    </ul>
                  </div>
                </div>

                <div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-950/20">
                  <h5 className="mb-2 font-semibold text-green-900 dark:text-green-100">
                    Key Insights:
                  </h5>
                  <ul className="space-y-1 text-sm text-green-800 dark:text-green-200">
                    <li>
                      • Copper content was the most critical parameter for
                      strength
                    </li>
                    <li>
                      • Heat treatment temperature and aging time showed strong
                      interaction effects
                    </li>
                    <li>
                      • Parallel batches of 3-4 samples were used to accelerate
                      testing
                    </li>
                    <li>
                      • Multi-objective optimization revealed optimal trade-off
                      between properties
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="manufacturing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="size-5 text-purple-600" />
                  Injection Molding Process Optimization
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="rounded-lg bg-purple-50 p-4 dark:bg-purple-950/20">
                  <h4 className="mb-2 font-semibold">Challenge:</h4>
                  <p className="mb-3 text-sm">
                    A plastic manufacturing company wanted to optimize their
                    injection molding process to reduce defects and improve
                    cycle time for automotive parts.
                  </p>
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-blue-900 dark:text-blue-100">
                      Parameters
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• Injection pressure: 80-120 MPa</li>
                      <li>• Melt temperature: 220-280°C</li>
                      <li>• Mold temperature: 40-80°C</li>
                      <li>• Injection speed: 20-80 mm/s</li>
                      <li>• Holding time: 5-20 seconds</li>
                    </ul>
                  </div>
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-green-900 dark:text-green-100">
                      Targets
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• Defect rate (MIN)</li>
                      <li>• Cycle time (MIN)</li>
                      <li>• Part weight consistency (MAX)</li>
                      <li>• Surface quality score (MAX)</li>
                    </ul>
                  </div>
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-purple-900 dark:text-purple-100">
                      Results
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• 65% defect reduction</li>
                      <li>• 15% faster cycle time</li>
                      <li>• 28 experiments total</li>
                      <li>• 4 weeks to completion</li>
                    </ul>
                  </div>
                </div>

                <div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-950/20">
                  <h5 className="mb-2 font-semibold text-green-900 dark:text-green-100">
                    Key Insights:
                  </h5>
                  <ul className="space-y-1 text-sm text-green-800 dark:text-green-200">
                    <li>
                      • Mold temperature had unexpected high impact on defect
                      rate
                    </li>
                    <li>
                      • Injection speed and pressure showed complex interaction
                      effects
                    </li>
                    <li>
                      • Sequential optimization was preferred due to production
                      constraints
                    </li>
                    <li>
                      • Real-time quality monitoring enabled rapid feedback
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="bioprocess" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="size-5 text-orange-600" />
                  Fermentation Process Optimization
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="rounded-lg bg-orange-50 p-4 dark:bg-orange-950/20">
                  <h4 className="mb-2 font-semibold">Challenge:</h4>
                  <p className="mb-3 text-sm">
                    A biotechnology company needed to optimize their bacterial
                    fermentation process to maximize protein production while
                    minimizing contamination risk.
                  </p>
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-blue-900 dark:text-blue-100">
                      Parameters
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• Temperature: 25-37°C</li>
                      <li>• pH: 6.5-8.0</li>
                      <li>• Dissolved oxygen: 20-80%</li>
                      <li>• Glucose concentration: 10-50 g/L</li>
                      <li>• Agitation rate: 100-400 rpm</li>
                    </ul>
                  </div>
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-green-900 dark:text-green-100">
                      Targets
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• Protein yield (MAX)</li>
                      <li>• Batch time (MIN)</li>
                      <li>• Contamination rate (MIN)</li>
                      <li>• Product purity (MAX)</li>
                    </ul>
                  </div>
                  <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-900">
                    <h5 className="mb-1 font-medium text-purple-900 dark:text-purple-100">
                      Results
                    </h5>
                    <ul className="text-muted-foreground space-y-1 text-sm">
                      <li>• 35% yield improvement</li>
                      <li>• 20% time reduction</li>
                      <li>• 38 experiments total</li>
                      <li>• 8 weeks to completion</li>
                    </ul>
                  </div>
                </div>

                <div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-950/20">
                  <h5 className="mb-2 font-semibold text-green-900 dark:text-green-100">
                    Key Insights:
                  </h5>
                  <ul className="space-y-1 text-sm text-green-800 dark:text-green-200">
                    <li>
                      • pH control was critical for both yield and contamination
                      prevention
                    </li>
                    <li>
                      • Temperature and dissolved oxygen showed strong
                      synergistic effects
                    </li>
                    <li>
                      • Parallel batches of 2-3 were used to account for
                      biological variability
                    </li>
                    <li>
                      • Multi-objective approach balanced productivity with
                      quality
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </section>

      {/* Best Practices */}
      <section>
        <h3 className="mb-4 text-xl font-bold">Best Practices & Tips</h3>

        <div className="grid gap-4 md:grid-cols-2">
          <Card className="border-green-200 dark:border-green-800">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg text-green-800 dark:text-green-200">
                <CheckCircle className="size-5" />
                Do's
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <span className="mt-1 text-green-600">•</span>
                  <span>
                    Start with fewer parameters (2-5) for your first
                    optimization
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 text-green-600">•</span>
                  <span>
                    Use realistic parameter ranges based on your process
                    constraints
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 text-green-600">•</span>
                  <span>
                    Ensure measurement accuracy - double-check your experimental
                    data
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 text-green-600">•</span>
                  <span>
                    Run initial samples before expecting good AI suggestions
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 text-green-600">•</span>
                  <span>
                    Monitor convergence to know when to stop optimizing
                  </span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="border-red-200 dark:border-red-800">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg text-red-800 dark:text-red-200">
                <AlertCircle className="size-5" />
                Don'ts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <span className="mt-1 text-red-600">•</span>
                  <span>
                    Don't expect good suggestions without initial data
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 text-red-600">•</span>
                  <span>
                    Don't use unrealistic parameter ranges that you can't
                    actually test
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 text-red-600">•</span>
                  <span>
                    Don't ignore measurement errors - they compound over time
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 text-red-600">•</span>
                  <span>
                    Don't stop too early - give the AI time to learn your space
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="mt-1 text-red-600">•</span>
                  <span>
                    Don't optimize too many parameters simultaneously as a
                    beginner
                  </span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Troubleshooting */}
      <section>
        <h3 className="mb-4 text-xl font-bold">
          Common Issues & Troubleshooting
        </h3>

        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <AlertCircle className="size-5 text-yellow-600" />
                "AI suggestions aren't improving my results"
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h5 className="mb-1 font-medium text-red-800 dark:text-red-200">
                    Possible Causes:
                  </h5>
                  <ul className="text-muted-foreground space-y-1 text-sm">
                    <li>
                      • Insufficient initial data (need 5-10 initial samples
                      minimum)
                    </li>
                    <li>
                      • Measurement noise or errors in your experimental data
                    </li>
                    <li>• Parameter ranges too narrow or too wide</li>
                    <li>
                      • The optimization has converged (you've found the
                      optimum)
                    </li>
                  </ul>
                </div>
                <div>
                  <h5 className="mb-1 font-medium text-green-800 dark:text-green-200">
                    Solutions:
                  </h5>
                  <ul className="text-muted-foreground space-y-1 text-sm">
                    <li>
                      • Add more initial samples with good parameter space
                      coverage
                    </li>
                    <li>• Review and validate your measurement accuracy</li>
                    <li>
                      • Check if convergence analysis shows you've reached the
                      optimum
                    </li>
                    <li>
                      • Consider expanding parameter ranges if physically
                      feasible
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <AlertCircle className="size-5 text-yellow-600" />
                "My optimization seems stuck in a local optimum"
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h5 className="mb-1 font-medium text-red-800 dark:text-red-200">
                    Possible Causes:
                  </h5>
                  <ul className="text-muted-foreground space-y-1 text-sm">
                    <li>• Limited exploration of the parameter space</li>
                    <li>
                      • Acquisition function is too focused on exploitation
                    </li>
                    <li>• Complex response surface with multiple optima</li>
                  </ul>
                </div>
                <div>
                  <h5 className="mb-1 font-medium text-green-800 dark:text-green-200">
                    Solutions:
                  </h5>
                  <ul className="text-muted-foreground space-y-1 text-sm">
                    <li>• Add some manual experiments in unexplored regions</li>
                    <li>• Generate additional exploratory samples</li>
                    <li>
                      • Consider restarting with different initial samples
                    </li>
                    <li>
                      • Use parallel suggestions to explore multiple regions
                      simultaneously
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <AlertCircle className="size-5 text-yellow-600" />
                "I'm getting inconsistent experimental results"
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h5 className="mb-1 font-medium text-red-800 dark:text-red-200">
                    Possible Causes:
                  </h5>
                  <ul className="text-muted-foreground space-y-1 text-sm">
                    <li>• Experimental noise or measurement uncertainty</li>
                    <li>• Uncontrolled variables affecting the process</li>
                    <li>• Equipment calibration issues</li>
                    <li>• Human error in parameter setting or measurement</li>
                  </ul>
                </div>
                <div>
                  <h5 className="mb-1 font-medium text-green-800 dark:text-green-200">
                    Solutions:
                  </h5>
                  <ul className="text-muted-foreground space-y-1 text-sm">
                    <li>
                      • Run replicate experiments to quantify measurement
                      uncertainty
                    </li>
                    <li>
                      • Identify and control additional variables that might
                      affect results
                    </li>
                    <li>
                      • Calibrate equipment and standardize experimental
                      procedures
                    </li>
                    <li>
                      • Consider the noise level when interpreting optimization
                      results
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Next Steps */}
      <section>
        <h3 className="mb-4 text-xl font-bold">Next Steps</h3>
        <p className="mb-4">
          Congratulations! You now understand the complete workflow for using
          INNOptimizer™. Here's what you can do next:
        </p>

        <div className="grid gap-4 md:grid-cols-2">
          <Card className="border-blue-200 dark:border-blue-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-blue-800 dark:text-blue-200">
                Continue Learning
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <ArrowRight className="size-4 text-blue-600" />
                  <span>Explore advanced configuration techniques</span>
                </li>
                <li className="flex items-center gap-2">
                  <ArrowRight className="size-4 text-blue-600" />
                  <span>Learn about multi-objective optimization</span>
                </li>
                <li className="flex items-center gap-2">
                  <ArrowRight className="size-4 text-blue-600" />
                  <span>Study sequential vs parallel strategies</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="border-green-200 dark:border-green-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-green-800 dark:text-green-200">
                Start Optimizing
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <ArrowRight className="size-4 text-green-600" />
                  <span>Create your first optimization project</span>
                </li>
                <li className="flex items-center gap-2">
                  <ArrowRight className="size-4 text-green-600" />
                  <span>Apply this workflow to your real problems</span>
                </li>
                <li className="flex items-center gap-2">
                  <ArrowRight className="size-4 text-green-600" />
                  <span>Share results with your team</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>

        <div className="mt-6 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 p-6 dark:from-blue-950/20 dark:to-purple-950/20">
          <div className="mb-3 flex items-center gap-3">
            <Lightbulb className="size-6 text-blue-600" />
            <h4 className="text-lg font-semibold">Ready to Get Started?</h4>
          </div>
          <p className="text-muted-foreground mb-4">
            You now have all the knowledge needed to successfully use
            INNOptimizer™ for your optimization projects. The key is to start
            simple and gradually tackle more complex problems.
          </p>
          <div className="flex gap-3">
            <Button className="flex items-center gap-2">
              <Play className="size-4" />
              Create Your First Optimization
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <FileText className="size-4" />
              View More Tutorials
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
