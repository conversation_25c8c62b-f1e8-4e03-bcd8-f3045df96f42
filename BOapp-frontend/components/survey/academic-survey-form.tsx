"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { saveAcademicSurveyAction } from "@/actions/academic-survey-actions"
import { AcademicSurveyResponses } from "@/actions/academic-verification-actions"
import { toast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"

// Define the form schema with validation
const formSchema = z.object({
  // Section 2: Experience with Bayesian Optimization Tools
  toolsAware: z.array(z.string()).min(1, "Please select at least one tool"),
  toolsAwareOther: z.string().optional(),
  toolsUsed: z
    .array(z.string())
    .min(
      1,
      "Please select at least one tool or 'None' if you haven't used any"
    ),
  toolsUsedOther: z.string().optional(),

  // Section 4: Pricing Perspective
  fairPriceAcademic: z.string().min(1, "Please select a price range"),
  fairPriceAcademicOther: z.string().optional(),
  fairPriceCommercial: z.string().min(1, "Please select a price range"),
  fairPriceCommercialOther: z.string().optional(),

  // Research area is now required
  researchArea: z.string().min(1, "Please provide your research area")
})

// List of Bayesian optimization tools
const bayesianTools = [
  { id: "gpyopt", label: "GPyOpt" },
  { id: "spearmint", label: "Spearmint" },
  { id: "hyperopt", label: "Hyperopt" },
  { id: "bayesopt", label: "BayesOpt" },
  { id: "skopt", label: "Scikit-optimize" }
]

// Add a "None" option for the tools used section
const toolsUsedOptions = [
  ...bayesianTools,
  { id: "none", label: "None - I haven't used any Bayesian optimization tools" }
]

export default function AcademicSurveyForm() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      toolsAware: [],
      toolsAwareOther: "",
      toolsUsed: [],
      toolsUsedOther: "",
      fairPriceAcademic: "",
      fairPriceAcademicOther: "",
      fairPriceCommercial: "",
      fairPriceCommercialOther: "",
      researchArea: ""
    }
  })

  // Calculate progress based on current step
  const calculateProgress = () => {
    return ((currentStep + 1) / 3) * 100
  }

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)
    try {
      const result = await saveAcademicSurveyAction(
        values as AcademicSurveyResponses,
        true
      )

      if (result.isSuccess) {
        toast({
          title: "Academic Survey Completed",
          description:
            "Thank you for completing the academic survey! You now have extended 90-day access.",
          variant: "default"
        })

        // Show a success message for a moment before redirecting
        setTimeout(() => {
          router.push("/dashboard/home")
        }, 1500)
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to submit survey",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error submitting survey:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Academic survey cannot be skipped
  // This function is kept for compatibility but is not used
  const handleSkip = async () => {
    toast({
      title: "Survey Required",
      description:
        "The academic survey must be completed to access the dashboard.",
      variant: "destructive"
    })
  }

  // Handle next step
  const handleNext = () => {
    const nextStep = currentStep + 1
    if (nextStep < 3) {
      setCurrentStep(nextStep)
    }
  }

  // Handle previous step
  const handlePrevious = () => {
    const prevStep = currentStep - 1
    if (prevStep >= 0) {
      setCurrentStep(prevStep)
    }
  }

  // Render the appropriate step
  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium">
              Experience with Bayesian Optimization Tools
            </h3>

            <FormField
              control={form.control}
              name="toolsAware"
              render={() => (
                <FormItem>
                  <FormLabel>
                    Which of the following Bayesian optimization tools are you
                    aware of?
                  </FormLabel>
                  <div className="space-y-2">
                    {bayesianTools.map(tool => (
                      <FormField
                        key={tool.id}
                        control={form.control}
                        name="toolsAware"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={tool.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(tool.id)}
                                  onCheckedChange={checked => {
                                    return checked
                                      ? field.onChange([
                                          ...field.value,
                                          tool.id
                                        ])
                                      : field.onChange(
                                          field.value?.filter(
                                            value => value !== tool.id
                                          )
                                        )
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {tool.label}
                              </FormLabel>
                            </FormItem>
                          )
                        }}
                      />
                    ))}

                    <FormField
                      control={form.control}
                      name="toolsAware"
                      render={({ field }) => {
                        return (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes("other")}
                                onCheckedChange={checked => {
                                  return checked
                                    ? field.onChange([...field.value, "other"])
                                    : field.onChange(
                                        field.value?.filter(
                                          value => value !== "other"
                                        )
                                      )
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">Other</FormLabel>
                          </FormItem>
                        )
                      }}
                    />

                    {form.watch("toolsAware").includes("other") && (
                      <FormField
                        control={form.control}
                        name="toolsAwareOther"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                placeholder="Please specify other tools"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium">Tools Used</h3>

            <FormField
              control={form.control}
              name="toolsUsed"
              render={() => (
                <FormItem>
                  <FormLabel>Which of these tools have you used?</FormLabel>
                  <div className="space-y-2">
                    {toolsUsedOptions.map(tool => (
                      <FormField
                        key={tool.id}
                        control={form.control}
                        name="toolsUsed"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={tool.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(tool.id)}
                                  onCheckedChange={checked => {
                                    // If selecting "None", clear all other selections
                                    if (tool.id === "none" && checked) {
                                      return field.onChange(["none"])
                                    }

                                    // If selecting any other option, remove "None" if it's selected
                                    let newValue = checked
                                      ? [...field.value, tool.id]
                                      : field.value?.filter(
                                          value => value !== tool.id
                                        )

                                    if (
                                      checked &&
                                      newValue.includes("none") &&
                                      tool.id !== "none"
                                    ) {
                                      newValue = newValue.filter(
                                        value => value !== "none"
                                      )
                                    }

                                    return field.onChange(newValue)
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {tool.label}
                              </FormLabel>
                            </FormItem>
                          )
                        }}
                      />
                    ))}

                    <FormField
                      control={form.control}
                      name="toolsUsed"
                      render={({ field }) => {
                        return (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes("other")}
                                onCheckedChange={checked => {
                                  return checked
                                    ? field.onChange([...field.value, "other"])
                                    : field.onChange(
                                        field.value?.filter(
                                          value => value !== "other"
                                        )
                                      )
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">Other</FormLabel>
                          </FormItem>
                        )
                      }}
                    />

                    {form.watch("toolsUsed").includes("other") && (
                      <FormField
                        control={form.control}
                        name="toolsUsedOther"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                placeholder="Please specify other tools you've used"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )
      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium">Pricing Perspective</h3>

            <FormField
              control={form.control}
              name="fairPriceAcademic"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    In your opinion, what is a fair yearly subscription fee for
                    academic users to access our tool?
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="free" />
                        </FormControl>
                        <FormLabel className="font-normal">Free</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="0-50" />
                        </FormControl>
                        <FormLabel className="font-normal">€0 - €50</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="50-100" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          €50 - €100
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="100-200" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          €100 - €200
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="other" />
                        </FormControl>
                        <FormLabel className="font-normal">Other</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch("fairPriceAcademic") === "other" && (
              <FormField
                control={form.control}
                name="fairPriceAcademicOther"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="Please specify other price range"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="fairPriceCommercial"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    In your opinion, what is a fair yearly subscription fee for
                    commercial users to access our tool?
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="100-500" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          €100 - €500
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="500-1000" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          €500 - €1000
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="1000-2000" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          €1000 - €2000
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="2000+" />
                        </FormControl>
                        <FormLabel className="font-normal">€2000+</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="other" />
                        </FormControl>
                        <FormLabel className="font-normal">Other</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch("fairPriceCommercial") === "other" && (
              <FormField
                control={form.control}
                name="fairPriceCommercialOther"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="Please specify other price range"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="researchArea"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    What is your primary field of study or research area?
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter your research area"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    This information helps us understand how our tool can better
                    serve your specific research needs.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Academic Survey</h3>
          <span className="text-muted-foreground text-sm">
            Step {currentStep + 1} of 3
          </span>
        </div>
        <Progress value={calculateProgress()} className="h-2" />
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {renderStep()}

          <div className="flex justify-between">
            <div>
              {currentStep > 0 ? (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={isSubmitting}
                >
                  Previous
                </Button>
              ) : (
                <div className="text-muted-foreground text-sm">
                  * Survey completion is required for academic access
                </div>
              )}
            </div>
            <div>
              {currentStep < 2 ? (
                <Button
                  type="button"
                  onClick={handleNext}
                  disabled={
                    isSubmitting ||
                    (currentStep === 0 && form.watch("toolsAware").length === 0)
                  }
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="submit"
                  disabled={isSubmitting || !form.formState.isValid}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 size-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    "Submit"
                  )}
                </Button>
              )}
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}
