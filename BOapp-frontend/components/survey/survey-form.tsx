"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { saveSurveyAction, SurveyResponses } from "@/actions/survey-actions"
import { toast } from "@/components/ui/use-toast"

// Define the form schema with validation
const formSchema = z.object({
  industry: z.string().min(1, "Please select your industry"),
  role: z.string().min(1, "Please select your role"),
  useCase: z.string().min(1, "Please select your primary use case")
})

export default function SurveyForm() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      industry: "",
      role: "",
      useCase: ""
    }
  })

  // Calculate progress based on filled fields
  const calculateProgress = () => {
    const values = form.getValues()
    const filledFields = Object.values(values).filter(Boolean).length
    return (filledFields / 3) * 100
  }

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)
    try {
      const result = await saveSurveyAction(values as SurveyResponses, true)

      if (result.isSuccess) {
        toast({
          title: "Survey completed",
          description: "Thank you for completing the survey!",
          variant: "default"
        })
        router.push("/dashboard/home")
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to submit survey",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error submitting survey:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle skipping the survey
  const handleSkip = async () => {
    setIsSubmitting(true)
    try {
      // Save an empty survey response but mark it as completed
      const emptyResponses: SurveyResponses = {
        industry: "skipped",
        role: "skipped",
        useCase: "skipped"
      }

      const result = await saveSurveyAction(emptyResponses, true)

      if (result.isSuccess) {
        router.push("/dashboard/home")
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to skip survey",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error skipping survey:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle next step
  const handleNext = () => {
    const nextStep = currentStep + 1
    if (nextStep < 3) {
      setCurrentStep(nextStep)
    }
  }

  // Handle previous step
  const handlePrevious = () => {
    const prevStep = currentStep - 1
    if (prevStep >= 0) {
      setCurrentStep(prevStep)
    }
  }

  // Render the appropriate step
  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <FormField
            control={form.control}
            name="industry"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>What industry are you in?</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your industry" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="pharmaceuticals">
                      Pharmaceuticals
                    </SelectItem>
                    <SelectItem value="chemicals">Chemicals</SelectItem>
                    <SelectItem value="materials">Materials Science</SelectItem>
                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                    <SelectItem value="energy">Energy</SelectItem>
                    <SelectItem value="biotech">Biotech</SelectItem>
                    <SelectItem value="academia">Academia/Research</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )
      case 1:
        return (
          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>What is your role?</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="researcher">Researcher</SelectItem>
                    <SelectItem value="scientist">Scientist</SelectItem>
                    <SelectItem value="engineer">Engineer</SelectItem>
                    <SelectItem value="manager">Manager/Team Lead</SelectItem>
                    <SelectItem value="executive">Executive</SelectItem>
                    <SelectItem value="student">Student</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )
      case 2:
        return (
          <FormField
            control={form.control}
            name="useCase"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>What is your primary use case?</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your primary use case" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="formulation">
                      Formulation Optimization
                    </SelectItem>
                    <SelectItem value="process">
                      Process Optimization
                    </SelectItem>
                    <SelectItem value="materials">
                      Materials Discovery
                    </SelectItem>
                    <SelectItem value="catalyst">
                      Catalyst Development
                    </SelectItem>
                    <SelectItem value="drug">Drug Discovery</SelectItem>
                    <SelectItem value="energy">Energy Systems</SelectItem>
                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">3 Quick Questions</h3>
        <Progress value={calculateProgress()} className="h-2" />
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {renderStep()}

          <div className="flex justify-between">
            <div>
              {currentStep > 0 ? (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={isSubmitting}
                >
                  Previous
                </Button>
              ) : (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSkip}
                  disabled={isSubmitting}
                >
                  Skip
                </Button>
              )}
            </div>
            <div>
              {currentStep < 2 ? (
                <Button
                  type="button"
                  onClick={handleNext}
                  disabled={
                    isSubmitting ||
                    (currentStep === 0 && !form.getValues().industry) ||
                    (currentStep === 1 && !form.getValues().role)
                  }
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="submit"
                  disabled={isSubmitting || !form.formState.isValid}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              )}
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}
