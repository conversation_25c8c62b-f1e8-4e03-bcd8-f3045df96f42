"use client"

import { useState } from "react"
import { MessageSquarePlus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { FeedbackDialog } from "@/components/feedback/feedback-dialog"
import { usePathname } from "next/navigation"

interface FeedbackButtonProps {
  variant?: "default" | "outline" | "ghost"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
}

export function FeedbackButton({
  variant = "outline",
  size = "default",
  className
}: FeedbackButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => setIsOpen(true)}
      >
        <MessageSquarePlus className="mr-2 size-4" />
        Feedback
      </Button>

      <FeedbackDialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        initialMetadata={{ currentPath: pathname }}
      />
    </>
  )
}
