"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  PopoverTrigger
} from "@/components/ui/popover"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { InfoIcon, ExternalLink } from "lucide-react"
import Link from "next/link"

export function StrategyEvolutionDocumentation() {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="size-6">
          <InfoIcon className="size-4" />
          <span className="sr-only">About Strategy Evolution</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="max-h-[500px] w-[350px] overflow-y-auto"
        align="end"
      >
        <div className="space-y-4">
          <h3 className="text-lg font-medium">
            Understanding Strategy Evolution
          </h3>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">What It Shows</h4>
            <p className="text-muted-foreground text-sm">
              The Strategy Evolution visualization shows how the optimization
              balances between exploration (trying new areas) and exploitation
              (focusing on promising areas) over time.
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Key Components</h4>
            <ul className="text-muted-foreground list-disc space-y-1 pl-5 text-sm">
              <li>
                <span className="font-medium text-blue-500">Blue area</span>:
                Represents exploration - searching new areas of the parameter
                space
              </li>
              <li>
                <span className="font-medium text-orange-500">Orange area</span>
                : Represents exploitation - focusing on promising regions
              </li>
              <li>
                <span className="font-medium text-green-500">Green line</span>:
                Shows the balance between exploration and exploitation
              </li>
              <li>
                <span className="font-medium text-red-500">Dashed lines</span>:
                Indicate significant strategy shifts
              </li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Controls</h4>
            <ul className="text-muted-foreground list-disc space-y-1 pl-5 text-sm">
              <li>
                <strong>Exploration Weight (β)</strong>: Adjusts the relative
                importance of exploration
              </li>
              <li>
                <strong>Window Size</strong>: Controls the smoothing window for
                trend calculations
              </li>
              <li>
                <strong>Show Strategy Shifts</strong>: Toggles visibility of
                strategy shift markers
              </li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">
              Interpreting the Visualization
            </h4>
            <p className="text-muted-foreground text-sm">
              A positive balance (above center) indicates more exploration,
              while a negative balance (below center) indicates more
              exploitation. Early experiments typically focus on exploration,
              while later ones shift toward exploitation.
            </p>
          </div>

          <div className="border-t pt-2">
            <Link
              href="/dashboard/help/optimization-analysis"
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Learn more about optimization analysis
              <ExternalLink className="ml-1 size-3" />
            </Link>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
