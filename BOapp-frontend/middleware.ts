/*
Contains middleware for protecting routes, checking user authentication, and redirecting as needed.
*/

import { clerkMiddleware } from "@clerk/nextjs/server"
import { NextResponse } from "next/server"

// Helper function to check if a path matches any of the patterns
const matchesPattern = (path: string, patterns: string[]) => {
  return patterns.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
      return regex.test(path);
    }
    return path === pattern;
  });
};

// Define route patterns
const protectedRoutes = ["/dashboard(.*)", "/todo(.*)"];
const welcomePages = ["/welcome"];
const academicPages = ["/academic-signup", "/academic-survey"];
const marketingPages = [
  "/", // Include root path as a marketing page
  "/features",
  "/pricing",
  "/contact",
  "/case-studies",
  "/tutorials",
  "/tutorials/(.*)" // Include all tutorial subpages
];

export default clerkMiddleware(async (auth, req) => {
  const { userId, redirectToSignIn } = await auth()
  const { pathname } = req.nextUrl

  console.log("Middleware processing path:", pathname, "User logged in:", !!userId);

  // Define public routes that don't require authentication
  const publicRoutes = [
    "/",
    "/about",
    "/features",
    "/pricing",
    "/contact",
    "/case-studies",
    "/tutorials",
    "/tutorials/(.*)", // Include all tutorial subpages
    "/api/stripe/webhooks",
    "/api/stripe/checkout",
    "/auth-redirect" // Add our new auth-redirect page
  ];

  // Check if the current path is a public route
  const isPublicRoute = publicRoutes.includes(pathname) ||
                        pathname.startsWith('/api/stripe/webhooks') ||
                        pathname.startsWith('/api/stripe/checkout') ||
                        pathname.startsWith('/tutorials/');

  // CASE 1: User is not logged in
  if (!userId) {
    // If trying to access protected routes, redirect to sign-in
    if (matchesPattern(pathname, protectedRoutes)) {
      console.log("Middleware: Unauthenticated user trying to access protected route, redirecting to login");
      return redirectToSignIn({ returnBackUrl: "/login" })
    }

    // If trying to access welcome page, redirect to sign-in
    if (matchesPattern(pathname, welcomePages)) {
      console.log("Middleware: Unauthenticated user trying to access welcome page, redirecting to login");
      return redirectToSignIn({ returnBackUrl: "/welcome" })
    }

    // If trying to access academic pages, redirect to sign-in
    if (matchesPattern(pathname, academicPages)) {
      console.log("Middleware: Unauthenticated user trying to access academic page, redirecting to login");
      return redirectToSignIn({ returnBackUrl: pathname })
    }

    // For all other public routes (marketing, etc.), allow access
    console.log("Middleware: Unauthenticated user accessing public route, allowing");
    return NextResponse.next()
  }

  // CASE 2: User is logged in - allow access to all routes
  console.log("Middleware: Authenticated user, allowing access");
  return NextResponse.next()
})

export const config = {
  matcher: [
    "/((?!.*\\..*|_next).*)",
    "/",
    "/(api|trpc)((?!/proxy).*)"] // Exclude /api/proxy routes from authentication
}
