name: boapp-frontend
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - bzip2=1.0.8=h4bc722e_7
  - ca-certificates=2025.1.31=hbcca054_0
  - ld_impl_linux-64=2.43=h712a8e2_4
  - libexpat=2.7.0=h5888daf_0
  - libffi=3.4.6=h2dba641_1
  - libgcc=14.2.0=h767d61c_2
  - libgcc-ng=14.2.0=h69a702a_2
  - libgomp=14.2.0=h767d61c_2
  - liblzma=5.8.1=hb9d3cd8_0
  - libnsl=2.0.1=hd590300_0
  - libsqlite=3.49.1=hee588c1_2
  - libuuid=2.38.1=h0b41bf4_0
  - libxcrypt=4.4.36=hd590300_1
  - libzlib=1.3.1=hb9d3cd8_2
  - ncurses=6.5=h2d0b736_3
  - openssl=3.5.0=h7b32b05_0
  - pip=25.0.1=pyh8b19718_0
  - python=3.11.11=h9e4cc4f_2_cpython
  - readline=8.2=h8c095d6_2
  - setuptools=78.1.0=pyhff2d567_0
  - tk=8.6.13=noxft_h4845f30_101
  - tzdata=2025b=h78e105d_0
  - wheel=0.45.1=pyhd8ed1ab_1
  - pip:
      - psycopg2-binary==2.9.10
      - python-dotenv==1.1.0
prefix: /home/<USER>/miniconda3/envs/boapp-frontend
