// db/schema/strategy-metrics-schema.ts
import {
  pgTable,
  uuid,
  text,
  jsonb,
  timestamp,
  integer,
  real
} from "drizzle-orm/pg-core"
import { optimizationsTable } from "./optimizations-schema"

// Strategy metrics table for storing optimization strategy analysis
export const strategyMetricsTable = pgTable("strategy_metrics", {
  id: uuid("id").defaultRandom().primaryKey(),
  optimizationId: uuid("optimization_id")
    .references(() => optimizationsTable.id, { onDelete: "cascade" })
    .notNull(),

  // Strategy metrics data
  parameterDiversity: jsonb("parameter_diversity").notNull().default({}),
  improvementRates: jsonb("improvement_rates").notNull().default({}),
  ucbComponents: jsonb("ucb_components").notNull().default({ experiments: [] }),
  explorationScore: real("exploration_score").notNull().default(0),
  exploitationScore: real("exploitation_score").notNull().default(0),
  normalizedExploration: real("normalized_exploration").notNull().default(0.5),
  normalizedExploitation: real("normalized_exploitation")
    .notNull()
    .default(0.5),
  balance: real("balance").notNull().default(0),
  dominantStrategy: text("dominant_strategy").notNull().default("balanced"),
  strategyShifts: jsonb("strategy_shifts").notNull().default([]),

  // Configuration parameters
  windowSize: integer("window_size").notNull().default(10),
  beta: real("beta").notNull().default(1.0),
  acquisitionFunction: text("acquisition_function")
    .notNull()
    .default("qUpperConfidenceBound"),

  // Tracking fields
  lastExperimentId: uuid("last_experiment_id"),

  // Standard metadata
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date())
})

// Type definitions
export type InsertStrategyMetrics = typeof strategyMetricsTable.$inferInsert
export type SelectStrategyMetrics = typeof strategyMetricsTable.$inferSelect
