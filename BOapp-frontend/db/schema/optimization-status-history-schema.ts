// db/schema/optimization-status-history-schema.ts
import { pgTable, uuid, text, timestamp } from "drizzle-orm/pg-core"
import { optimizationsTable } from "./optimizations-schema"

// Status history table for tracking optimization status changes
export const optimizationStatusHistoryTable = pgTable(
  "optimization_status_history",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    optimizationId: uuid("optimization_id")
      .references(() => optimizationsTable.id, { onDelete: "cascade" })
      .notNull(),
    previousStatus: text("previous_status").notNull(),
    newStatus: text("new_status").notNull(),
    reason: text("reason"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    createdBy: text("created_by").notNull()
  }
)

// Type definitions
export type InsertOptimizationStatusHistory =
  typeof optimizationStatusHistoryTable.$inferInsert
export type SelectOptimizationStatusHistory =
  typeof optimizationStatusHistoryTable.$inferSelect
