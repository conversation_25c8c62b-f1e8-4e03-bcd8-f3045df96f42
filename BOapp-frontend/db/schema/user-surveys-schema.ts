/*
Defines the database schema for user survey responses.
*/

import { pgTable, text, timestamp, jsonb } from "drizzle-orm/pg-core"

export const userSurveysTable = pgTable("user_surveys", {
  userId: text("user_id").primaryKey().notNull(),
  responses: jsonb("responses").notNull(), // Store survey responses as JSON
  completed: text("completed").default("true").notNull(), // Whether the survey was completed or skipped
  surveyType: text("survey_type").default("regular").notNull(), // Type of survey: 'regular' or 'academic'
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date())
})

export type InsertUserSurvey = typeof userSurveysTable.$inferInsert
export type SelectUserSurvey = typeof userSurveysTable.$inferSelect
