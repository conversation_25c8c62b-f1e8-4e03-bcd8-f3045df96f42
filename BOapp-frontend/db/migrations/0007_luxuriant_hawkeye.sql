CREATE TYPE "public"."subscription_type" AS ENUM('monthly', 'yearly');--> statement-breakpoint
CREATE TABLE "user_surveys" (
	"user_id" text PRIMARY KEY NOT NULL,
	"responses" jsonb NOT NULL,
	"completed" text DEFAULT 'true' NOT NULL,
	"survey_type" text DEFAULT 'regular' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "academic_verifications" (
	"user_id" text PRIMARY KEY NOT NULL,
	"full_name" text NOT NULL,
	"email" text NOT NULL,
	"institutional_email" text NOT NULL,
	"institution" text NOT NULL,
	"role" text NOT NULL,
	"verification_status" text DEFAULT 'pending' NOT NULL,
	"verification_method" text,
	"verification_timestamp" timestamp,
	"rejection_reason" text,
	"verification_token" text,
	"verification_token_expiry" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "profiles" ADD COLUMN "subscription_type" "subscription_type";