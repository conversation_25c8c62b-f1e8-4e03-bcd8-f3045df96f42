import { sql } from "drizzle-orm"
import { db } from "../db"

export async function restoreTrialFields() {
  console.log("Restoring trial fields to profiles table...")

  try {
    // Add trial_started_at column if it doesn't exist
    await db.execute(sql`
      ALTER TABLE "profiles" ADD COLUMN IF NOT EXISTS "trial_started_at" timestamp;
    `)

    // Add trial_ends_at column if it doesn't exist
    await db.execute(sql`
      ALTER TABLE "profiles" ADD COLUMN IF NOT EXISTS "trial_ends_at" timestamp;
    `)

    // Add has_trial_expired column if it doesn't exist
    await db.execute(sql`
      ALTER TABLE "profiles" ADD COLUMN IF NOT EXISTS "has_trial_expired" text DEFAULT 'false';
    `)

    console.log("Trial fields restored successfully!")
  } catch (error) {
    console.error("Error restoring trial fields:", error)
    throw error
  }
}

// Run this migration if executed directly
if (require.main === module) {
  restoreTrialFields()
    .then(() => process.exit(0))
    .catch(error => {
      console.error("Migration failed:", error)
      process.exit(1)
    })
}
