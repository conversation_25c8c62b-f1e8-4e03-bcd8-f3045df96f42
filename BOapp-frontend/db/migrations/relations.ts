import { relations } from "drizzle-orm/relations"
import { optimizations, measurements } from "./schema"

export const measurementsRelations = relations(measurements, ({ one }) => ({
  optimization: one(optimizations, {
    fields: [measurements.optimizationId],
    references: [optimizations.id]
  })
}))

export const optimizationsRelations = relations(optimizations, ({ many }) => ({
  measurements: many(measurements)
}))
