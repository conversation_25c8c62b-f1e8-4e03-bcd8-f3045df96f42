-- Create samples table
CREATE TABLE IF NOT EXISTS "samples" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  "optimization_id" uuid NOT NULL REFERENCES "optimizations"("id") ON DELETE CASCADE,
  "parameters" jsonb NOT NULL,
  "batch_id" text NOT NULL,
  "sample_index" integer NOT NULL,
  "sampling_method" text NOT NULL DEFAULT 'LHS',
  "seed" integer,
  "status" text NOT NULL DEFAULT 'pending',
  "target_values" jsonb,
  "created_at" timestamp NOT NULL DEFAULT now(),
  "updated_at" timestamp NOT NULL DEFAULT now(),
  "last_accessed_at" timestamp,
  "submitted_at" timestamp
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS "samples_optimization_id_idx" ON "samples" ("optimization_id");
CREATE INDEX IF NOT EXISTS "samples_batch_id_idx" ON "samples" ("batch_id");
CREATE INDEX IF NOT EXISTS "samples_status_idx" ON "samples" ("status");
