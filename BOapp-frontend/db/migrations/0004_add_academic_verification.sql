-- Add survey_type to user_surveys table
ALTER TABLE user_surveys ADD COLUMN survey_type TEXT NOT NULL DEFAULT 'regular';

-- Create academic_verifications table
CREATE TABLE IF NOT EXISTS academic_verifications (
  user_id TEXT PRIMARY KEY NOT NULL,
  full_name TEXT NOT NULL,
  email TEXT NOT NULL,
  institutional_email TEXT NOT NULL,
  institution TEXT NOT NULL,
  role TEXT NOT NULL,
  verification_status TEXT NOT NULL DEFAULT 'pending',
  verification_method TEXT,
  verification_timestamp TIMESTAMP,
  rejection_reason TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Add index on verification_status for faster queries
CREATE INDEX IF NOT EXISTS idx_academic_verifications_status ON academic_verifications(verification_status);
