CREATE TABLE "suggestions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"optimization_id" uuid NOT NULL,
	"parameters" jsonb NOT NULL,
	"batch_id" text NOT NULL,
	"suggestion_index" integer NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"target_values" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"last_accessed_at" timestamp,
	"submitted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "samples" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"optimization_id" uuid NOT NULL,
	"parameters" jsonb NOT NULL,
	"batch_id" text NOT NULL,
	"sample_index" integer NOT NULL,
	"sampling_method" text DEFAULT 'LHS' NOT NULL,
	"seed" integer,
	"sample_class" text DEFAULT 'exploratory' NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"target_values" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"last_accessed_at" timestamp,
	"submitted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "optimization_status_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"optimization_id" uuid NOT NULL,
	"previous_status" text NOT NULL,
	"new_status" text NOT NULL,
	"reason" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"created_by" text NOT NULL
);
--> statement-breakpoint
ALTER TABLE "profiles" ADD COLUMN "trial_started_at" timestamp;--> statement-breakpoint
ALTER TABLE "profiles" ADD COLUMN "trial_ends_at" timestamp;--> statement-breakpoint
ALTER TABLE "profiles" ADD COLUMN "has_trial_expired" text DEFAULT 'false';--> statement-breakpoint
ALTER TABLE "measurements" ADD COLUMN "target_values" jsonb;--> statement-breakpoint
ALTER TABLE "measurements" ADD COLUMN "batch_id" text;--> statement-breakpoint
ALTER TABLE "suggestions" ADD CONSTRAINT "suggestions_optimization_id_optimizations_id_fk" FOREIGN KEY ("optimization_id") REFERENCES "public"."optimizations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "samples" ADD CONSTRAINT "samples_optimization_id_optimizations_id_fk" FOREIGN KEY ("optimization_id") REFERENCES "public"."optimizations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "optimization_status_history" ADD CONSTRAINT "optimization_status_history_optimization_id_optimizations_id_fk" FOREIGN KEY ("optimization_id") REFERENCES "public"."optimizations"("id") ON DELETE cascade ON UPDATE no action;