import { sql } from "drizzle-orm"
import { db } from "../db"

export async function addSampleClassToSamples() {
  console.log("Adding sample_class column to samples table...")

  try {
    // Check if the column already exists
    const checkColumnExists = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'samples' AND column_name = 'sample_class'
    `)

    if (checkColumnExists.length === 0) {
      // Add the column if it doesn't exist
      await db.execute(sql`
        ALTER TABLE samples 
        ADD COLUMN sample_class TEXT NOT NULL DEFAULT 'exploratory'
      `)
      console.log("Added sample_class column to samples table")
    } else {
      console.log("sample_class column already exists in samples table")
    }

    return { success: true }
  } catch (error) {
    console.error("Error adding sample_class column to samples table:", error)
    return { success: false, error }
  }
}
