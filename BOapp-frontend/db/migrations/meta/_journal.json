{"version": "7", "dialect": "postgresql", "entries": [{"idx": 0, "version": "7", "when": 1744379152588, "tag": "0000_flaky_doctor_spectrum", "breakpoints": true}, {"idx": 1, "version": "7", "when": 1744721500000, "tag": "0001_add_target_values_column", "breakpoints": true}, {"idx": 2, "version": "7", "when": 1744721600000, "tag": "0002_add_batch_id_column", "breakpoints": true}, {"idx": 3, "version": "7", "when": 1744721700000, "tag": "0003_add_user_surveys_table", "breakpoints": true}, {"idx": 4, "version": "7", "when": 1744721800000, "tag": "0004_add_academic_verification", "breakpoints": true}, {"idx": 5, "version": "7", "when": 1744721900000, "tag": "0005_create_academic_verifications", "breakpoints": true}, {"idx": 6, "version": "7", "when": 1744722000000, "tag": "0006_add_verification_token", "breakpoints": true}, {"idx": 7, "version": "7", "when": 1746444421037, "tag": "0003_square_senator_kelly", "breakpoints": true}, {"idx": 8, "version": "7", "when": 1747082417444, "tag": "0007_luxuriant_hawkeye", "breakpoints": true}]}