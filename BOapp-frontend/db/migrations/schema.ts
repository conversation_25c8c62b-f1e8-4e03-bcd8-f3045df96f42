import {
  pgTable,
  text,
  timestamp,
  unique,
  uuid,
  jsonb,
  foreignKey,
  boolean,
  pgEnum
} from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const membership = pgEnum("membership", ["free", "pro"])

export const profiles = pgTable("profiles", {
  userId: text("user_id").primaryKey().notNull(),
  membership: membership().default("free").notNull(),
  stripeCustomerId: text("stripe_customer_id"),
  stripeSubscriptionId: text("stripe_subscription_id"),
  createdAt: timestamp("created_at", { mode: "string" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "string" }).defaultNow().notNull()
})

export const optimizations = pgTable(
  "optimizations",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    userId: text("user_id").notNull(),
    name: text().notNull(),
    description: text(),
    optimizerId: text("optimizer_id").notNull(),
    config: jsonb().notNull(),
    targetName: text("target_name").notNull(),
    targetMode: text("target_mode").notNull(),
    status: text().default("draft").notNull(),
    createdAt: timestamp("created_at", { mode: "string" })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp("updated_at", { mode: "string" })
      .defaultNow()
      .notNull()
  },
  table => [unique("optimizations_optimizer_id_unique").on(table.optimizerId)]
)

export const measurements = pgTable(
  "measurements",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    optimizationId: uuid("optimization_id").notNull(),
    parameters: jsonb().notNull(),
    targetValue: text("target_value").notNull(),
    isRecommended: boolean("is_recommended").default(true).notNull(),
    createdAt: timestamp("created_at", { mode: "string" })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp("updated_at", { mode: "string" })
      .defaultNow()
      .notNull(),
    targetValues: jsonb("target_values")
  },
  table => [
    foreignKey({
      columns: [table.optimizationId],
      foreignColumns: [optimizations.id],
      name: "measurements_optimization_id_optimizations_id_fk"
    }).onDelete("cascade")
  ]
)

export const userSurveys = pgTable("user_surveys", {
  userId: text("user_id").primaryKey().notNull(),
  responses: jsonb("responses").notNull(),
  completed: text("completed").default("true").notNull(),
  createdAt: timestamp("created_at", { mode: "string" }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { mode: "string" }).defaultNow().notNull()
})
