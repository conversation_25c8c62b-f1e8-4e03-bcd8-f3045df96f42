# User Feedback Feature

This document describes the user feedback feature implemented in the Optimizer™ platform.

## Overview

The user feedback feature allows users to provide feedback about their experience with the application directly through the UI. This helps collect valuable insights for improving the application.

## Features

- **Feedback Dialog**: A modal dialog that allows users to submit feedback with a title, description, and category.
- **Feedback <PERSON><PERSON> in Sidebar**: A button in the dashboard sidebar that opens the feedback dialog.
- **Floating Feedback Button**: A floating button for mobile users that appears at the bottom right of the screen.
- **Feedback Categories**: Users can categorize their feedback as bug reports, feature requests, usability issues, performance issues, or general feedback.
- **Metadata Collection**: The system automatically collects metadata such as the current page, browser information, and screen size to provide context for the feedback.

## Implementation Details

### Database Schema

The feedback is stored in a PostgreSQL table with the following schema:

```sql
CREATE TABLE IF NOT EXISTS feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  category TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  rating INTEGER,
  metadata JSONB,
  status TEXT NOT NULL DEFAULT 'new',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  resolved_at TIMESTAMP WITH TIME ZONE
);
```

### API Endpoints

- `POST /api/feedback`: Submit new feedback
- `GET /api/feedback`: Get all feedback for the current user
- `GET /api/feedback/:id`: Get a specific feedback item
- `PATCH /api/feedback/:id`: Update the status of a feedback item

### Components

- `FeedbackDialog`: The main dialog component for submitting feedback
- `FeedbackButton`: A button component that opens the feedback dialog
- `FloatingFeedbackButton`: A floating button for mobile users

### Server Actions

- `createFeedbackAction`: Create a new feedback entry
- `getUserFeedbackAction`: Get all feedback for the current user
- `getFeedbackByIdAction`: Get a specific feedback item
- `updateFeedbackStatusAction`: Update the status of a feedback item

## Usage

### For Users

Users can provide feedback in two ways:

1. Click the "Provide Feedback" button in the dashboard sidebar
2. On mobile devices, click the floating "Feedback" button at the bottom right of the screen

### For Developers

To add the feedback button to a new page:

```tsx
import { FeedbackButton } from "@/components/feedback/feedback-button"

// In your component
<FeedbackButton />
```

To add a floating feedback button:

```tsx
import { FloatingFeedbackButton } from "@/components/feedback/floating-feedback-button"

// In your component
<FloatingFeedbackButton />
```

## Database Migration

To create the feedback table in your database, use Drizzle:

```bash
# Generate migration files based on schema changes
npm run db:generate

# Apply migrations to the database
npm run db:push

# Or use the existing script
node scripts/run-drizzle-migration.js
```

## Future Enhancements

- Admin dashboard for viewing and managing feedback
- Email notifications for new feedback
- Ability to attach screenshots or files to feedback
- Integration with issue tracking systems
- Analytics dashboard for feedback trends
