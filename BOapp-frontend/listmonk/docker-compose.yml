version: '3'

services:
  listmonk:
    image: listmonk/listmonk:latest
    ports:
      - "9001:9000"
    environment:
      - TZ=UTC
      - LISTMONK_app.admin_username=listmonk
      - LISTMONK_app.admin_password=listmonk
      - LISTMONK_db.host=db
      - LISTMONK_db.port=5432
      - LISTMONK_db.user=listmonk
      - LISTMONK_db.password=listmonk
      - LISTMONK_db.database=listmonk
      - LISTMONK_db.ssl_mode=disable
    depends_on:
      - db
    restart: unless-stopped
    volumes:
      - ./uploads:/listmonk/uploads
      - ./config.toml:/listmonk/config.toml

  db:
    image: postgres:13
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=listmonk
      - POSTGRES_PASSWORD=listmonk
      - POSTGRES_DB=listmonk
    volumes:
      - listmonk-data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  listmonk-data:
