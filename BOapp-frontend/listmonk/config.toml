[app]
address = "0.0.0.0:9000"
admin_username = "listmonk"
admin_password = "listmonk"

# Set this to true to enable public subscription endpoints that don't require
# authentication.
enable_public_subscription_endpoints = true

# Set this to true to enable public archive endpoints that don't require
# authentication.
enable_public_archive_endpoints = true

# Enable or disable sending of e-mails. This is useful in dev/test environments.
enable_smtp = true

# SMTP configuration.
[smtp]
enabled = true
host = "smtp.example.com"
port = 587
auth_protocol = "plain"
username = "username"
password = "password"
hello_hostname = ""
email_headers = []
max_conns = 10
max_msg_retries = 2
idle_timeout = "15s"
wait_timeout = "5s"
keep_alive = "15s"
tls_enabled = true
tls_skip_verify = false
