import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"

export default function PrivacyPolicyPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mx-auto max-w-4xl">
        <Card>
          <CardHeader>
            <CardTitle>Privacy Policy & Cookie Information</CardTitle>
            <CardDescription>
              Last updated: {new Date().toLocaleDateString()}
            </CardDescription>
          </CardHeader>
          <CardContent className="prose prose-sm max-w-none">
            <h2>Cookie Usage</h2>
            <p>
              This website uses cookies to enhance your browsing experience and
              provide personalized content. We use different types of cookies
              for various purposes:
            </p>

            <h3>Types of Cookies We Use</h3>

            <h4>Necessary Cookies</h4>
            <p>
              These cookies are essential for the website to function properly.
              They enable basic functions like page navigation and access to
              secure areas of the website. The website cannot function properly
              without these cookies.
            </p>

            <h4>Functional Cookies</h4>
            <p>
              These cookies enable the website to provide enhanced functionality
              and personalization. They may be set by us or by third party
              providers whose services we have added to our pages.
            </p>

            <h4>Analytics Cookies</h4>
            <p>
              These cookies help us understand how visitors interact with our
              website by collecting and reporting information anonymously. This
              helps us improve our website's performance and user experience.
            </p>

            <h4>Marketing Cookies</h4>
            <p>
              These cookies are used to track visitors across websites. The
              intention is to display ads that are relevant and engaging for the
              individual user and thereby more valuable for publishers and third
              party advertisers.
            </p>

            <h3>Managing Your Cookie Preferences</h3>
            <p>
              You can manage your cookie preferences at any time by clicking the
              cookie settings button that appears on our website. You can also
              adjust your browser settings to refuse all cookies or to indicate
              when a cookie is being sent.
            </p>

            <h3>Third-Party Services</h3>
            <p>
              We use various third-party services that may set cookies,
              including:
            </p>
            <ul>
              <li>Google Analytics (for website analytics)</li>
              <li>Clerk (for authentication)</li>
              <li>Stripe (for payment processing)</li>
            </ul>

            <h3>Contact Information</h3>
            <p>
              If you have any questions about this privacy policy or our use of
              cookies, please contact us through our website.
            </p>

            <h3>Cookie Declaration</h3>
            <p>
              For a detailed list of all cookies used on this website, including
              their purpose and duration, please refer to our automatic cookie
              scanner results provided by CookieScript.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
