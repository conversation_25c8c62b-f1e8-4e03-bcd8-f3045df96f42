"use client"

import { <PERSON>ieConsentManager } from "@/components/utilities/cookie-consent-manager"
import { useCookieConsent } from "@/lib/hooks/use-cookie-consent"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useEffect, useState } from "react"

export default function TestCookiesPage() {
  const { consentState, hasAnalyticsConsent, hasMarketingConsent, isLoaded } =
    useCookieConsent()

  const [analyticsLoaded, setAnalyticsLoaded] = useState(false)
  const [marketingLoaded, setMarketingLoaded] = useState(false)

  // Simulate loading analytics when consent is granted
  useEffect(() => {
    if (hasAnalyticsConsent && !analyticsLoaded) {
      console.log(
        "🔍 Analytics consent granted - would load Google Analytics here"
      )
      setAnalyticsLoaded(true)
    } else if (!hasAnalyticsConsent && analyticsLoaded) {
      console.log(
        "🚫 Analytics consent revoked - would disable Google Analytics here"
      )
      setAnalyticsLoaded(false)
    }
  }, [hasAnalyticsConsent, analyticsLoaded])

  // Simulate loading marketing scripts when consent is granted
  useEffect(() => {
    if (hasMarketingConsent && !marketingLoaded) {
      console.log(
        "📢 Marketing consent granted - would load Facebook Pixel, etc. here"
      )
      setMarketingLoaded(true)
    } else if (!hasMarketingConsent && marketingLoaded) {
      console.log(
        "🚫 Marketing consent revoked - would disable marketing scripts here"
      )
      setMarketingLoaded(false)
    }
  }, [hasMarketingConsent, marketingLoaded])

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mx-auto max-w-4xl space-y-8">
        <div className="text-center">
          <h1 className="mb-4 text-3xl font-bold">Cookie Consent Test Page</h1>
          <p className="text-muted-foreground">
            This page demonstrates the CookieScript integration and cookie
            consent management.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Cookie Consent Manager */}
          <div>
            <CookieConsentManager />
          </div>

          {/* Consent State Display */}
          <Card>
            <CardHeader>
              <CardTitle>Consent State Details</CardTitle>
              <CardDescription>
                Real-time consent state from the useCookieConsent hook
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!isLoaded ? (
                <p className="text-muted-foreground">
                  Loading consent state...
                </p>
              ) : (
                <div className="space-y-3">
                  <ConsentStateItem
                    label="CookieScript Loaded"
                    value={isLoaded}
                  />
                  <ConsentStateItem
                    label="Necessary Cookies"
                    value={consentState.necessary}
                  />
                  <ConsentStateItem
                    label="Functional Cookies"
                    value={consentState.functional}
                  />
                  <ConsentStateItem
                    label="Analytics Cookies"
                    value={consentState.analytics}
                  />
                  <ConsentStateItem
                    label="Marketing Cookies"
                    value={consentState.marketing}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Simulated Script Loading */}
        <Card>
          <CardHeader>
            <CardTitle>Simulated Third-Party Scripts</CardTitle>
            <CardDescription>
              These represent how third-party scripts would be loaded based on
              consent
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="rounded-lg border p-4">
                <h4 className="mb-2 font-medium">Analytics Scripts</h4>
                <div className="flex items-center gap-2">
                  <Badge variant={analyticsLoaded ? "default" : "secondary"}>
                    {analyticsLoaded ? "Loaded" : "Blocked"}
                  </Badge>
                  <span className="text-muted-foreground text-sm">
                    Google Analytics, Hotjar, etc.
                  </span>
                </div>
              </div>

              <div className="rounded-lg border p-4">
                <h4 className="mb-2 font-medium">Marketing Scripts</h4>
                <div className="flex items-center gap-2">
                  <Badge variant={marketingLoaded ? "default" : "secondary"}>
                    {marketingLoaded ? "Loaded" : "Blocked"}
                  </Badge>
                  <span className="text-muted-foreground text-sm">
                    Facebook Pixel, Google Ads, etc.
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <ol className="list-inside list-decimal space-y-2 text-sm">
              <li>
                Open your browser's developer console to see consent change logs
              </li>
              <li>
                Try accepting all cookies and watch the consent state update
              </li>
              <li>Try rejecting cookies and see how scripts are blocked</li>
              <li>
                Use "Customize Settings" to selectively enable/disable cookie
                categories
              </li>
              <li>
                Refresh the page to see the consent banner appear again (if
                configured)
              </li>
            </ol>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

interface ConsentStateItemProps {
  label: string
  value: boolean
}

function ConsentStateItem({ label, value }: ConsentStateItemProps) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-sm font-medium">{label}</span>
      <Badge variant={value ? "default" : "secondary"}>
        {value ? "Granted" : "Denied"}
      </Badge>
    </div>
  )
}
