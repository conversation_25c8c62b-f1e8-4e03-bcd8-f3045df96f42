/*
This server page provides the academic signup form for users to apply for extended academic access.
*/

"use server"

import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import AcademicSignupForm from "./_components/academic-signup-form"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { checkAcademicVerificationStatusAction } from "@/actions/academic-verification-actions"

export default async function AcademicSignupPage() {
  const { userId } = await auth()

  // If user is not authenticated, redirect to login
  if (!userId) {
    redirect("/login?redirect_url=/academic-signup")
  }

  // Check if the user has already submitted a verification request
  const verificationStatus = await checkAcademicVerificationStatusAction()

  // If the user has already been verified, redirect to the academic survey
  if (
    verificationStatus.isSuccess &&
    verificationStatus.status === "approved"
  ) {
    redirect("/academic-survey")
  }

  return (
    <div className="container mx-auto max-w-5xl px-4 py-12">
      <div className="mx-auto mb-12 max-w-2xl text-center">
        <h1 className="mb-4 text-4xl font-bold">Academic Access Application</h1>
        <p className="text-muted-foreground">
          Eligible academic institutions can qualify for extended 90-day access.
          Complete the form below to apply.
        </p>
      </div>

      <Card className="mx-auto max-w-2xl">
        <CardHeader>
          <CardTitle>Academic Verification</CardTitle>
          <CardDescription>
            Please provide your academic information for verification. We'll
            verify your institutional email to confirm your academic status.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AcademicSignupForm
            existingData={verificationStatus.data}
            verificationStatus={verificationStatus.status}
          />
        </CardContent>
      </Card>
    </div>
  )
}
