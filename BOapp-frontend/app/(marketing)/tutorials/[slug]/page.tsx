/*
 * This server page displays individual tutorial content based on the slug.
 */

"use server"

import { notFound } from "next/navigation"
import { BookOpen } from "lucide-react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"
import { BayesianOptimizationIntro } from "@/components/tutorials/bayesian-optimization-intro"
import { CompleteWorkflowGuide } from "@/components/tutorials/complete-workflow-guide"

// This would typically come from a database or CMS
// For now, we'll hardcode the available tutorials
const availableTutorials = [
  "bayesian-optimization-intro",
  "sequential-vs-parallel",
  "complete-workflow-guide"
]

interface TutorialPageProps {
  params: Promise<{
    slug: string
  }>
}

export default async function TutorialPage({ params }: TutorialPageProps) {
  const { slug } = await params

  // Check if the tutorial exists
  if (!availableTutorials.includes(slug)) {
    notFound()
  }

  return (
    <div className="container mx-auto max-w-4xl px-4 py-12">
      <div className="mb-8">
        <Link href="/tutorials">
          <Button variant="ghost" className="flex items-center gap-2 pl-0">
            <ChevronLeft className="size-4" />
            <span>Back to Tutorials</span>
          </Button>
        </Link>
      </div>

      {/* Dynamic content based on slug */}
      {slug === "bayesian-optimization-intro" && (
        <div className="space-y-8">
          <div className="mb-8">
            <div className="mb-2 flex items-center gap-3">
              <div className="bg-primary text-primary-foreground rounded-full p-2">
                <BookOpen className="size-5" />
              </div>
              <span className="text-muted-foreground text-sm font-medium">
                Beginner
              </span>
            </div>
            <h1 className="mb-4 text-4xl font-bold">
              Familiarize with Bayesian Optimization
            </h1>
            <p className="text-muted-foreground text-lg">
              Learn the fundamentals of Bayesian optimization and how it can
              guide your experimental design process.
            </p>
          </div>

          {/* Tutorial content imported from component */}
          <div className="prose prose-slate dark:prose-invert max-w-none">
            <BayesianOptimizationIntro />
          </div>
        </div>
      )}

      {slug === "complete-workflow-guide" && (
        <div className="space-y-8">
          <div className="mb-8">
            <div className="mb-2 flex items-center gap-3">
              <div className="bg-primary text-primary-foreground rounded-full p-2">
                <BookOpen className="size-5" />
              </div>
              <span className="text-muted-foreground text-sm font-medium">
                Practical
              </span>
            </div>
            <h1 className="mb-4 text-4xl font-bold">
              Complete Workflow Guide: From Setup to Results
            </h1>
            <p className="text-muted-foreground text-lg">
              Learn how to use INNOptimizer™ effectively for real optimization
              projects, from creating your first optimization to analyzing
              results.
            </p>
          </div>

          {/* Tutorial content imported from component */}
          <div className="prose prose-slate dark:prose-invert max-w-none">
            <CompleteWorkflowGuide />
          </div>
        </div>
      )}
    </div>
  )
}
