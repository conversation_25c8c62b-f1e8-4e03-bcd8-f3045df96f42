"use server"

import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { checkAcademicSurveyCompletionAction } from "@/actions/academic-survey-actions"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import AcademicSurveyForm from "@/components/survey/academic-survey-form"

export default async function AcademicSurveyPage() {
  const { userId } = await auth()

  // If user is not authenticated, redirect to login
  if (!userId) {
    redirect("/login")
  }

  // Check if the user has already completed the survey
  const surveyStatus = await checkAcademicSurveyCompletionAction()

  // If the user has already completed the survey, redirect to dashboard
  if (surveyStatus.isSuccess && surveyStatus.hasCompleted) {
    redirect("/dashboard/home")
  }

  return (
    <div className="container mx-auto flex min-h-screen flex-col items-center justify-center px-4 py-12">
      <Card className="mx-auto w-full max-w-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            Academic User Survey
          </CardTitle>
          <CardDescription>
            <span className="font-semibold">
              Thank you for verifying your academic status!
            </span>{" "}
            You now qualify for extended 90-day access.
            <span className="mt-2 block font-medium text-amber-600 dark:text-amber-400">
              This survey is required to complete your academic registration and
              access the dashboard.
            </span>
            Please help us understand your needs by answering a few questions
            about your experience with Bayesian optimization tools.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <p className="text-muted-foreground">
              Your responses will help us improve our platform for academic
              users and provide better support for your research needs.
            </p>

            <AcademicSurveyForm />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
