"use server"

import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { checkAcademicVerificationStatusAction } from "@/actions/academic-verification-actions"

export default async function AcademicSurveyLayout({
  children
}: {
  children: React.ReactNode
}) {
  const { userId } = await auth()

  // If user is not authenticated, redirect to login
  if (!userId) {
    redirect("/login?redirect_url=/academic-survey")
  }

  // Check if the user has been verified
  const verificationStatus = await checkAcademicVerificationStatusAction()

  // If the user hasn't been verified, redirect to the academic signup page
  if (
    !verificationStatus.isSuccess ||
    !verificationStatus.status ||
    verificationStatus.status !== "approved"
  ) {
    redirect("/academic-signup")
  }

  return <>{children}</>
}
