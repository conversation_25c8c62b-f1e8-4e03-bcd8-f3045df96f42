"use server"

import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { checkSurveyCompletionAction } from "@/actions/survey-actions"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import SurveyForm from "@/components/survey/survey-form"
import { BRAND } from "@/lib/constants"

export default async function WelcomePage({
  searchParams
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const { userId } = await auth()

  // If user is not authenticated, redirect to login
  if (!userId) {
    redirect("/login")
  }

  // Get the search params
  const params = await searchParams

  // Check if the user came from a successful checkout
  const checkoutSuccess = params.checkout_success === "true"
  const sessionId = params.session_id as string | undefined
  // Check if the user came from the dashboard
  const fromDashboard = params.from === "dashboard"

  console.log("Welcome page params:", {
    checkoutSuccess,
    sessionId,
    fromDashboard,
    allParams: params
  })

  // Check if the user has already completed the survey
  console.log("Welcome page: Checking survey completion for user", userId)
  const surveyStatus = await checkSurveyCompletionAction()
  console.log("Welcome page: Survey status:", surveyStatus)

  // If the user has already completed the survey, redirect to dashboard
  if (surveyStatus.isSuccess && surveyStatus.hasCompleted) {
    console.log(
      "Welcome page: User has completed survey, redirecting to dashboard"
    )
    redirect("/dashboard/home")
  } else {
    console.log(
      "Welcome page: User has not completed survey, showing survey form"
    )
  }

  return (
    <div className="container mx-auto flex min-h-screen flex-col items-center justify-center px-4 py-12">
      <Card className="mx-auto w-full max-w-lg">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">
            {checkoutSuccess
              ? "Thank You for Your Subscription!"
              : fromDashboard
                ? "Complete Your Profile"
                : `Welcome to ${BRAND.NAME}!`}
          </CardTitle>
          <CardDescription>
            {checkoutSuccess
              ? "Your premium subscription has been activated. To complete your setup, please answer a few quick questions."
              : fromDashboard
                ? "Before you can access the dashboard, please complete this quick survey to help us tailor your experience."
                : "We're excited to have you on board. Help us tailor your experience by answering a few quick questions."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {checkoutSuccess && (
              <div className="mb-4 rounded-md bg-green-50 p-4 dark:bg-green-900/20">
                <p className="text-sm text-green-800 dark:text-green-200">
                  <span className="font-semibold">
                    Subscription Successful!
                  </span>{" "}
                  Your payment has been processed and your premium account is
                  now active. You now have full access to all premium features.
                </p>
              </div>
            )}

            <div className="mb-4 rounded-md bg-blue-50 p-4 dark:bg-blue-900/20">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>
                  This survey is required to access the dashboard.
                </strong>{" "}
                Your responses help us customize your experience and provide
                relevant recommendations for your specific needs.
              </p>
            </div>

            <p className="text-muted-foreground">
              {BRAND.NAME} is a powerful platform for experimental design and
              optimization. Your responses will help us customize your
              experience and provide relevant recommendations for your specific
              needs.
            </p>

            <SurveyForm />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
