"use client"

import { useSearchParams } from "next/navigation"
import { RedirectHandler } from "@/components/auth/redirect-handler"

/**
 * This page handles redirects after authentication
 * It extracts the redirect URL from the query parameters
 * and uses the RedirectHandler component to handle the redirect
 */
export default function AuthRedirectPage() {
  const searchParams = useSearchParams()

  // Get the redirect URL from the query parameters
  const redirectUrl = searchParams.get("redirect_url") || "/welcome"

  // Get the plan type from the query parameters
  const planType = searchParams.get("plan")

  // Store the plan type in localStorage if it exists
  if (typeof window !== "undefined" && planType) {
    localStorage.setItem("selectedPlanType", planType)
    console.log("AuthRedirectPage: Stored plan type in localStorage:", planType)
  }

  console.log("AuthRedirectPage: Redirecting to:", redirectUrl)

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <h1 className="mb-4 text-2xl font-bold">Redirecting...</h1>
        <p className="text-muted-foreground">
          Please wait while we redirect you.
        </p>
        <RedirectHandler redirectUrl={redirectUrl} />
      </div>
    </div>
  )
}
