/*
This client page provides the signup form from Clerk.
*/

"use client"

import { SignUp } from "@clerk/nextjs"
import { dark } from "@clerk/themes"
import { useTheme } from "next-themes"
import { useSearchParams } from "next/navigation"

export default function SignUpPage() {
  const { theme } = useTheme()
  const searchParams = useSearchParams()

  // Get the redirect URL from the query string if it exists
  const baseRedirectUrl = searchParams.get("redirect_url") || "/welcome"

  // Get the plan type if it exists
  const planType = searchParams.get("plan")

  // Instead of redirecting directly to the final URL, redirect to our auth-redirect page
  // which will handle adding the plan parameter to the URL
  const authRedirectUrl = `/auth-redirect?redirect_url=${encodeURIComponent(baseRedirectUrl)}`

  // If we have a plan type, add it to the auth-redirect URL
  const finalRedirectUrl = planType
    ? `${authRedirectUrl}&plan=${encodeURIComponent(planType)}`
    : authRedirectUrl

  console.log(
    "Signup page redirecting to:",
    finalRedirectUrl,
    "with plan:",
    planType
  )

  return (
    <SignUp
      redirectUrl={finalRedirectUrl}
      appearance={{ baseTheme: theme === "dark" ? dark : undefined }}
    />
  )
}
