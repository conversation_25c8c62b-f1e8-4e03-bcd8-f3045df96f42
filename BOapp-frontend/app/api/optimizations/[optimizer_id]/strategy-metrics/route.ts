import { NextRequest, NextResponse } from "next/server"
import { API_CONFIG } from "@/lib/config"

// Define a type for the URL params (optimizer_id)
type Params = {
  optimizer_id: string
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<Params> } // Corrected to await params
) {
  try {
    const { searchParams } = new URL(request.url)
    const windowSize = searchParams.get("window_size") || "5"
    const beta = searchParams.get("beta") || "0.2"

    // Await the params before using them
    const { optimizer_id } = await params

    // Use the API URL from the centralized config
    const apiUrl = `${API_CONFIG.BASE_URL}/optimizations/${optimizer_id}/strategy-metrics?window_size=${windowSize}&beta=${beta}`

    console.log("Strategy Metrics: Fetching from", apiUrl)

    // Call the backend API with dynamic optimizer_id
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-API-Key": API_CONFIG.API_KEY
      }
    })

    const data = await response.json()

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching strategy metrics:", error)
    return NextResponse.json(
      {
        status: "error",
        message:
          error instanceof Error ? error.message : "An unknown error occurred"
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<Params> } // Same fix for POST method
) {
  try {
    const body = await request.json()

    // Await the params before using them
    const { optimizer_id } = await params

    // Use the API URL from the centralized config
    const apiUrl = `${API_CONFIG.BASE_URL}/optimizations/${optimizer_id}/strategy-metrics`

    console.log("Strategy Metrics POST: Sending to", apiUrl)

    // Make the POST request to the backend API
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-API-Key": API_CONFIG.API_KEY
      },
      body: JSON.stringify(body)
    })

    const data = await response.json()

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching strategy metrics:", error)
    return NextResponse.json(
      {
        status: "error",
        message:
          error instanceof Error ? error.message : "An unknown error occurred"
      },
      { status: 500 }
    )
  }
}
