import { NextRequest, NextResponse } from "next/server"
import { db } from "@/db/db"
import { strategyMetricsTable } from "@/db/schema/strategy-metrics-schema"
import { eq } from "drizzle-orm"
import { optimizationsTable } from "@/db/schema/optimizations-schema"
import { getStrategyMetricsAction, checkStrategyMetricsNeedUpdateAction, upsertStrategyMetricsAction, getAcquisitionFunctionTypeAction } from "@/actions/db/strategy-metrics-actions"

export async function GET(
  request: NextRequest,
  { params }: { params: { optimizer_id: string } }
) {
  try {
    // Await the params object to get the optimizer_id
    const optimizer_id = params.optimizer_id

    const { searchParams } = new URL(request.url)
    const windowSize = searchParams.get("window_size") || "5"
    const beta = searchParams.get("beta") || "0.2"
    const forceBackend = searchParams.get("force_backend") === "true"

    console.log(`Fetching strategy metrics for optimizer ID: ${optimizer_id}`)

    // First, try to get metrics from the database
    const optimization = await db
      .select()
      .from(optimizationsTable)
      .where(eq(optimizationsTable.optimizerId, optimizer_id))
      .limit(1)

    if (optimization.length === 0) {
      return NextResponse.json({
        status: "error",
        message: "Optimization not found"
      }, { status: 404 })
    }

    const optimizationId = optimization[0].id

    // Check if we need to update the metrics
    const needsUpdateResult = await checkStrategyMetricsNeedUpdateAction(optimizationId)
    const needsUpdate = needsUpdateResult.isSuccess && needsUpdateResult.data

    // If metrics don't need updating and we're not forcing backend, return them from the database
    if (!needsUpdate && !forceBackend) {
      const metricsResult = await getStrategyMetricsAction(optimizationId)

      if (metricsResult.isSuccess && metricsResult.data) {
        // Convert database format to API format
        const metrics = {
          parameter_diversity: metricsResult.data.parameterDiversity,
          improvement_rates: metricsResult.data.improvementRates,
          ucb_components: metricsResult.data.ucbComponents,
          exploration_score: metricsResult.data.explorationScore,
          exploitation_score: metricsResult.data.exploitationScore,
          normalized_exploration: metricsResult.data.normalizedExploration,
          normalized_exploitation: metricsResult.data.normalizedExploitation,
          balance: metricsResult.data.balance,
          dominant_strategy: metricsResult.data.dominantStrategy,
          strategy_shifts: metricsResult.data.strategyShifts
        }

        return NextResponse.json({
          status: "success",
          metrics,
          message: "Strategy metrics retrieved from database",
          lastUpdated: metricsResult.data.updatedAt
        })
      }
    }

    // If we need to update or don't have metrics, fetch from backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000"

    console.log(`Fetching strategy metrics from backend: ${backendUrl}/optimizations/${optimizer_id}/strategy-metrics?window_size=${windowSize}&beta=${beta}`)

    // Forward the request to the backend
    const response = await fetch(
      `${backendUrl}/optimizations/${optimizer_id}/strategy-metrics?window_size=${windowSize}&beta=${beta}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    )

    console.log(`Backend response status: ${response.status}`)

    // Get the response data
    const data = await response.json()

    // Log detailed information about the response
    console.log(`Backend response data:`, data)

    // Check if we have UCB components
    if (data.status === 'success' && data.metrics) {
      const ucbComponents = data.metrics.ucb_components?.experiments || []
      console.log(`UCB components count: ${ucbComponents.length}`)

      if (ucbComponents.length === 0) {
        console.log('Warning: No UCB components returned from backend. This may indicate:')
        console.log('1. The campaign does not have a recommender')
        console.log('2. The recommender does not have a surrogate model')
        console.log('3. There are fewer than 2 measurements')
      }

      // Get the acquisition function type
      const acquisitionResult = await getAcquisitionFunctionTypeAction(optimizationId)
      const acquisitionFunction = acquisitionResult.isSuccess ? acquisitionResult.data : "qUpperConfidenceBound"

      // Get the latest measurement ID to track updates
      const latestMeasurement = await db
        .select()
        .from(measurementsTable)
        .where(eq(measurementsTable.optimizationId, optimizationId))
        .orderBy(desc(measurementsTable.createdAt))
        .limit(1)

      const lastExperimentId = latestMeasurement.length > 0 ? latestMeasurement[0].id : null

      // Store the metrics in the database
      await upsertStrategyMetricsAction({
        optimizationId,
        parameterDiversity: data.metrics.parameter_diversity || {},
        improvementRates: data.metrics.improvement_rates || {},
        ucbComponents: data.metrics.ucb_components || { experiments: [] },
        explorationScore: data.metrics.exploration_score || 0,
        exploitationScore: data.metrics.exploitation_score || 0,
        normalizedExploration: data.metrics.normalized_exploration || 0.5,
        normalizedExploitation: data.metrics.normalized_exploitation || 0.5,
        balance: data.metrics.balance || 0,
        dominantStrategy: data.metrics.dominant_strategy || "balanced",
        strategyShifts: data.metrics.strategy_shifts || [],
        windowSize: parseInt(windowSize),
        beta: parseFloat(beta),
        acquisitionFunction,
        experimentCount: ucbComponents.length,
        lastExperimentId
      })
    }

    // Return the response from the backend
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching strategy metrics:", error)
    return NextResponse.json(
      {
        status: "error",
        message: error instanceof Error ? error.message : "An unknown error occurred",
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { optimizer_id: string } }
) {
  try {
    // Await the params object to get the optimizer_id
    const optimizer_id = params.optimizer_id

    // Get the request body
    const body = await request.json()
    const { window_size = 5, beta = 0.2 } = body

    // First, try to get the optimization from the database
    const optimization = await db
      .select()
      .from(optimizationsTable)
      .where(eq(optimizationsTable.optimizerId, optimizer_id))
      .limit(1)

    if (optimization.length === 0) {
      return NextResponse.json({
        status: "error",
        message: "Optimization not found"
      }, { status: 404 })
    }

    const optimizationId = optimization[0].id

    // Get the backend URL from environment variables or use default
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000"

    console.log(`Posting strategy metrics to backend for optimizer ID: ${optimizer_id}`)

    // Forward the request to the backend
    const response = await fetch(
      `${backendUrl}/optimizations/${optimizer_id}/strategy-metrics`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body)
      }
    )

    console.log(`Backend response status: ${response.status}`)

    // Get the response data
    const data = await response.json()

    console.log(`Backend response data:`, data)

    // If successful, store the metrics in the database
    if (data.status === 'success' && data.metrics) {
      // Get the acquisition function type
      const acquisitionResult = await getAcquisitionFunctionTypeAction(optimizationId)
      const acquisitionFunction = acquisitionResult.isSuccess ? acquisitionResult.data : "qUpperConfidenceBound"

      const ucbComponents = data.metrics.ucb_components?.experiments || []

      // Get the latest measurement ID to track updates
      const latestMeasurement = await db
        .select()
        .from(measurementsTable)
        .where(eq(measurementsTable.optimizationId, optimizationId))
        .orderBy(desc(measurementsTable.createdAt))
        .limit(1)

      const lastExperimentId = latestMeasurement.length > 0 ? latestMeasurement[0].id : null

      // Store the metrics in the database
      await upsertStrategyMetricsAction({
        optimizationId,
        parameterDiversity: data.metrics.parameter_diversity || {},
        improvementRates: data.metrics.improvement_rates || {},
        ucbComponents: data.metrics.ucb_components || { experiments: [] },
        explorationScore: data.metrics.exploration_score || 0,
        exploitationScore: data.metrics.exploitation_score || 0,
        normalizedExploration: data.metrics.normalized_exploration || 0.5,
        normalizedExploitation: data.metrics.normalized_exploitation || 0.5,
        balance: data.metrics.balance || 0,
        dominantStrategy: data.metrics.dominant_strategy || "balanced",
        strategyShifts: data.metrics.strategy_shifts || [],
        windowSize: typeof window_size === 'number' ? window_size : parseInt(window_size.toString()),
        beta: typeof beta === 'number' ? beta : parseFloat(beta.toString()),
        acquisitionFunction,
        experimentCount: ucbComponents.length,
        lastExperimentId
      })
    }

    // Return the response
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching strategy metrics:", error)
    return NextResponse.json(
      {
        status: "error",
        message: error instanceof Error ? error.message : "An unknown error occurred",
      },
      { status: 500 }
    )
  }
}
