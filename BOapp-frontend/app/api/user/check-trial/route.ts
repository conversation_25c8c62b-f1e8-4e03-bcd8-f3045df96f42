import { auth } from "@clerk/nextjs/server"
import { ensureUserTrialAction } from "@/actions/ensure-trial-action"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    const { userId } = await auth()

    if (!userId) {
      return new Response("Unauthorized", { status: 401 })
    }

    // Check and initialize trial if needed
    const result = await ensureUserTrialAction()

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error checking trial:", error)
    return NextResponse.json(
      {
        success: false,
        message: `Error checking trial: ${error instanceof Error ? error.message : "Unknown error"}`
      },
      { status: 500 }
    )
  }
}
