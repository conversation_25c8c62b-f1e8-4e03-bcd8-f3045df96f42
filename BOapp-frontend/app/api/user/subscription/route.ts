import { auth } from "@clerk/nextjs/server"
import { NextResponse } from "next/server"
import { getProfileByUserIdAction } from "@/actions/db/profiles-actions"
import { checkTrialStatusAction } from "@/actions/trial-actions"

export async function GET() {
  const { userId } = await auth()

  if (!userId) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  try {
    // Get user profile
    const profileResult = await getProfileByUserIdAction(userId)

    // If user has a Stripe subscription
    if (
      profileResult.isSuccess &&
      profileResult.data &&
      profileResult.data.stripeSubscriptionId
    ) {
      return NextResponse.json({
        tier: profileResult.data.membership || "pro",
        status: "active",
        stripeSubscriptionId: profileResult.data.stripeSubscriptionId,
        stripeCustomerId: profileResult.data.stripeCustomerId,
        subscriptionType: profileResult.data.subscriptionType || null
      })
    }

    // Check trial status
    const trialResult = await checkTrialStatusAction(userId)

    if (trialResult.isSuccess && trialResult.data?.isActive) {
      // Return actual trial data from database
      return NextResponse.json({
        tier: "trial",
        trialEndsAt: trialResult.data.trialEndsAt,
        trialDaysLeft: trialResult.data.daysLeft,
        status: "active"
      })
    }

    // If no trial or subscription, return free tier
    return NextResponse.json({
      tier:
        profileResult.isSuccess && profileResult.data
          ? profileResult.data.membership || "free"
          : "free",
      status: "active"
    })
  } catch (error) {
    console.error("Error getting subscription data:", error)

    // Return a fallback response in case of error
    return NextResponse.json(
      {
        tier: "free",
        status: "active",
        error: "Failed to retrieve subscription data"
      },
      { status: 500 }
    )
  }
}
