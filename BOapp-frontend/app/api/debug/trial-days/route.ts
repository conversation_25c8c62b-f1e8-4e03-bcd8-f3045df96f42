import { auth } from "@clerk/nextjs/server"
import { getProfileByUserIdAction } from "@/actions/db/profiles-actions"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    const { userId } = await auth()

    if (!userId) {
      return new Response("Unauthorized", { status: 401 })
    }

    // Get user profile
    const profileResult = await getProfileByUserIdAction(userId)

    if (!profileResult.isSuccess || !profileResult.data) {
      return NextResponse.json({ error: "Profile not found" }, { status: 404 })
    }

    const profile = profileResult.data

    // If user doesn't have trial data, return error
    if (!profile.trialStartedAt || !profile.trialEndsAt) {
      return NextResponse.json(
        { error: "No trial data found" },
        { status: 404 }
      )
    }

    const now = new Date()
    const trialStartDate = new Date(profile.trialStartedAt)
    const trialEndDate = new Date(profile.trialEndsAt)

    // Calculate days left using different methods
    const daysLeftMethod1 = Math.ceil(
      (trialEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    )

    const daysLeftMethod2 = Math.max(
      0,
      Math.ceil((trialEndDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
    )

    // Calculate total trial days
    const totalTrialDays = Math.ceil(
      (trialEndDate.getTime() - trialStartDate.getTime()) /
        (1000 * 60 * 60 * 24)
    )

    return NextResponse.json({
      profile: {
        userId: profile.userId,
        membership: profile.membership,
        trialStartedAt: profile.trialStartedAt,
        trialEndsAt: profile.trialEndsAt,
        hasTrialExpired: profile.hasTrialExpired
      },
      calculations: {
        now: now.toISOString(),
        trialStartDate: trialStartDate.toISOString(),
        trialEndDate: trialEndDate.toISOString(),
        daysLeftMethod1,
        daysLeftMethod2,
        totalTrialDays,
        nowTimestamp: now.getTime(),
        trialEndTimestamp: trialEndDate.getTime(),
        difference: trialEndDate.getTime() - now.getTime(),
        differenceInDays:
          (trialEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
      }
    })
  } catch (error) {
    console.error("Error in debug trial days:", error)
    return NextResponse.json(
      {
        error: `Error in debug trial days: ${error instanceof Error ? error.message : "Unknown error"}`
      },
      { status: 500 }
    )
  }
}
