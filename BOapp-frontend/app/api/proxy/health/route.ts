// app/api/proxy/health/route.ts
import { NextRequest, NextResponse } from "next/server"
import { API_CONFIG } from "@/lib/config"

/**
 * Proxy endpoint for API health check to avoid CORS issues
 */
export async function GET(request: NextRequest) {
  try {
    // Get the API URL from the centralized config
    const apiUrl = `${API_CONFIG.BASE_URL}/health`

    console.log("Health Proxy: Fetching from", apiUrl)

    // Make the request to the API
    const response = await fetch(apiUrl, {
      headers: {
        "X-API-Key": API_CONFIG.API_KEY,
        Accept: "application/json",
        "Content-Type": "application/json"
      },
      // Add a reasonable timeout
      signal: AbortSignal.timeout(10000)
    })

    // Get the response as text first for debugging
    const responseText = await response.text()
    console.log("Health Proxy: Raw response:", responseText)

    // Try to parse the response as JSON
    let data
    try {
      data = JSON.parse(responseText)
      console.log("Health Proxy: Parsed JSON:", data)
    } catch (error) {
      console.error("Health Proxy: Failed to parse JSON:", error)
      return NextResponse.json(
        { error: "Failed to parse API response as JSON", raw: responseText },
        { status: 500 }
      )
    }

    // Return the data
    return NextResponse.json(data)
  } catch (error) {
    console.error("Health Proxy error:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch API health",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}
