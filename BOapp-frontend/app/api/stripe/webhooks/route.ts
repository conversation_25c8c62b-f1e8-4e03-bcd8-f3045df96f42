/*
This API route handles Stripe webhook events to manage subscription status changes and updates user profiles accordingly.
*/

import {
  manageSubscriptionStatusChange,
  updateStripeCustomer
} from "@/actions/stripe-actions"
import { stripe } from "@/lib/stripe"
import { headers } from "next/headers"
import <PERSON><PERSON> from "stripe"

const relevantEvents = new Set([
  "checkout.session.completed",
  "customer.subscription.updated",
  "customer.subscription.deleted"
])

export async function POST(req: Request) {
  const body = await req.text()
  const sig = (await headers()).get("Stripe-Signature") as string
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET
  let event: Stripe.Event

  console.log("Received webhook event")

  try {
    if (!sig || !webhookSecret) {
      console.error("Webhook secret or signature missing")
      console.log("Webhook secret:", webhookSecret ? "Present" : "Missing")
      console.log("Signature:", sig ? "Present" : "Missing")
      throw new Error("Webhook secret or signature missing")
    }

    event = stripe.webhooks.constructEvent(body, sig, webhookSecret)
    console.log("Webhook event constructed successfully:", event.type)
  } catch (err: any) {
    console.error(`Webhook Error: ${err.message}`)
    return new Response(`Webhook Error: ${err.message}`, { status: 400 })
  }

  if (relevantEvents.has(event.type)) {
    console.log(`Processing relevant event: ${event.type}`)
    try {
      switch (event.type) {
        case "customer.subscription.updated":
          console.log("Processing subscription update event")
          await handleSubscriptionChange(event)
          break

        case "customer.subscription.deleted":
          console.log("Processing subscription deletion event")
          await handleSubscriptionChange(event)
          break

        case "checkout.session.completed":
          console.log("Processing checkout session completed event")
          await handleCheckoutSession(event)
          break

        default:
          console.error(`Unhandled relevant event: ${event.type}`)
          throw new Error("Unhandled relevant event!")
      }
      console.log(`Successfully processed event: ${event.type}`)
    } catch (error) {
      console.error("Webhook handler failed:", error)
      return new Response(
        "Webhook handler failed. View your nextjs function logs.",
        { status: 400 }
      )
    }
  } else {
    console.log(`Ignoring irrelevant event: ${event.type}`)
  }

  return new Response(JSON.stringify({ received: true }))
}

async function handleSubscriptionChange(event: Stripe.Event) {
  const subscription = event.data.object as Stripe.Subscription

  // Get the product ID from the subscription
  // The product might be returned as an object or a string depending on the expand parameter
  let productId: string

  if (typeof subscription.items.data[0].price.product === "string") {
    productId = subscription.items.data[0].price.product
  } else if (
    typeof subscription.items.data[0].price.product === "object" &&
    subscription.items.data[0].price.product !== null
  ) {
    // If it's an object, extract the ID
    productId = (subscription.items.data[0].price.product as any).id
  } else {
    console.error(
      "Invalid product format:",
      subscription.items.data[0].price.product
    )
    throw new Error("Invalid product format")
  }

  console.log("Handling subscription change with product ID:", productId)

  await manageSubscriptionStatusChange(
    subscription.id,
    subscription.customer as string,
    productId
  )
}

async function handleCheckoutSession(event: Stripe.Event) {
  const checkoutSession = event.data.object as Stripe.Checkout.Session

  console.log("Processing checkout session:", {
    id: checkoutSession.id,
    mode: checkoutSession.mode,
    client_reference_id: checkoutSession.client_reference_id,
    customer: checkoutSession.customer,
    subscription: checkoutSession.subscription,
    metadata: checkoutSession.metadata
  })

  if (checkoutSession.mode === "subscription") {
    // Make sure we have a client_reference_id (user ID)
    if (!checkoutSession.client_reference_id) {
      console.error(
        "Missing client_reference_id in checkout session:",
        checkoutSession.id
      )
      return
    }

    // Make sure we have a subscription ID
    const subscriptionId = checkoutSession.subscription as string
    if (!subscriptionId) {
      console.error(
        "Missing subscription ID in checkout session:",
        checkoutSession.id
      )
      return
    }

    // Make sure we have a customer ID
    const customerId = checkoutSession.customer as string
    if (!customerId) {
      console.error(
        "Missing customer ID in checkout session:",
        checkoutSession.id
      )
      return
    }

    console.log("Updating Stripe customer:", {
      userId: checkoutSession.client_reference_id,
      subscriptionId,
      customerId
    })

    // Update the user's profile with the Stripe customer and subscription IDs
    await updateStripeCustomer(
      checkoutSession.client_reference_id,
      subscriptionId,
      customerId
    )

    // Retrieve the subscription to get the product ID
    console.log("Retrieving subscription:", subscriptionId)
    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ["default_payment_method", "items.data.price.product"]
    })

    // Get the product ID from the subscription
    // The product might be returned as an object or a string depending on the expand parameter
    let productId: string

    if (typeof subscription.items.data[0].price.product === "string") {
      productId = subscription.items.data[0].price.product
    } else if (
      typeof subscription.items.data[0].price.product === "object" &&
      subscription.items.data[0].price.product !== null
    ) {
      // If it's an object, extract the ID
      productId = (subscription.items.data[0].price.product as any).id
    } else {
      console.error(
        "Invalid product format:",
        subscription.items.data[0].price.product
      )
      throw new Error("Invalid product format")
    }

    console.log("Managing subscription status change:", {
      subscriptionId,
      customerId,
      productId
    })

    // Update the user's membership status based on the subscription
    await manageSubscriptionStatusChange(subscription.id, customerId, productId)

    console.log("Successfully processed checkout session:", checkoutSession.id)
  } else {
    console.log(
      "Ignoring non-subscription checkout session:",
      checkoutSession.id
    )
  }
}
