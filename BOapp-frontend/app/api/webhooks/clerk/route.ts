import { Webhook } from "svix"
import { headers } from "next/headers"
import { WebhookEvent } from "@clerk/nextjs/server"
import { initializeTrialAction } from "@/actions/trial-actions"
import { NextResponse } from "next/server"

export async function POST(req: Request) {
  // Get the webhook signature from the headers
  const headerPayload = await headers()
  const svix_id = headerPayload.get("svix-id")
  const svix_timestamp = headerPayload.get("svix-timestamp")
  const svix_signature = headerPayload.get("svix-signature")

  // If there are no headers, return 400
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Missing svix headers", { status: 400 })
  }

  // Get the body
  const payload = await req.json()
  const body = JSON.stringify(payload)

  // Create a new Svix instance with your webhook secret
  const webhookSecret = process.env.CLERK_WEBHOOK_SECRET

  if (!webhookSecret) {
    return new Response("Missing webhook secret", { status: 500 })
  }

  // Create a new Webhook instance with your secret
  const wh = new Webhook(webhookSecret)

  let evt: WebhookEvent

  // Verify the webhook
  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature
    }) as WebhookEvent
  } catch (err) {
    console.error("Error verifying webhook:", err)
    return new Response("Error verifying webhook", { status: 400 })
  }

  // Handle the webhook
  const eventType = evt.type

  if (eventType === "user.created") {
    const { id } = evt.data

    // Initialize trial for the new user
    const result = await initializeTrialAction(id)

    if (!result.isSuccess) {
      console.error("Failed to initialize trial:", result.message)
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { success: true, message: "Trial initialized successfully" },
      { status: 200 }
    )
  }

  // Return a 200 response for other event types
  return NextResponse.json({ success: true }, { status: 200 })
}
