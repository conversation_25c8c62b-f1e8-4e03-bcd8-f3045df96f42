"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { useAuth } from "@clerk/nextjs"

export default function DebugTrialPage() {
  const { userId } = useAuth()
  const [debugData, setDebugData] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const fetchDebugData = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/debug/trial-days")
      const data = await response.json()
      setDebugData(data)
    } catch (error) {
      console.error("Error fetching debug data:", error)
      setDebugData({ error: "Error fetching debug data" })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Fetch debug data on page load
    fetchDebugData()
  }, [])

  return (
    <div className="container mx-auto max-w-2xl">
      <h1 className="mb-6 text-3xl font-bold">Trial Days Debug</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>User Information</CardTitle>
          <CardDescription>Your current user details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div>
              <strong>User ID:</strong> {userId || "Not authenticated"}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Trial Days Calculation</CardTitle>
          <CardDescription>
            Detailed breakdown of trial days calculation
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div>Loading...</div>
          ) : debugData ? (
            debugData.error ? (
              <div className="text-red-500">{debugData.error}</div>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Profile Data</h3>
                  <div>
                    <strong>User ID:</strong> {debugData.profile.userId}
                  </div>
                  <div>
                    <strong>Membership:</strong> {debugData.profile.membership}
                  </div>
                  <div>
                    <strong>Trial Started At:</strong>{" "}
                    {new Date(
                      debugData.profile.trialStartedAt
                    ).toLocaleString()}
                  </div>
                  <div>
                    <strong>Trial Ends At:</strong>{" "}
                    {new Date(debugData.profile.trialEndsAt).toLocaleString()}
                  </div>
                  <div>
                    <strong>Has Trial Expired:</strong>{" "}
                    {debugData.profile.hasTrialExpired}
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Calculations</h3>
                  <div>
                    <strong>Current Time:</strong>{" "}
                    {new Date(debugData.calculations.now).toLocaleString()}
                  </div>
                  <div>
                    <strong>Days Left (Method 1):</strong>{" "}
                    {debugData.calculations.daysLeftMethod1}
                  </div>
                  <div>
                    <strong>Days Left (Method 2):</strong>{" "}
                    {debugData.calculations.daysLeftMethod2}
                  </div>
                  <div>
                    <strong>Total Trial Days:</strong>{" "}
                    {debugData.calculations.totalTrialDays}
                  </div>
                  <div>
                    <strong>Raw Difference (in days):</strong>{" "}
                    {debugData.calculations.differenceInDays.toFixed(2)}
                  </div>
                </div>
              </div>
            )
          ) : (
            <div>No debug data available</div>
          )}
        </CardContent>
        <CardFooter>
          <Button onClick={fetchDebugData} disabled={loading}>
            Refresh Data
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
