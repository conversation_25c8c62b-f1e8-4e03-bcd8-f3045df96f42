// app/dashboard/optimizations/page.tsx
"use server"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Plus, Beaker } from "lucide-react"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import {
  getOptimizationsAction,
  getMeasurementCountsAction,
  getLatestMeasurementsAction
} from "@/actions/db/optimizations-actions"
import { getOptimizationsActionTest } from "@/actions/db/optimizations-actions"
import { checkAPIHealth } from "@/actions/optimization-actions"
import Link from "next/link"
import { OptimizationCard } from "@/components/dashboard/optimization-card"

export default async function OptimizationsPage() {
  const { userId } = await auth()

  if (!userId) {
    redirect("/login?redirect_url=/dashboard/optimizations")
  }

  // Check if the API is available (for error handling)
  const apiStatus = await checkAPIHealth()

  // Get optimizations for this user
  const optimizationsResult = await getOptimizationsAction(userId)
  // const optimizationsResult = await getOptimizationsActionTest() // This shows ALL users' optimizations

  const optimizations = optimizationsResult.isSuccess
    ? optimizationsResult.data
    : []

  // Get measurement counts for each optimization
  const measurementCountsResult = await getMeasurementCountsAction(userId)
  const measurementCounts = measurementCountsResult.isSuccess
    ? measurementCountsResult.data
    : {}

  // Get latest measurements for each optimization to determine last activity
  const latestMeasurementsResult = await getLatestMeasurementsAction(userId)
  const latestMeasurements = latestMeasurementsResult.isSuccess
    ? latestMeasurementsResult.data
    : {}

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Optimizations</h1>
        <Link href="/dashboard/optimizations/create">
          <Button>
            <Plus className="mr-2 size-4" />
            New Optimization
          </Button>
        </Link>
      </div>

      {/* Optimizations Grid */}
      <div className="grid auto-rows-fr grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {optimizations.map((optimization, index) => {
          // Get measurement count for this optimization
          const measurementCount = measurementCounts[optimization.id] || 0

          // Get latest measurement for this optimization
          const latestMeasurement = latestMeasurements[optimization.id] || null
          const lastActivity = latestMeasurement
            ? new Date(latestMeasurement.createdAt)
            : null

          // Get best value (placeholder for now - would need to be fetched from API)
          const bestValue = measurementCount > 0 ? 75 : null // Placeholder progress value

          return (
            <OptimizationCard
              key={optimization.id}
              optimization={optimization}
              index={index}
              measurements={measurementCount}
              bestValue={bestValue}
              lastActivity={lastActivity}
            />
          )
        })}
      </div>

      {/* Empty State */}
      {optimizations.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Beaker className="text-muted-foreground mb-4 size-16" />
            <h3 className="mb-2 text-xl font-medium">No optimizations yet</h3>
            <p className="text-muted-foreground mb-6 max-w-md text-center">
              Create your first optimization to start leveraging Bayesian
              techniques for experiment design and parameter tuning.
            </p>
            <Link href="/dashboard/optimizations/create">
              <Button>
                <Plus className="mr-2 size-4" />
                Create Your First Optimization
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
