/*
This server page provides the main dashboard interface for authenticated users.
*/

"use server"

import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { DashboardHomeClient } from "@/components/dashboard/dashboard-home-client"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import {
  getTotalMeasurementsCountAction,
  getOptimizationsAction
} from "@/actions/db/optimizations-actions"
import { statusLabels, statusColors } from "@/lib/status-utils"
import { formatDistanceToNow } from "date-fns"
import Link from "next/link"

export default async function DashboardHomePage() {
  const { userId } = await auth()

  // If user is not authenticated, redirect to the dashboard root for auth check
  if (!userId) {
    redirect("/dashboard")
  }

  // Get total number of experiments (measurements) for this user
  const totalExperimentsResult = await getTotalMeasurementsCountAction(userId)
  const totalExperiments = totalExperimentsResult.isSuccess
    ? totalExperimentsResult.data
    : 0

  // Get optimizations for this user
  const optimizationsResult = await getOptimizationsAction(userId)
  const optimizations = optimizationsResult.isSuccess
    ? optimizationsResult.data
    : []
  const activeOptimizations = optimizations.length

  // Get recent optimizations (limit to 3)
  const recentOptimizations = optimizations.slice(0, 3)

  return (
    <div className="container mx-auto p-6">
      <h1 className="mb-6 text-3xl font-bold">Dashboard</h1>

      <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Experiments
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalExperiments}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Active Optimizations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeOptimizations}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Efficiency Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">92%</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Optimizations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOptimizations.length > 0 ? (
                recentOptimizations.map((optimization, i) => {
                  const status =
                    optimization.status as keyof typeof statusLabels
                  const createdAt = new Date(optimization.createdAt)
                  const timeAgo = formatDistanceToNow(createdAt, {
                    addSuffix: true
                  })

                  return (
                    <div
                      key={optimization.id}
                      className="flex items-center justify-between border-b pb-2"
                    >
                      <div>
                        <Link
                          href={`/dashboard/optimizations/${optimization.id}/run`}
                          className="hover:text-primary font-medium hover:underline"
                        >
                          {optimization.name}
                        </Link>
                        <p className="text-muted-foreground text-sm">
                          Created {timeAgo}
                        </p>
                      </div>
                      <span
                        className={`rounded px-2 py-1 text-sm text-white ${
                          statusColors[status]
                        }`}
                      >
                        {statusLabels[status]}
                      </span>
                    </div>
                  )
                })
              ) : (
                <div className="text-muted-foreground py-4 text-center">
                  No optimizations yet. Create your first one!
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex h-[300px] items-center justify-center rounded-md border">
              <p className="text-muted-foreground">
                Chart visualization would appear here
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Client component with Optimizations button and loading overlay */}
      <div className="mt-6">
        <DashboardHomeClient />
      </div>
    </div>
  )
}
