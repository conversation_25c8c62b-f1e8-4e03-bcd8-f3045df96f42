"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { useAuth } from "@clerk/nextjs"

export default function TestTrialPage() {
  const { userId } = useAuth()
  const [trialStatus, setTrialStatus] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const checkTrial = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/user/check-trial")
      const data = await response.json()
      setTrialStatus(data)
    } catch (error) {
      console.error("Error checking trial:", error)
      setTrialStatus({ success: false, message: "Error checking trial" })
    } finally {
      setLoading(false)
    }
  }

  const initializeTrial = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/user/initialize-trial", {
        method: "POST"
      })
      const data = await response.json()
      setTrialStatus(data)
    } catch (error) {
      console.error("Error initializing trial:", error)
      setTrialStatus({ success: false, message: "Error initializing trial" })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Check trial status on page load
    checkTrial()
  }, [])

  return (
    <div className="container mx-auto max-w-2xl">
      <h1 className="mb-6 text-3xl font-bold">Trial Status Test</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>User Information</CardTitle>
          <CardDescription>Your current user details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div>
              <strong>User ID:</strong> {userId || "Not authenticated"}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Trial Status</CardTitle>
          <CardDescription>Your current trial status</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div>Loading...</div>
          ) : trialStatus ? (
            <div className="space-y-2">
              <div>
                <strong>Success:</strong> {trialStatus.success ? "Yes" : "No"}
              </div>
              <div>
                <strong>Message:</strong> {trialStatus.message}
              </div>
              {trialStatus.hasExistingTrial !== undefined && (
                <div>
                  <strong>Has Existing Trial:</strong>{" "}
                  {trialStatus.hasExistingTrial ? "Yes" : "No"}
                </div>
              )}
              {trialStatus.trialEndsAt && (
                <div>
                  <strong>Trial Ends At:</strong>{" "}
                  {new Date(trialStatus.trialEndsAt).toLocaleString()}
                </div>
              )}
            </div>
          ) : (
            <div>No trial status available</div>
          )}
        </CardContent>
        <CardFooter className="flex gap-2">
          <Button onClick={checkTrial} disabled={loading}>
            Check Trial Status
          </Button>
          <Button
            onClick={initializeTrial}
            disabled={loading}
            variant="outline"
          >
            Initialize Trial
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
