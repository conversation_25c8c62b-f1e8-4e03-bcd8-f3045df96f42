import { Metada<PERSON> } from "next"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import {
  Workflow,
  GitBranch,
  BarChart3,
  LineChart,
  Layers,
  Target
} from "lucide-react"

export const metadata: Metadata = {
  title: "Optimization Analysis | Help Centre",
  description: "Understanding the analysis tools in the optimization system"
}

export default function OptimizationAnalysisPage() {
  return (
    <div className="container max-w-5xl py-10">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Optimization Analysis
        </h1>
        <p className="text-muted-foreground">
          Understanding the analysis tools and visualizations in the
          optimization system
        </p>
      </div>

      <div className="mt-8 space-y-8">
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Workflow className="text-primary size-5" />
                Strategy Evolution
              </CardTitle>
              <CardDescription>
                Understanding how optimization strategy changes over time
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                The Strategy Evolution visualization shows how the optimization
                balances between exploration (trying new areas) and exploitation
                (focusing on promising areas) over time. This balance is crucial
                for efficient optimization.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Key Components:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <span className="font-medium text-blue-500">Blue area</span>
                    : Represents exploration - searching new areas of the
                    parameter space
                  </li>
                  <li>
                    <span className="font-medium text-orange-500">
                      Orange area
                    </span>
                    : Represents exploitation - focusing on promising regions
                  </li>
                  <li>
                    <span className="font-medium text-green-500">
                      Green line
                    </span>
                    : Shows the balance between exploration and exploitation
                  </li>
                  <li>
                    <span className="font-medium text-red-500">
                      Dashed lines
                    </span>
                    : Indicate significant strategy shifts
                  </li>
                </ul>
              </div>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Interpreting the Visualization:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    A positive balance (above center) indicates more exploration
                  </li>
                  <li>
                    A negative balance (below center) indicates more
                    exploitation
                  </li>
                  <li>Early experiments typically focus on exploration</li>
                  <li>Later experiments typically shift toward exploitation</li>
                  <li>
                    Strategy shifts occur when the algorithm significantly
                    changes its approach
                  </li>
                </ul>
              </div>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Controls:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>Exploration Weight (β)</strong>: Adjusts the
                    relative importance of exploration. Higher values increase
                    the importance of exploration.
                  </li>
                  <li>
                    <strong>Window Size</strong>: Controls the smoothing window
                    for trend calculations. Larger windows provide smoother
                    trends but may miss short-term changes.
                  </li>
                  <li>
                    <strong>Show Strategy Shifts</strong>: Toggles visibility of
                    strategy shift markers, which indicate significant changes
                    in the optimization approach.
                  </li>
                </ul>
              </div>

              <p>
                The Strategy Evolution visualization helps you understand how
                the optimization algorithm is making decisions and can provide
                insights into when and why the algorithm changes its approach.
                This can be particularly useful for diagnosing optimization
                problems or understanding why certain parameter regions are
                being explored.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="text-primary size-5" />
                Trade-off Explorer
              </CardTitle>
              <CardDescription>
                Visualizing trade-offs between multiple optimization targets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                The Trade-off Explorer helps you understand the relationships
                and trade-offs between different optimization targets. It's
                particularly useful for multi-target optimizations where
                improving one target might come at the expense of another.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Key Features:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    Scatter plot showing the relationship between two targets
                  </li>
                  <li>
                    Pareto front highlighting to identify optimal trade-offs
                  </li>
                  <li>
                    Experiment selection to view specific parameter values
                  </li>
                  <li>
                    Target selection to explore different trade-off
                    relationships
                  </li>
                </ul>
              </div>

              <p className="mt-4">
                By exploring these trade-offs, you can make informed decisions
                about which parameter combinations best meet your specific
                requirements, especially when you need to balance multiple
                competing objectives.
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="text-primary size-5" />
                Parameter Analysis
              </CardTitle>
              <CardDescription>
                Understanding parameter relationships and importance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Parameter Analysis helps you understand how different parameters
                affect your optimization targets and how they interact with each
                other. This can provide valuable insights for experimental
                design and process understanding.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Available Visualizations:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>Correlation Explorer</strong>: Shows relationships
                    between parameters and targets
                  </li>
                  <li>
                    <strong>Parameter Distributions</strong>: Visualizes the
                    distribution of parameter values
                  </li>
                  <li>
                    <strong>Parameter Importance</strong>: Identifies which
                    parameters have the most influence
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="text-primary size-5" />
                Experiment Timeline
              </CardTitle>
              <CardDescription>
                Tracking optimization progress over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                The Experiment Timeline section provides visualizations that
                track how your optimization progresses over time, helping you
                understand the learning rate and strategy evolution.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Timeline Components:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>Progress Visualization</strong>: Shows how target
                    values improve over time
                  </li>
                  <li>
                    <strong>Strategy Evolution</strong>: Visualizes the balance
                    between exploration and exploitation
                  </li>
                  <li>
                    <strong>Improvement Rate</strong>: Tracks how quickly the
                    optimization is learning
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layers className="text-primary size-5" />
                Response Surface Analysis
              </CardTitle>
              <CardDescription>
                Visualizing the predicted response surface
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Response Surface Analysis provides visualizations of how the
                optimization targets are predicted to change as parameters vary.
                This helps you understand the shape of the optimization
                landscape and identify promising regions.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Surface Visualizations:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>3D Surface Plots</strong>: Show how two parameters
                    affect a target
                  </li>
                  <li>
                    <strong>Contour Plots</strong>: Provide 2D views of the
                    response surface
                  </li>
                  <li>
                    <strong>Cross-sections</strong>: Show how targets change
                    along specific parameter paths
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="text-primary size-5" />
                Multi-Target Analysis
              </CardTitle>
              <CardDescription>
                Analyzing relationships between multiple targets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Multi-Target Analysis helps you understand the relationships
                between different optimization targets and how they interact.
                This is crucial for multi-objective optimization where you need
                to balance multiple competing goals.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Analysis Tools:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>Trade-off Explorer</strong>: Visualizes Pareto
                    fronts and trade-offs
                  </li>
                  <li>
                    <strong>Parallel Coordinates</strong>: Shows
                    multi-dimensional relationships
                  </li>
                  <li>
                    <strong>Target Correlations</strong>: Identifies how targets
                    relate to each other
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
