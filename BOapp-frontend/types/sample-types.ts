// types/sample-types.ts

import { Parameter } from "./optimization-types"

/**
 * Sample status types
 */
export type SampleStatus = "pending" | "in_progress" | "submitted" | "discarded"

/**
 * Sample class types
 */
export type SampleClass = "exploratory" | "batch" | "sequential" | "recommended"

/**
 * Sampling method types
 */
export type SamplingMethod = "LHS" | "random" | "sobol" | "grid"

/**
 * Base sample parameters
 */
export type SampleParameters = Record<string, any>

/**
 * Target values for a sample
 */
export type TargetValues = Record<string, number>

/**
 * Client-side sample with metadata
 */
export interface ClientSample extends SampleParameters {
  _sampleId: string
  _targetValues: TargetValues
  _batchId: string
  _sampleIndex: number
  _samplingMethod: SamplingMethod
  _seed: number | null
  _sampleClass: SampleClass
  _status?: SampleStatus
  _optimizationId?: string
  _savedToDatabase?: boolean // Flag to track if sample has been saved to database
}

/**
 * Database sample from the API
 */
export interface DBSample {
  id: string
  optimizationId: string
  parameters: SampleParameters
  batchId: string
  sampleIndex: number
  samplingMethod: SamplingMethod
  seed: number | null
  sampleClass: SampleClass
  status: SampleStatus
  targetValues: TargetValues | null
  createdAt?: string
  updatedAt?: string
  lastAccessedAt?: string | null
  submittedAt?: string | null
}

/**
 * Sample generation response from the API
 */
export interface SampleGenerationResponse {
  samples: SampleParameters[]
  message?: string
}

/**
 * Sample generation result with batch ID
 */
export interface SampleGenerationResult {
  samples: ClientSample[]
  batchId: string | null
}

/**
 * Saved samples result
 */
export interface SavedSamplesResult {
  savedSamples: ClientSample[]
}
