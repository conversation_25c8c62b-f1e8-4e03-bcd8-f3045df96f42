"use client"

import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from "react"

type LoadingContextType = {
  isLoading: boolean
  startLoading: () => void
  stopLoading: () => void
  pendingRequests: number
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export function useLoading() {
  const context = useContext(LoadingContext)
  if (context === undefined) {
    throw new Error("useLoading must be used within a LoadingProvider")
  }
  return context
}

interface LoadingProviderProps {
  children: ReactNode
}

export function LoadingProvider({ children }: LoadingProviderProps) {
  const [pendingRequests, setPendingRequests] = useState(0)

  const startLoading = useCallback(() => {
    setPendingRequests((prev) => prev + 1)
  }, [])

  const stopLoading = useCallback(() => {
    setPendingRequests((prev) => Math.max(0, prev - 1))
  }, [])

  // Reset loading state when component unmounts or on page navigation
  useEffect(() => {
    // This will run when the component unmounts
    return () => {
      setPendingRequests(0)
    }
  }, [])

  const isLoading = pendingRequests > 0

  const value = {
    isLoading,
    startLoading,
    stopLoading,
    pendingRequests
  }

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  )
}
