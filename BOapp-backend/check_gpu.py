import torch
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

def check_gpu():
    logger.info("Checking GPU availability...")
    
    # Check if CUDA is available
    if torch.cuda.is_available():
        logger.info("CUDA is available!")
        
        # Get device count
        device_count = torch.cuda.device_count()
        logger.info(f"Found {device_count} CUDA device(s)")
        
        # Get device information for each GPU
        for i in range(device_count):
            device_name = torch.cuda.get_device_name(i)
            logger.info(f"Device {i}: {device_name}")
            
            # Check memory
            logger.info(f"Memory allocated: {torch.cuda.memory_allocated(i) / 1e6:.2f} MB")
            logger.info(f"Memory cached: {torch.cuda.memory_cached(i) / 1e6:.2f} MB")
            
        # Try to use CUDA for a simple operation
        logger.info("Testing CUDA with a simple tensor operation...")
        try:
            # Create a tensor on CPU
            x = torch.rand(1000, 1000)
            logger.info(f"Created tensor on CPU with shape {x.shape}")
            
            # Move to GPU
            device = torch.device("cuda")
            x = x.to(device)
            logger.info(f"Moved tensor to {device}")
            
            # Perform a simple operation
            y = torch.matmul(x, x)
            logger.info(f"Performed matrix multiplication, result shape: {y.shape}")
            
            # Test with autocast
            logger.info("Testing with autocast...")
            with torch.cuda.amp.autocast():
                z = torch.matmul(x, x)
            logger.info(f"Performed matrix multiplication with autocast, result shape: {z.shape}")
            
            logger.info("CUDA operations completed successfully!")
            return True
        except Exception as e:
            logger.error(f"Error performing CUDA operations: {str(e)}")
            return False
    else:
        logger.warning("CUDA is not available. PyTorch will use CPU.")
        return False

if __name__ == "__main__":
    check_gpu()
