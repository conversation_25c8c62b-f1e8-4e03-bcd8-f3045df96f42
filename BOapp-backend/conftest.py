import os
import sys
import pytest
from fastapi.testclient import TestClient
from unittest.mock import MagicMock, patch

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import the FastAPI app and backend
from baybe_api.main import app, get_backend

# Create a mock for the BayesianOptimizationBackend
@pytest.fixture
def mock_backend():
    """Create a mock for the Bayesian Optimization Backend."""
    mock = MagicMock()
    # Set up default returns for common methods
    mock.create_optimization.return_value = {
        "status": "success",
        "message": "Optimization created",
        "optimizer_id": "test_optimizer",
        "parameter_count": 2,
        "constraint_count": 0
    }
    mock.suggest_next_point.return_value = {
        "status": "success",
        "suggestions": [{"x": 0.5, "y": 0.5}],
        "batch_size": 1
    }
    mock.add_measurement.return_value = {
        "status": "success",
        "message": "Measurement added"
    }
    mock.add_multiple_measurements.return_value = {
        "status": "success",
        "message": "2 measurements added"
    }
    mock.get_best_point.return_value = {
        "status": "success",
        "best_value": 0.95,
        "best_parameters": {"x": 0.7, "y": 0.3},
        "total_measurements": 10,
        "target_name": "Target",
        "target_mode": "MAX"
    }
    mock.get_measurement_history.return_value = {
        "status": "success",
        "measurements": [
            {"x": 0.1, "y": 0.2, "Target": 0.5},
            {"x": 0.3, "y": 0.4, "Target": 0.7}
        ],
        "target_info": [{"name": "Target", "mode": "MAX"}],
        "count": 2
    }
    mock.get_campaign_info.return_value = {
        "status": "success",
        "info": {
            "parameters": [
                {"name": "x", "type": "NumericalContinuousParameter", "bounds": [0, 1]},
                {"name": "y", "type": "NumericalContinuousParameter", "bounds": [0, 1]}
            ],
            "targets": [{"name": "Target", "mode": "MAX"}],
            "measurements_count": 10,
            "objective_type": "SingleTarget"
        }
    }
    mock.initialize_from_data.return_value = {
        "status": "success",
        "message": "2 measurements added"
    }
    mock.list_optimizations.return_value = {
        "status": "success",
        "optimizers": [
            {"id": "test_optimizer_1", "file_path": "/app/data/test_optimizer_1.json"},
            {"id": "test_optimizer_2", "file_path": "/app/data/test_optimizer_2.json"}
        ],
        "count": 2
    }
    mock.delete_optimization.return_value = {
        "status": "success",
        "message": "Optimizer test_optimizer deleted successfully"
    }
    mock.unload_campaign.return_value = {
        "status": "success",
        "message": "Optimizer test_optimizer unloaded successfully"
    }
    return mock

@pytest.fixture
def client(mock_backend):
    """Create a test client with a mocked backend."""
    # Override the dependency to return the mock backend
    app.dependency_overrides[get_backend] = lambda: mock_backend
    with TestClient(app) as client:
        yield client
    # Clear the dependency override after the test
    app.dependency_overrides.clear()

@pytest.fixture
def sample_optimization_request():
    """Return a sample optimization creation request."""
    return {
        "optimizer_id": "test_optimizer",
        "parameters": [
            {"name": "x", "type": "NumericalContinuous", "bounds": [0, 1]},
            {"name": "y", "type": "NumericalContinuous", "bounds": [0, 1]}
        ],
        "target_config": {"name": "Target", "mode": "MAX"},
        "objective_type": "SingleTarget",
        "num_initial_samples": 5
    }

@pytest.fixture
def sample_multi_objective_request():
    """Return a sample multi-objective optimization creation request."""
    return {
        "optimizer_id": "multi_objective_test",
        "parameters": [
            {"name": "x", "type": "NumericalContinuous", "bounds": [0, 1]},
            {"name": "y", "type": "NumericalContinuous", "bounds": [0, 1]}
        ],
        "target_config": [
            {"name": "efficiency", "mode": "MAX"},
            {"name": "cost", "mode": "MIN", "weight": 0.7}
        ],
        "objective_type": "Desirability",
        "scalarizer": "GEOM_MEAN",
        "weights": [1.0, 0.7],
        "num_initial_samples": 5
    }

@pytest.fixture
def sample_measurement_request():
    """Return a sample measurement request."""
    return {
        "parameters": {"x": 0.5, "y": 0.5},
        "target_value": 0.75
    }

@pytest.fixture
def sample_multiple_measurements_request():
    """Return a sample multiple measurements request."""
    return {
        "measurements": [
            {"x": 0.1, "y": 0.2, "Target": 0.5},
            {"x": 0.3, "y": 0.4, "Target": 0.7}
        ]
    }

@pytest.fixture
def sample_initialize_data_request():
    """Return a sample initialize from data request."""
    return {
        "parameters": [
            {"x": 0.1, "y": 0.2},
            {"x": 0.3, "y": 0.4}
        ],
        "targets": [0.5, 0.7]
    }