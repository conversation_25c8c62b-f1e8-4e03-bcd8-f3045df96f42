"""Router for strategy evolution metrics endpoints."""

from fastapi import APIRouter, HTTPException, Depends, Body, Query
from pydantic import BaseModel
from typing import Dict, Any, List, Optional

from baybe_api.strategy_metrics import calculate_strategy_metrics
from baybe_api.utils import sanitize_for_json
from baybe_api.responses import SafeJSONResponse
from baybe_api.backend_utils import get_optimizer

# Create a router for strategy endpoints
strategy_router = APIRouter(tags=["strategy"])

# Pydantic models
class StrategyMetricsConfig(BaseModel):
    """Configuration for strategy metrics calculation."""
    window_size: int = 5
    beta: float = 0.2

class StrategyMetricsResponse(BaseModel):
    """Response model for strategy metrics."""
    status: str
    metrics: Optional[Dict[str, Any]] = None
    message: Optional[str] = None

# Routes
@strategy_router.post(
    "/optimizations/{optimizer_id}/strategy-metrics",
    response_class=SafeJSONResponse,
    response_model=StrategyMetricsResponse
)
async def get_strategy_metrics(
    optimizer_id: str,
    config: StrategyMetricsConfig = Body(StrategyMetricsConfig())
):
    """Get exploration-exploitation metrics for an optimization campaign.

    Args:
        optimizer_id: ID of the optimization campaign
        config: Configuration for strategy metrics calculation

    Returns:
        Dictionary with strategy metrics
    """
    try:
        # Get the campaign
        campaign = get_optimizer(optimizer_id)

        if not campaign:
            raise HTTPException(status_code=404, detail=f"Optimization {optimizer_id} not found")

        # Calculate strategy metrics
        metrics = calculate_strategy_metrics(
            campaign=campaign,
            window_size=config.window_size,
            beta=config.beta
        )

        # Sanitize the response for JSON serialization
        return sanitize_for_json({
            "status": "success",
            "metrics": metrics
        })

    except HTTPException as e:
        raise e
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to calculate strategy metrics: {str(e)}"
        }

@strategy_router.get(
    "/optimizations/{optimizer_id}/strategy-metrics",
    response_class=SafeJSONResponse,
    response_model=StrategyMetricsResponse
)
async def get_strategy_metrics_get(
    optimizer_id: str,
    window_size: int = Query(5, ge=1, le=100),
    beta: float = Query(0.2, ge=0.0, le=10.0)
):
    """Get exploration-exploitation metrics for an optimization campaign (GET method).

    Args:
        optimizer_id: ID of the optimization campaign
        window_size: Number of recent experiments to consider for trend metrics
        beta: The beta parameter for UCB calculations

    Returns:
        Dictionary with strategy metrics
    """
    config = StrategyMetricsConfig(window_size=window_size, beta=beta)
    return await get_strategy_metrics(optimizer_id, config)
