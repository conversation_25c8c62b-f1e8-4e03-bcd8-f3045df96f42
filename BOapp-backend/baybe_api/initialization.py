# initialization.py

import numpy as np
import pandas as pd
import logging
from typing import List, Dict, Any, Optional, Union
from fastapi import APIRouter, HTTPException, Body, Depends, Query, File, UploadFile
from pydantic import BaseModel, Field
from pyDOE2 import lhs
import io
from io import BytesIO
from baybe_api.dependencies import get_backend
from baybe_api.optimizer.backend import BayesianOptimizationBackend

# We'll get the backend instance when needed in the endpoint functions

# Import get_optimizer from utils to avoid circular imports
from baybe_api.utils import get_optimizer

# Set up logging
logger = logging.getLogger(__name__)

# Create a router for initialization endpoints
initialization_router = APIRouter(tags=["initialization"])

# Try to import pyDOE2 for Latin Hypercube Sampling
try:
    from pyDOE2 import lhs
    LHS_AVAILABLE = True
except ImportError:
    logger.warning("pyDOE2 not installed. Using numpy random sampling instead of LHS.")
    LHS_AVAILABLE = False

# Pydantic models for request/response schemas
class InitialSamplesRequest(BaseModel):
    n_samples: int = Field(10, gt=0, description="Number of initial samples to generate")
    seed: Optional[int] = Field(None, description="Random seed for reproducibility")

class ExistingDataRequest(BaseModel):
    data: Dict[str, List[Any]] = Field(..., description="Existing experimental data as a dictionary of parameter_name: [values]")

class CSVUploadResponse(BaseModel):
    status: str
    message: str
    parameters: List[str]
    rows_parsed: int

# Utility functions
def generate_samples(
    parameters: List[Dict[str, Any]],
    n_samples: int = 10,
    seed: Optional[int] = None,
    use_lhs: bool = True
) -> pd.DataFrame:
    """
    Generate initial samples using Latin Hypercube Sampling (LHS) or random sampling.

    Args:
        parameters: List of parameter configurations.
        n_samples: Number of samples to generate.
        seed: Random seed for reproducibility.
        use_lhs: Whether to use LHS (if available) or random sampling.

    Returns:
        Dataframe with parameter values for initial samples.
    """
    # Set random seed if provided
    if seed is not None:
        np.random.seed(seed)

    # Extract numerical parameters and their bounds/values
    numerical_params = []
    numerical_bounds = []

    for param in parameters:
        if param["type"] in ["NumericalContinuous", "NumericalContinuousParameter",
                            "NumericalDiscrete", "NumericalDiscreteParameter"]:
            numerical_params.append(param["name"])

            if "bounds" in param:
                # For continuous parameters with bounds
                numerical_bounds.append((param["bounds"][0], param["bounds"][1]))
            elif "values" in param:
                # For discrete parameters with values
                numerical_bounds.append((min(param["values"]), max(param["values"])))

    # Generate samples for numerical parameters
    n_numerical = len(numerical_params)
    df_samples = pd.DataFrame(index=range(n_samples))

    if n_numerical > 0:
        if use_lhs and LHS_AVAILABLE:
            # Generate normalized LHS samples in [0,1]
            lhs_samples = lhs(n_numerical, samples=n_samples)

            # Scale to actual parameter ranges
            for i, param_name in enumerate(numerical_params):
                lower, upper = numerical_bounds[i]
                df_samples[param_name] = lhs_samples[:,i] * (upper - lower) + lower
        else:
            # Use random sampling
            for i, param_name in enumerate(numerical_params):
                lower, upper = numerical_bounds[i]
                df_samples[param_name] = np.random.uniform(lower, upper, n_samples)

        # For discrete parameters, map to nearest discrete value
        for param in parameters:
            if param["type"] in ["NumericalDiscrete", "NumericalDiscreteParameter"] and "values" in param:
                name = param["name"]
                if name in df_samples.columns:
                    # Find closest discrete value for each sample
                    values = np.array(param["values"])
                    df_samples[name] = df_samples[name].apply(
                        lambda x: values[np.abs(values - x).argmin()]
                    )

    # Handle categorical parameters by random sampling
    for param in parameters:
        if param["type"] in ["Categorical", "CategoricalParameter", "Substance", "SubstanceParameter"]:
            if "values" in param:
                df_samples[param["name"]] = np.random.choice(
                    param["values"],
                    size=n_samples
                )

    return df_samples

def parse_existing_data(
    data: Dict[str, List[Any]],
    parameters: List[Dict[str, Any]],
    target_name: str
) -> pd.DataFrame:
    """
    Parse existing experimental data provided by the user.

    Args:
        data: Dictionary with parameter names as keys and lists of values.
        parameters: List of parameter configurations.
        target_name: Name of the target variable.

    Returns:
        Dataframe with parameter values and target values.
    """
    # Create DataFrame from provided data
    df = pd.DataFrame(data)

    # Validate that all required parameters are present
    param_names = [p["name"] for p in parameters]
    missing_params = [p for p in param_names if p not in df.columns]

    if missing_params:
        raise ValueError(f"Missing parameters in provided data: {missing_params}")

    # Validate that target column is present
    if target_name not in df.columns:
        raise ValueError(f"Target column '{target_name}' not found in provided data")

    # Validate data types and convert if necessary
    for param in parameters:
        name = param["name"]

        if param["type"] in ["NumericalContinuous", "NumericalContinuousParameter",
                            "NumericalDiscrete", "NumericalDiscreteParameter"]:
            # Convert to float for numerical parameters
            df[name] = df[name].astype(float)
        elif param["type"] in ["Categorical", "CategoricalParameter", "Substance", "SubstanceParameter"]:
            # Convert to string for categorical parameters
            df[name] = df[name].astype(str)

    # Convert target to float
    df[target_name] = df[target_name].astype(float)

    return df

# Routes
@initialization_router.post("/{optimizer_id}/initialize/predefined")
async def initialize_predefined(
    optimizer_id: str,
    request: InitialSamplesRequest = Body(...)
):
    """Initialize optimization with predefined samples."""
    try:
        # Use get_optimizer from utils
        campaign = get_optimizer(optimizer_id)

        parameters = []
        for param in campaign.searchspace.parameters:
            param_dict = {
                "name": param.name,
                "type": param.__class__.__name__
            }

            if hasattr(param, "values") and param.values is not None:
                param_dict["values"] = param.values.tolist() if hasattr(param.values, 'tolist') else param.values
            elif hasattr(param, "bounds") and param.bounds is not None:
                param_dict["bounds"] = [param.bounds.lower, param.bounds.upper]

            parameters.append(param_dict)

        samples_df = generate_samples(
            parameters=parameters,
            n_samples=request.n_samples,
            seed=request.seed,
            use_lhs=LHS_AVAILABLE
        )

        return {
            "status": "success",
            "samples": samples_df.to_dict(orient="records")
        }
    except Exception as e:
        logger.error(f"Error initializing with predefined samples: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@initialization_router.post("/{optimizer_id}/initialize/zero")
async def initialize_zero(
    optimizer_id: str,
    request: InitialSamplesRequest = Body(...)
):
    """Initialize optimization from zero samples."""
    return await initialize_predefined(optimizer_id, request)

@initialization_router.post("/{optimizer_id}/initialize/existing")
async def initialize_existing(
    optimizer_id: str,
    request: ExistingDataRequest = Body(...)
):
    """Initialize optimization with existing data."""
    try:
        # Use get_optimizer from utils
        campaign = get_optimizer(optimizer_id)

        parameters = []
        for param in campaign.searchspace.parameters:
            param_dict = {
                "name": param.name,
                "type": param.__class__.__name__
            }

            if hasattr(param, "values") and param.values is not None:
                param_dict["values"] = param.values.tolist() if hasattr(param.values, 'tolist') else param.values
            elif hasattr(param, "bounds") and param.bounds is not None:
                param_dict["bounds"] = [param.bounds.lower, param.bounds.upper]

            parameters.append(param_dict)

        # Get target name from objective
        if not hasattr(campaign, 'objective') or campaign.objective is None:
            raise ValueError("No objective defined for this campaign")

        targets = campaign.objective.targets
        if not targets:
            raise ValueError("No targets defined in objective")

        target_name = targets[0].name

        measurements_df = parse_existing_data(
            data=request.data,
            parameters=parameters,
            target_name=target_name
        )

        # Get the backend instance
        backend = get_backend()

        # Add measurements to the campaign
        result = backend.add_multiple_measurements(
            optimizer_id=optimizer_id,
            measurements=measurements_df
        )

        # Ensure we return the measurements count for the test
        return {
            "status": "success",
            "measurements": measurements_df.to_dict(orient="records"),
            "measurements_count": len(measurements_df)
        }
    except Exception as e:
        logger.error(f"Error initializing with existing data: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@initialization_router.get("/{optimizer_id}/parameter-info")
async def get_parameter_info(optimizer_id: str):
    """Get parameter information for an optimization campaign."""
    try:
        # Use get_optimizer from utils
        campaign = get_optimizer(optimizer_id)

        # Get parameters from the campaign
        parameters = []
        for param in campaign.searchspace.parameters:
            param_dict = {
                "name": param.name,
                "type": param.__class__.__name__
            }

            if hasattr(param, "values") and param.values is not None:
                param_dict["values"] = param.values.tolist() if hasattr(param.values, 'tolist') else param.values
            elif hasattr(param, "bounds") and param.bounds is not None:
                param_dict["bounds"] = [param.bounds.lower, param.bounds.upper]

            parameters.append(param_dict)

        # Get target information from objective
        if not hasattr(campaign, 'objective') or campaign.objective is None:
            raise ValueError("No objective defined for this campaign")

        targets = campaign.objective.targets
        if not targets:
            raise ValueError("No targets defined in objective")

        target = targets[0]

        return {
            "status": "success",
            "parameters": parameters,
            "target": {
                "name": target.name,
                "mode": target.mode
            }
        }
    except Exception as e:
        logger.error(f"Error getting parameter info: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get parameter info: {str(e)}")

@initialization_router.post("/{optimizer_id}/upload-csv")
async def upload_csv(
    optimizer_id: str,
    file: UploadFile,
    backend = Depends(get_backend)
):
    """Upload CSV data to initialize an optimization."""
    try:
        # Use get_optimizer from utils
        campaign = get_optimizer(optimizer_id)

        # Get parameter names from searchspace
        parameter_names = [param.name for param in campaign.searchspace.parameters]

        # Get target name from objective
        if not hasattr(campaign, 'objective') or campaign.objective is None:
            raise ValueError("No objective defined for this campaign")

        targets = campaign.objective.targets
        if not targets:
            raise ValueError("No targets defined in objective")

        target_name = targets[0].name

        content = await file.read()
        csv_data = pd.read_csv(BytesIO(content))

        required_columns = parameter_names + [target_name]
        missing_columns = set(required_columns) - set(csv_data.columns)
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        parameters_data = csv_data[parameter_names]
        targets_data = csv_data[target_name]

        backend.initialize_from_data(
            optimizer_id,
            parameters=parameters_data.to_dict('records'),
            targets=targets_data.tolist()
        )

        return {
            "status": "success",
            "message": "CSV data uploaded successfully",
            "parameters": parameter_names,
            "rows_parsed": len(csv_data)
        }
    except Exception as e:
        logger.error(f"Error uploading CSV: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
