"""Response classes for the BayBE API."""

import json
import math
from typing import Any

import numpy as np
from fastapi.responses import JSONResponse

from baybe_api.utils import sanitize_for_json

class SafeJSONResponse(JSONResponse):
    """A JSONResponse that handles NaN, Infinity, and other special values."""

    def render(self, content: Any) -> bytes:
        """Render the content as JSON bytes."""
        # First sanitize the content
        sanitized_content = sanitize_for_json(content)

        # Then use the standard JSON encoder
        return json.dumps(
            sanitized_content,
            ensure_ascii=False,
            allow_nan=True,  # We've already sanitized the content
        ).encode("utf-8")
