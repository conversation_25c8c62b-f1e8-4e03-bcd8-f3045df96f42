"""
SOBOL Analysis Module for Contour Plot Generation

This module provides functionality for generating contour plot data using
Sobol' sensitivity analysis from SALib.
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from SALib.sample import saltelli
from SALib.analyze import sobol
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern, RBF, ConstantKernel

from baybe_api.backend_utils import get_backend
from baybe_api.optimizer.backend import BayesianOptimizationBackend
from baybe_api.utils import SafeJSONResponse

router = APIRouter(
    prefix="/sobol-analysis",
    tags=["sobol-analysis"],
    responses={404: {"description": "Not found"}},
)

class SobolContourRequest(BaseModel):
    """Request model for SOBOL contour plot data."""
    param1_name: str
    param2_name: str
    target_name: Optional[str] = None  # Optional target name for multi-target optimizations
    grid_size: int = 50
    num_samples: int = 1024
    experiment_start_idx: Optional[int] = None  # Optional start index for experiment range
    experiment_end_idx: Optional[int] = None  # Optional end index for experiment range

class SobolContourResponse(BaseModel):
    """Response model for SOBOL contour plot data."""
    x_values: List[float]
    y_values: List[float]
    z_values: List[List[float]]
    uncertainty: List[List[float]]
    sobol_first_order: Dict[str, float]
    sobol_second_order: float
    sobol_total: Dict[str, float]
    param1_type: str
    param2_type: str
    param1_is_categorical: bool
    param2_is_categorical: bool
    param1_categories: Optional[List[str]] = None
    param2_categories: Optional[List[str]] = None
    target_name: str  # The name of the target used for analysis
    fixed_params: Optional[List[str]] = None  # List of parameters that have converged
    fixed_params_message: Optional[str] = None  # Message about fixed parameters

def _extract_parameter_data(
    backend: BayesianOptimizationBackend,
    optimizer_id: str,
    param1_name: str,
    param2_name: str,
    target_name: Optional[str] = None,
    experiment_start_idx: Optional[int] = None,
    experiment_end_idx: Optional[int] = None
) -> Tuple[np.ndarray, np.ndarray, Dict[str, List[float]], Dict[str, Dict[str, Any]]]:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    """
    Extract parameter data from the optimizer.

    Args:
        backend: The optimizer backend instance
        optimizer_id: The ID of the optimizer
        param1_name: The name of the first parameter
        param2_name: The name of the second parameter
        target_name: Optional name of the target to analyze (for multi-target optimizations)

    Returns:
        Tuple containing:
        - X: Array of parameter values
        - y: Array of target values
        - param_ranges: Dictionary of parameter ranges
        - param_configs: Dictionary of parameter configurations
    """
    # Get optimizer state
    try:
        optimizer = backend.get_optimizer(optimizer_id)
        if optimizer is None:
            logger.error(f"Optimizer {optimizer_id} not found")
            raise HTTPException(status_code=404, detail=f"Optimizer {optimizer_id} not found")
        logger.info(f"Successfully retrieved optimizer {optimizer_id}")
    except Exception as e:
        logger.error(f"Error getting optimizer: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting optimizer: {str(e)}")

    # Get measurements
    try:
        logger.info(f"Getting measurements for optimizer {optimizer_id}")
        measurements_response = backend.get_measurement_history(optimizer_id)
        if measurements_response.get("status") == "error":
            error_msg = measurements_response.get("message", "Error getting measurements")
            logger.error(f"Error in measurement history response: {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)

        measurements = measurements_response.get("measurements", [])
        logger.info(f"Retrieved {len(measurements)} measurements")
        if not measurements:
            logger.error("No measurements available for this optimization")
            raise HTTPException(status_code=400, detail="No measurements available for this optimization")

        # Filter measurements based on experiment range if provided
        if experiment_start_idx is not None or experiment_end_idx is not None:
            total_measurements = len(measurements)
            start_idx = experiment_start_idx if experiment_start_idx is not None else 0
            end_idx = experiment_end_idx if experiment_end_idx is not None else total_measurements

            # Ensure indices are within bounds
            start_idx = max(0, min(start_idx, total_measurements))
            end_idx = max(start_idx, min(end_idx, total_measurements))

            # Log some sample measurements before filtering
            if len(measurements) > 0:
                logger.info(f"Sample measurement before filtering: {measurements[0]}")
                if len(measurements) > 1:
                    logger.info(f"Another sample measurement before filtering: {measurements[-1]}")

            # Filter measurements
            filtered_measurements = measurements[start_idx:end_idx]

            # Log some sample measurements after filtering
            if len(filtered_measurements) > 0:
                logger.info(f"Sample measurement after filtering: {filtered_measurements[0]}")
                if len(filtered_measurements) > 1:
                    logger.info(f"Another sample measurement after filtering: {filtered_measurements[-1]}")

            logger.info(f"Filtered measurements from index {start_idx} to {end_idx} (total: {len(filtered_measurements)})")

            # Replace the measurements with the filtered ones
            measurements = filtered_measurements
    except Exception as e:
        logger.error(f"Error getting measurements: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting measurements: {str(e)}")

    # Get parameter configurations
    try:
        logger.info("Getting parameter configurations")
        param_configs = {}
        logger.info(f"Searchspace parameters: {[p.name for p in optimizer.searchspace.parameters]}")
        for param in optimizer.searchspace.parameters:
            if param.name == param1_name or param.name == param2_name:
                param_configs[param.name] = param
                logger.info(f"Found parameter {param.name} of type {param.__class__.__name__}")

        if param1_name not in param_configs:
            logger.error(f"Parameter {param1_name} not found in searchspace")
            raise HTTPException(status_code=400, detail=f"Parameter {param1_name} not found")
        if param2_name not in param_configs:
            logger.error(f"Parameter {param2_name} not found in searchspace")
            raise HTTPException(status_code=400, detail=f"Parameter {param2_name} not found")
    except Exception as e:
        logger.error(f"Error getting parameter configurations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting parameter configurations: {str(e)}")

    # Extract parameter ranges
    try:
        logger.info("Extracting parameter ranges")
        param_ranges = {}
        for name, config in param_configs.items():
            logger.info(f"Processing parameter {name} of type {config.__class__.__name__}")
            if hasattr(config, "bounds"):
                logger.info(f"Parameter {name} has bounds attribute")
                param_ranges[name] = [config.bounds.lower, config.bounds.upper]
                logger.info(f"Parameter {name} range: {param_ranges[name]}")
            elif hasattr(config, "lower_bound") and hasattr(config, "upper_bound"):
                logger.info(f"Parameter {name} has lower_bound and upper_bound attributes")
                param_ranges[name] = [config.lower_bound, config.upper_bound]
                logger.info(f"Parameter {name} range: {param_ranges[name]}")
            elif config.__class__.__name__ == "NumericalDiscreteParameter" and hasattr(config, "values"):
                logger.info(f"Parameter {name} is NumericalDiscreteParameter with {len(config.values)} values")
                param_ranges[name] = [min(config.values), max(config.values)]
                logger.info(f"Parameter {name} range: {param_ranges[name]}")
            elif hasattr(config, "values") and config.__class__.__name__ == "CategoricalParameter":
                logger.info(f"Parameter {name} is categorical with {len(config.values)} values")
                param_ranges[name] = [0, len(config.values) - 1]
                logger.info(f"Parameter {name} range: {param_ranges[name]}")
            else:
                logger.error(f"Cannot determine range for parameter {name} of type {config.__class__.__name__}")
                raise HTTPException(
                    status_code=400,
                    detail=f"Cannot determine range for parameter {name} of type {config.__class__.__name__}"
                )
    except Exception as e:
        logger.error(f"Error extracting parameter ranges: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error extracting parameter ranges: {str(e)}")

    # Extract parameter values and target values from measurements
    try:
        logger.info("Extracting parameter values and target values from measurements")
        X = []
        y = []

        # Get target name - either use the provided one or default to the first target
        targets = optimizer.objective.targets
        if not targets:
            logger.error("No targets found in the campaign")
            raise HTTPException(status_code=400, detail="No targets found in the campaign")

        # If target_name is provided, verify it exists in the campaign
        if target_name:
            target_names = [t.name for t in targets]
            if target_name not in target_names:
                logger.error(f"Target {target_name} not found in campaign. Available targets: {target_names}")
                raise HTTPException(
                    status_code=400,
                    detail=f"Target {target_name} not found. Available targets: {', '.join(target_names)}"
                )
            logger.info(f"Using specified target: {target_name}")
        else:
            # Default to first target if none specified
            target_name = targets[0].name
            logger.info(f"No target specified, using first target: {target_name}")

        for i, measurement in enumerate(measurements):
            logger.debug(f"Processing measurement {i+1}/{len(measurements)}")
            if param1_name in measurement and param2_name in measurement and target_name in measurement:
                # Handle categorical parameters by converting to indices
                param1_value = measurement[param1_name]
                param2_value = measurement[param2_name]

                if hasattr(param_configs[param1_name], "values") and param_configs[param1_name].__class__.__name__ == "CategoricalParameter":
                    try:
                        param1_value = param_configs[param1_name].values.index(param1_value)
                        logger.debug(f"Converted categorical value for {param1_name}: {measurement[param1_name]} -> {param1_value}")
                    except ValueError:
                        logger.debug(f"Skipping measurement: categorical value {measurement[param1_name]} not in list for {param1_name}")
                        # Skip this measurement if the categorical value is not in the list
                        continue

                if hasattr(param_configs[param2_name], "values") and param_configs[param2_name].__class__.__name__ == "CategoricalParameter":
                    try:
                        param2_value = param_configs[param2_name].values.index(param2_value)
                        logger.debug(f"Converted categorical value for {param2_name}: {measurement[param2_name]} -> {param2_value}")
                    except ValueError:
                        logger.debug(f"Skipping measurement: categorical value {measurement[param2_name]} not in list for {param2_name}")
                        # Skip this measurement if the categorical value is not in the list
                        continue

                X.append([float(param1_value), float(param2_value)])
                y.append(float(measurement[target_name]))
                logger.debug(f"Added data point: [{param1_value}, {param2_value}] -> {measurement[target_name]}")
            else:
                missing = []
                if param1_name not in measurement:
                    missing.append(param1_name)
                if param2_name not in measurement:
                    missing.append(param2_name)
                if target_name not in measurement:
                    missing.append(target_name)
                logger.debug(f"Skipping measurement: missing values for {', '.join(missing)}")

        logger.info(f"Extracted {len(X)} data points")
        if not X:
            logger.error(f"No measurements found with both parameters {param1_name} and {param2_name}")
            raise HTTPException(
                status_code=400,
                detail=f"No measurements found with both parameters {param1_name} and {param2_name}"
            )
    except Exception as e:
        logger.error(f"Error extracting parameter values: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error extracting parameter values: {str(e)}")

    return np.array(X), np.array(y), param_ranges, param_configs

def _build_surrogate_model(X: np.ndarray, y: np.ndarray) -> GaussianProcessRegressor:
    """
    Build a Gaussian Process surrogate model.

    Args:
        X: Array of parameter values
        y: Array of target values

    Returns:
        Trained Gaussian Process model
    """
    # Define kernel
    kernel = ConstantKernel() * Matern(nu=2.5, length_scale=[1.0] * X.shape[1])

    # Create and fit GP model
    gp = GaussianProcessRegressor(
        kernel=kernel,
        normalize_y=True,
        n_restarts_optimizer=5,
        random_state=42
    )
    gp.fit(X, y)

    return gp

def _detect_converged_parameters(
    X: np.ndarray,
    param_names: List[str],
    threshold: float = 1e-6
) -> Dict[str, float]:
    """
    Identify parameters that have converged to a single value.

    Args:
        X: Array of parameter values where each column corresponds to a parameter
        param_names: List of parameter names corresponding to columns in X
        threshold: Threshold for considering a parameter as converged

    Returns:
        Dictionary mapping parameter names to their fixed values
    """
    import logging
    logger = logging.getLogger(__name__)

    fixed_params = {}

    for i, param in enumerate(param_names):
        if i >= X.shape[1]:
            logger.warning(f"Parameter index {i} out of bounds for X with shape {X.shape}")
            continue

        values = X[:, i]
        unique_values = np.unique(values)

        logger.info(f"Checking parameter {param} for convergence: {len(unique_values)} unique values")

        if len(unique_values) == 1:
            fixed_params[param] = float(unique_values[0])
            logger.info(f"Parameter {param} has converged to a single value: {fixed_params[param]}")
        elif len(unique_values) > 1 and np.max(values) - np.min(values) < threshold:
            # Consider as converged if variation is below threshold
            fixed_params[param] = float(np.mean(values))
            logger.info(f"Parameter {param} has effectively converged (variation < {threshold}): {fixed_params[param]}")

    return fixed_params

def _perform_sobol_analysis_with_fixed_params(
    gp: GaussianProcessRegressor,
    param_ranges: Dict[str, List[float]],
    param1_name: str,
    param2_name: str,
    fixed_params: Dict[str, float] = None,
    num_samples: int = 1024,
    num_bootstrap: int = 100  # Number of bootstrap samples for confidence intervals
) -> Dict[str, Any]:
    """
    Perform Sobol' sensitivity analysis with handling for converged parameters.

    Args:
        gp: Trained Gaussian Process model
        param_ranges: Dictionary of parameter ranges
        param1_name: Name of first parameter
        param2_name: Name of second parameter
        fixed_params: Dictionary of parameters that have converged {param_name: fixed_value}
        num_samples: Number of samples for Sobol' analysis
        num_bootstrap: Number of bootstrap samples for confidence intervals

    Returns:
        Dictionary containing Sobol' indices with confidence intervals and information about fixed parameters
    """
    import logging
    logger = logging.getLogger(__name__)

    # Initialize fixed_params if not provided
    if fixed_params is None:
        fixed_params = {}

    logger.info(f"Performing Sobol analysis with fixed parameters: {fixed_params}")

    # Identify which parameters are fixed
    param1_fixed = param1_name in fixed_params
    param2_fixed = param2_name in fixed_params

    # Case 1: Both parameters are fixed
    if param1_fixed and param2_fixed:
        logger.info(f"Both parameters {param1_name} and {param2_name} have converged. No sensitivity analysis possible.")
        return {
            'S1': {param1_name: 0.0, param2_name: 0.0},
            'S1_confidence_intervals': {
                param1_name: [0.0, 0.0],
                param2_name: [0.0, 0.0]
            },
            'S2': 0.0,
            'S2_confidence_interval': [0.0, 0.0],
            'ST': {param1_name: 0.0, param2_name: 0.0},
            'ST_confidence_intervals': {
                param1_name: [0.0, 0.0],
                param2_name: [0.0, 0.0]
            },
            'fixed_params': [param1_name, param2_name],
            'message': f"Both parameters {param1_name} and {param2_name} have converged to fixed values. No sensitivity analysis possible."
        }

    # Case 2: One parameter is fixed
    if param1_fixed or param2_fixed:
        fixed_param = param1_name if param1_fixed else param2_name
        varying_param = param2_name if param1_fixed else param1_name

        logger.info(f"Parameter {fixed_param} has converged. Setting its sensitivity indices to 0.")
        logger.info(f"Parameter {varying_param} is the only source of variation. Setting its sensitivity indices to 1.")

        # For a single varying parameter, its first-order index is 1.0
        # (it explains 100% of the variance since it's the only source of variation)
        result = {
            'S1': {varying_param: 1.0},
            'S1_confidence_intervals': {
                varying_param: [1.0, 1.0]
            },
            'ST': {varying_param: 1.0},
            'ST_confidence_intervals': {
                varying_param: [1.0, 1.0]
            },
            'fixed_params': [fixed_param],
            'message': f"Parameter '{fixed_param}' has converged to a fixed value ({fixed_params[fixed_param]})."
        }

        # Set the fixed parameter's indices to 0
        result['S1'][fixed_param] = 0.0
        result['S1_confidence_intervals'][fixed_param] = [0.0, 0.0]
        result['ST'][fixed_param] = 0.0
        result['ST_confidence_intervals'][fixed_param] = [0.0, 0.0]
        result['S2'] = 0.0
        result['S2_confidence_interval'] = [0.0, 0.0]

        return result

    # Case 3: No parameters are fixed - perform standard Sobol analysis
    logger.info(f"No fixed parameters detected. Performing standard Sobol analysis with {num_samples} samples and {num_bootstrap} bootstrap iterations")

    # Define problem for SALib
    problem = {
        'num_vars': 2,
        'names': [param1_name, param2_name],
        'bounds': [param_ranges[param1_name], param_ranges[param2_name]]
    }

    # Generate samples using Saltelli's extension of Sobol' sequence
    param_values = saltelli.sample(problem, num_samples)
    logger.info(f"Generated {len(param_values)} Saltelli samples")

    # Evaluate model at sample points
    Y = np.zeros(param_values.shape[0])
    for i, X in enumerate(param_values):
        pred = gp.predict([X])
        Y[i] = pred[0]  # Extract the scalar value from the array

    # Perform Sobol' Analysis
    Si = sobol.analyze(problem, Y, print_to_console=False, calc_second_order=True)
    logger.info("Completed main Sobol analysis")

    # Calculate confidence intervals using bootstrap
    S1_param1_samples = []
    S1_param2_samples = []
    S2_samples = []
    ST_param1_samples = []
    ST_param2_samples = []

    # Bootstrap sampling
    n_samples = len(Y)
    logger.info(f"Starting bootstrap sampling with {n_samples} samples")

    for i in range(num_bootstrap):
        if i % 10 == 0:
            logger.info(f"Bootstrap iteration {i}/{num_bootstrap}")

        # Sample with replacement
        bootstrap_indices = np.random.choice(n_samples, n_samples, replace=True)
        Y_bootstrap = Y[bootstrap_indices]

        # Analyze bootstrap sample
        Si_bootstrap = sobol.analyze(problem, Y_bootstrap, print_to_console=False, calc_second_order=True)

        # Store results
        S1_param1_samples.append(float(Si_bootstrap['S1'][0]))
        S1_param2_samples.append(float(Si_bootstrap['S1'][1]))
        S2_samples.append(float(Si_bootstrap['S2'][0, 1]))
        ST_param1_samples.append(float(Si_bootstrap['ST'][0]))
        ST_param2_samples.append(float(Si_bootstrap['ST'][1]))

    # Calculate confidence intervals (5th and 95th percentiles)
    confidence_level = 0.95
    alpha = (1 - confidence_level) / 2

    S1_param1_ci = [float(np.percentile(S1_param1_samples, 100 * alpha)),
                    float(np.percentile(S1_param1_samples, 100 * (1 - alpha)))]
    S1_param2_ci = [float(np.percentile(S1_param2_samples, 100 * alpha)),
                    float(np.percentile(S1_param2_samples, 100 * (1 - alpha)))]
    S2_ci = [float(np.percentile(S2_samples, 100 * alpha)),
             float(np.percentile(S2_samples, 100 * (1 - alpha)))]
    ST_param1_ci = [float(np.percentile(ST_param1_samples, 100 * alpha)),
                    float(np.percentile(ST_param1_samples, 100 * (1 - alpha)))]
    ST_param2_ci = [float(np.percentile(ST_param2_samples, 100 * alpha)),
                    float(np.percentile(ST_param2_samples, 100 * (1 - alpha)))]

    logger.info("Completed bootstrap confidence interval calculation")
    logger.info(f"S1 {param1_name} CI: {S1_param1_ci}")
    logger.info(f"S1 {param2_name} CI: {S1_param2_ci}")
    logger.info(f"S2 CI: {S2_ci}")
    logger.info(f"ST {param1_name} CI: {ST_param1_ci}")
    logger.info(f"ST {param2_name} CI: {ST_param2_ci}")

    return {
        'S1': {
            param1_name: float(Si['S1'][0]),
            param2_name: float(Si['S1'][1])
        },
        'S1_confidence_intervals': {
            param1_name: S1_param1_ci,
            param2_name: S1_param2_ci
        },
        'S2': float(Si['S2'][0, 1]),
        'S2_confidence_interval': S2_ci,
        'ST': {
            param1_name: float(Si['ST'][0]),
            param2_name: float(Si['ST'][1])
        },
        'ST_confidence_intervals': {
            param1_name: ST_param1_ci,
            param2_name: ST_param2_ci
        },
        'fixed_params': []  # No fixed parameters
    }



def _generate_contour_data(
    gp: GaussianProcessRegressor,
    param_ranges: Dict[str, List[float]],
    param1_name: str,
    param2_name: str,
    grid_size: int = 50
) -> Dict[str, Any]:
    """
    Generate contour plot data.

    Args:
        gp: Trained Gaussian Process model
        param_ranges: Dictionary of parameter ranges
        param1_name: Name of first parameter
        param2_name: Name of second parameter
        grid_size: Size of the grid for contour plot

    Returns:
        Dictionary containing contour plot data
    """
    # Generate grid for contour plot
    x1 = np.linspace(param_ranges[param1_name][0], param_ranges[param1_name][1], grid_size)
    x2 = np.linspace(param_ranges[param2_name][0], param_ranges[param2_name][1], grid_size)
    X1, X2 = np.meshgrid(x1, x2)

    # Predict values across the grid
    Z = np.zeros(X1.shape)
    Z_std = np.zeros(X1.shape)

    for i in range(X1.shape[0]):
        for j in range(X1.shape[1]):
            x = np.array([[X1[i,j], X2[i,j]]])
            pred, std = gp.predict(x, return_std=True)
            Z[i,j] = pred[0]  # Extract the scalar value from the array
            Z_std[i,j] = std[0]  # Extract the scalar value from the array

    return {
        'x_values': x1.tolist(),
        'y_values': x2.tolist(),
        'z_values': Z.tolist(),
        'uncertainty': Z_std.tolist()
    }

@router.post("/{optimizer_id}/contour", response_class=SafeJSONResponse)
async def generate_sobol_contour(
    optimizer_id: str,
    request: SobolContourRequest,
    backend: BayesianOptimizationBackend = Depends(get_backend)
) -> Dict[str, Any]:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    """
    Generate contour plot data with Sobol' sensitivity analysis.

    Args:
        optimizer_id: The ID of the optimizer
        request: The request containing parameter names
        backend: The optimizer backend instance

    Returns:
        Contour plot data with Sobol' indices
    """
    try:
        logger.info(f"Processing SOBOL analysis request for optimizer {optimizer_id}")
        logger.info(f"Parameters: {request.param1_name}, {request.param2_name}")

        # Extract parameter data
        try:
            X, y, param_ranges, param_configs = _extract_parameter_data(
                backend,
                optimizer_id,
                request.param1_name,
                request.param2_name,
                request.target_name,
                request.experiment_start_idx,
                request.experiment_end_idx
            )
            logger.info(f"Extracted {len(X)} data points for analysis")
        except Exception as e:
            logger.error(f"Error extracting parameter data: {str(e)}")
            return {"status": "error", "message": f"Error extracting parameter data: {str(e)}"}

        # Build surrogate model
        try:
            gp = _build_surrogate_model(X, y)
            logger.info("Surrogate model built successfully")
        except Exception as e:
            logger.error(f"Error building surrogate model: {str(e)}")
            return {"status": "error", "message": f"Error building surrogate model: {str(e)}"}

        # Detect converged parameters
        try:
            # Get parameter names in the correct order
            param_names = [request.param1_name, request.param2_name]

            # Detect converged parameters
            fixed_params = _detect_converged_parameters(X, param_names)
            logger.info(f"Detected fixed parameters: {fixed_params}")

            # Perform Sobol analysis with handling for fixed parameters
            sobol_indices = _perform_sobol_analysis_with_fixed_params(
                gp,
                param_ranges,
                request.param1_name,
                request.param2_name,
                fixed_params,
                request.num_samples
            )
            logger.info("SOBOL analysis completed successfully")
        except Exception as e:
            logger.error(f"Error performing SOBOL analysis: {str(e)}")
            return {"status": "error", "message": f"Error performing SOBOL analysis: {str(e)}"}

        # Generate contour data
        try:
            contour_data = _generate_contour_data(
                gp,
                param_ranges,
                request.param1_name,
                request.param2_name,
                request.grid_size
            )
            logger.info("Contour data generated successfully")
        except Exception as e:
            logger.error(f"Error generating contour data: {str(e)}")
            return {"status": "error", "message": f"Error generating contour data: {str(e)}"}

        # Determine parameter types
        try:
            param1_config = param_configs[request.param1_name]
            param2_config = param_configs[request.param2_name]

            param1_is_categorical = hasattr(param1_config, "values") and param1_config.__class__.__name__ == "CategoricalParameter"
            param2_is_categorical = hasattr(param2_config, "values") and param2_config.__class__.__name__ == "CategoricalParameter"

            param1_categories = param1_config.values if param1_is_categorical else None
            param2_categories = param2_config.values if param2_is_categorical else None

            # Get parameter types
            param1_type = param1_config.__class__.__name__
            param2_type = param2_config.__class__.__name__

            logger.info(f"Parameter types determined: {param1_type}, {param2_type}")
        except Exception as e:
            logger.error(f"Error determining parameter types: {str(e)}")
            return {"status": "error", "message": f"Error determining parameter types: {str(e)}"}

        # Combine results
        try:
            # Get the actual target name used (either the specified one or the default)
            used_target_name = request.target_name
            if not used_target_name:
                # We need to get the optimizer again to access the targets
                optimizer = backend.get_optimizer(optimizer_id)
                used_target_name = optimizer.objective.targets[0].name

            result = {
                "status": "success",
                "x_values": contour_data['x_values'],
                "y_values": contour_data['y_values'],
                "z_values": contour_data['z_values'],
                "uncertainty": contour_data['uncertainty'],
                "sobol_first_order": sobol_indices['S1'],
                "sobol_first_order_ci": sobol_indices.get('S1_confidence_intervals', {}),
                "sobol_second_order": sobol_indices['S2'],
                "sobol_second_order_ci": sobol_indices.get('S2_confidence_interval', []),
                "sobol_total": sobol_indices['ST'],
                "sobol_total_ci": sobol_indices.get('ST_confidence_intervals', {}),
                "param1_type": param1_type,
                "param2_type": param2_type,
                "param1_is_categorical": param1_is_categorical,
                "param2_is_categorical": param2_is_categorical,
                "param1_categories": param1_categories,
                "param2_categories": param2_categories,
                "target_name": used_target_name,
                "experiment_start_idx": request.experiment_start_idx,
                "experiment_end_idx": request.experiment_end_idx,
                "fixed_params": sobol_indices.get('fixed_params', []),
                "fixed_params_message": sobol_indices.get('message', None)
            }
            logger.info("SOBOL analysis completed successfully")
            return result
        except Exception as e:
            logger.error(f"Error preparing final result: {str(e)}")
            return {"status": "error", "message": f"Error preparing final result: {str(e)}"}

    except Exception as e:
        logger.error(f"Unexpected error in SOBOL analysis: {str(e)}")
        return {
            "status": "error",
            "message": f"Unexpected error in SOBOL analysis: {str(e)}"
        }
