from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from ..middleware.auth import AuthMiddleware, create_access_token, get_current_user_id

router = APIRouter(prefix="/auth", tags=["auth"])
auth_handler = AuthMiddleware()

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"

class UserAuth(BaseModel):
    user_id: str
    clerk_token: str  # Token from Clerk for verification

@router.post("/token", response_model=TokenResponse)
async def get_access_token(user_auth: UserAuth):
    # Here you would typically verify the Clerk token
    # For now, we'll just create a token with the user_id
    access_token = create_access_token(user_auth.user_id)
    return TokenResponse(access_token=access_token)

@router.get("/verify")
async def verify_token(credentials = Depends(auth_handler)):
    user_id = get_current_user_id(credentials)
    return {"status": "valid", "user_id": user_id}