"""Utility functions for accessing the backend."""

from fastapi import HTTPException

# Global variable to store the backend instance
_backend_instance = None

def set_backend_instance(backend):
    """Set the global backend instance."""
    global _backend_instance
    _backend_instance = backend

def get_backend():
    """Get the global backend instance."""
    if _backend_instance is None:
        raise HTTPException(status_code=500, detail="Backend not initialized")
    return _backend_instance

def get_optimizer(optimizer_id: str):
    """Get an optimizer by ID."""
    backend = get_backend()
    return backend.get_optimizer(optimizer_id)
