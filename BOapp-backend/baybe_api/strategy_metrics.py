"""Module for calculating exploration-exploitation metrics for Bayesian optimization."""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Union
from baybe import Campaign
from baybe.acquisition import UpperConfidenceBound, qUpperConfidenceBound
from baybe.acquisition import ExpectedImprovement, qExpectedImprovement
from baybe.acquisition import ProbabilityOfImprovement, qProbabilityOfImprovement
from baybe.acquisition import PosteriorMean, PosteriorStandardDeviation

# Set up logging
logger = logging.getLogger(__name__)

def calculate_parameter_diversity(
    campaign: Campaign,
    window_size: int = 5
) -> Dict[str, float]:
    """Calculate the diversity of parameter values in recent experiments.
    
    Args:
        campaign: The optimization campaign
        window_size: Number of recent experiments to consider
        
    Returns:
        Dictionary with diversity metrics for each parameter
    """
    try:
        # Get parameter names
        parameter_names = [p.name for p in campaign.parameters]
        
        # Get measurements data
        measurements = campaign.measurements
        
        if len(measurements) < 2:
            return {param: 0.0 for param in parameter_names}
        
        # Get the most recent experiments
        recent_measurements = measurements.iloc[-min(window_size, len(measurements)):]
        
        # Calculate diversity for each parameter
        diversity = {}
        for param in parameter_names:
            if param in recent_measurements.columns:
                # Convert to numeric if possible
                values = pd.to_numeric(recent_measurements[param], errors='coerce')
                
                # Skip if all values are NaN
                if values.isna().all():
                    diversity[param] = 0.0
                    continue
                
                # Calculate normalized variance
                if len(values.dropna()) > 1:
                    variance = values.var()
                    # Normalize by the parameter range if available
                    param_obj = next((p for p in campaign.parameters if p.name == param), None)
                    if param_obj and hasattr(param_obj, 'bounds'):
                        bounds = param_obj.bounds
                        param_range = bounds[1] - bounds[0]
                        if param_range > 0:
                            variance = variance / (param_range ** 2)
                    
                    diversity[param] = float(variance)
                else:
                    diversity[param] = 0.0
            else:
                diversity[param] = 0.0
        
        return diversity
    
    except Exception as e:
        logger.error(f"Error calculating parameter diversity: {str(e)}")
        return {}

def calculate_target_improvement_rate(
    campaign: Campaign,
    window_size: int = 5
) -> Dict[str, float]:
    """Calculate the improvement rate of target values in recent experiments.
    
    Args:
        campaign: The optimization campaign
        window_size: Number of recent experiments to consider
        
    Returns:
        Dictionary with improvement rate metrics for each target
    """
    try:
        # Get target names and modes
        targets = campaign.targets
        target_names = [t.name for t in targets]
        target_modes = {t.name: t.mode for t in targets}
        
        # Get measurements data
        measurements = campaign.measurements
        
        if len(measurements) < 2:
            return {target: 0.0 for target in target_names}
        
        # Get the most recent experiments
        recent_measurements = measurements.iloc[-min(window_size, len(measurements)):]
        
        # Calculate improvement rate for each target
        improvement_rates = {}
        for target in target_names:
            if target in recent_measurements.columns:
                # Convert to numeric
                values = pd.to_numeric(recent_measurements[target], errors='coerce')
                
                # Skip if all values are NaN
                if values.isna().all():
                    improvement_rates[target] = 0.0
                    continue
                
                # Calculate improvement rate based on target mode
                is_maximize = target_modes[target] == "MAX"
                
                # Calculate running best
                if is_maximize:
                    running_best = values.expanding().max()
                else:
                    running_best = values.expanding().min()
                
                # Calculate improvements
                improvements = []
                for i in range(1, len(running_best)):
                    if running_best.iloc[i] != running_best.iloc[i-1]:
                        if is_maximize:
                            improvement = running_best.iloc[i] - running_best.iloc[i-1]
                        else:
                            improvement = running_best.iloc[i-1] - running_best.iloc[i]
                        improvements.append(float(improvement))
                
                # Calculate average improvement rate
                if improvements:
                    # Normalize by the target range
                    target_range = values.max() - values.min()
                    if target_range > 0:
                        avg_improvement = sum(improvements) / len(improvements) / target_range
                    else:
                        avg_improvement = sum(improvements) / len(improvements)
                    
                    improvement_rates[target] = float(avg_improvement)
                else:
                    improvement_rates[target] = 0.0
            else:
                improvement_rates[target] = 0.0
        
        return improvement_rates
    
    except Exception as e:
        logger.error(f"Error calculating target improvement rate: {str(e)}")
        return {}

def calculate_ucb_components(
    campaign: Campaign,
    beta: float = 0.2
) -> Dict[str, List[Dict[str, float]]]:
    """Calculate the Upper Confidence Bound components for each experiment.
    
    Args:
        campaign: The optimization campaign
        beta: The beta parameter for UCB
        
    Returns:
        Dictionary with UCB components for each experiment
    """
    try:
        # Get measurements data
        measurements = campaign.measurements
        
        if len(measurements) < 2:
            return {"experiments": []}
        
        # Get surrogate model
        if not hasattr(campaign, 'recommender') or not campaign.recommender:
            logger.warning("Campaign does not have a recommender")
            return {"experiments": []}
        
        recommender = campaign.recommender
        if not hasattr(recommender, 'surrogate_model') or not recommender.surrogate_model:
            logger.warning("Recommender does not have a surrogate model")
            return {"experiments": []}
        
        surrogate = recommender.surrogate_model
        
        # Calculate posterior mean and standard deviation for each experiment
        results = []
        for idx, row in measurements.iterrows():
            try:
                # Create a dataframe with just this experiment
                experiment_df = pd.DataFrame([row])
                
                # Get parameter columns only
                param_names = [p.name for p in campaign.parameters]
                param_df = experiment_df[param_names]
                
                # Calculate posterior mean and standard deviation
                with np.errstate(divide='ignore', invalid='ignore'):
                    posterior = surrogate.posterior(param_df)
                    mean = float(posterior.mean.numpy().item())
                    std = float(posterior.variance.sqrt().numpy().item())
                
                # Calculate UCB components
                exploration = beta * std
                exploitation = mean
                
                # Calculate balance
                total = abs(exploitation) + abs(exploration)
                if total > 0:
                    balance = (exploration - exploitation) / total
                else:
                    balance = 0.0
                
                results.append({
                    "experiment_index": int(idx),
                    "mean": float(mean),
                    "std": float(std),
                    "exploration": float(exploration),
                    "exploitation": float(exploitation),
                    "balance": float(balance),
                    "ucb_value": float(exploitation + exploration)
                })
            except Exception as e:
                logger.error(f"Error calculating UCB components for experiment {idx}: {str(e)}")
                continue
        
        return {"experiments": results}
    
    except Exception as e:
        logger.error(f"Error calculating UCB components: {str(e)}")
        return {"experiments": []}

def calculate_strategy_metrics(
    campaign: Campaign,
    window_size: int = 5,
    beta: float = 0.2
) -> Dict[str, Any]:
    """Calculate exploration-exploitation metrics for a campaign.
    
    Args:
        campaign: The optimization campaign
        window_size: Number of recent experiments to consider for trend metrics
        beta: The beta parameter for UCB calculations
        
    Returns:
        Dictionary with strategy metrics
    """
    try:
        # Calculate parameter diversity
        diversity = calculate_parameter_diversity(campaign, window_size)
        
        # Calculate target improvement rate
        improvement_rates = calculate_target_improvement_rate(campaign, window_size)
        
        # Calculate UCB components
        ucb_components = calculate_ucb_components(campaign, beta)
        
        # Calculate overall exploration and exploitation scores
        exploration_score = sum(diversity.values()) / max(1, len(diversity))
        exploitation_score = sum(improvement_rates.values()) / max(1, len(improvement_rates))
        
        # Normalize scores
        total = exploration_score + exploitation_score
        if total > 0:
            normalized_exploration = exploration_score / total
            normalized_exploitation = exploitation_score / total
        else:
            normalized_exploration = 0.5
            normalized_exploitation = 0.5
        
        # Calculate balance (-1 to 1, where -1 is pure exploitation, 1 is pure exploration)
        balance = normalized_exploration - normalized_exploitation
        
        # Determine dominant strategy
        if balance > 0.2:
            dominant_strategy = "exploration"
        elif balance < -0.2:
            dominant_strategy = "exploitation"
        else:
            dominant_strategy = "balanced"
        
        # Calculate strategy shifts
        strategy_shifts = []
        if "experiments" in ucb_components and len(ucb_components["experiments"]) > 1:
            experiments = ucb_components["experiments"]
            for i in range(1, len(experiments)):
                prev_balance = experiments[i-1]["balance"]
                curr_balance = experiments[i]["balance"]
                
                # Detect significant shifts in balance
                if abs(curr_balance - prev_balance) > 0.3:
                    strategy_shifts.append({
                        "experiment_index": experiments[i]["experiment_index"],
                        "previous_balance": prev_balance,
                        "current_balance": curr_balance,
                        "shift_magnitude": curr_balance - prev_balance
                    })
        
        return {
            "parameter_diversity": diversity,
            "improvement_rates": improvement_rates,
            "ucb_components": ucb_components,
            "exploration_score": float(exploration_score),
            "exploitation_score": float(exploitation_score),
            "normalized_exploration": float(normalized_exploration),
            "normalized_exploitation": float(normalized_exploitation),
            "balance": float(balance),
            "dominant_strategy": dominant_strategy,
            "strategy_shifts": strategy_shifts
        }
    
    except Exception as e:
        logger.error(f"Error calculating strategy metrics: {str(e)}")
        return {
            "error": str(e),
            "parameter_diversity": {},
            "improvement_rates": {},
            "ucb_components": {"experiments": []},
            "exploration_score": 0.0,
            "exploitation_score": 0.0,
            "normalized_exploration": 0.5,
            "normalized_exploitation": 0.5,
            "balance": 0.0,
            "dominant_strategy": "unknown",
            "strategy_shifts": []
        }
