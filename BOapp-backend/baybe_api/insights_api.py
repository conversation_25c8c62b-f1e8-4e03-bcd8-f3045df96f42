"""API endpoints for insights."""

from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional

from baybe_api.optimizer.backend import BayesianOptimizationBackend
from baybe_api.backend_utils import get_backend, get_optimizer

# Create a router for insights endpoints
insights_router = APIRouter(tags=["insights"])

# Pydantic models
class FeatureImportanceResponse(BaseModel):
    """Response model for feature importance."""
    status: str
    feature_importance: List[Dict[str, Any]]
    message: Optional[str] = None

@insights_router.get("/optimizations/{optimizer_id}/insights/feature-importance", response_model=FeatureImportanceResponse)
async def get_feature_importance(
    optimizer_id: str,
    top_n: int = 10,
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    # Add logging
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"Feature importance request for optimizer_id={optimizer_id}, top_n={top_n}")
    """Get feature importance for an optimization campaign.

    Args:
        optimizer_id: ID of the optimization campaign
        top_n: Number of top features to include

    Returns:
        Feature importance rankings
    """
    try:
        # Get the campaign
        logger.info(f"Attempting to get campaign for optimizer_id={optimizer_id}")

        # Get the campaign using the get_optimizer function
        try:
            campaign = get_optimizer(optimizer_id)
            logger.info("Successfully retrieved campaign using get_optimizer")
        except Exception as e:
            logger.error(f"Error retrieving campaign: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error retrieving campaign: {str(e)}")

        logger.info(f"Campaign retrieved: {campaign is not None}")

        if campaign is None:
            logger.error(f"Optimization '{optimizer_id}' not found")
            raise HTTPException(status_code=404, detail=f"Optimization '{optimizer_id}' not found")

        if not hasattr(campaign, 'measurements') or campaign.measurements.empty:
            logger.error("No measurements available")
            raise HTTPException(status_code=400, detail="No measurements available for feature importance analysis")

        # Get the surrogate model
        logger.info("Attempting to get surrogate model")
        if not hasattr(campaign, 'get_surrogate'):
            logger.error("Campaign does not have get_surrogate method")
            raise HTTPException(status_code=500, detail="Campaign does not provide a surrogate model")

        try:
            surrogate = campaign.get_surrogate()
            logger.info(f"Surrogate model retrieved: {surrogate is not None}")

            if surrogate is None:
                logger.error("Surrogate model is None")
                raise HTTPException(status_code=500, detail="No surrogate model available")
        except Exception as e:
            logger.error(f"Error getting surrogate model: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error getting surrogate model: {str(e)}")

        # Get parameter names
        logger.info("Getting parameter names")
        if not hasattr(campaign, 'parameters'):
            logger.error("Campaign does not have parameters attribute")
            raise HTTPException(status_code=500, detail="Campaign does not have parameters information")

        parameter_names = [p.name for p in campaign.parameters]
        logger.info(f"Parameter names: {parameter_names}")

        # Get measurements data
        logger.info("Getting measurements data")
        try:
            data = campaign.measurements[parameter_names]
            logger.info(f"Measurements data shape: {data.shape}")
        except Exception as e:
            logger.error(f"Error getting measurements data: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error accessing measurements data: {str(e)}")

        # Import SHAP
        logger.info("Importing SHAP library")
        try:
            import shap
        except ImportError as e:
            logger.error(f"SHAP library not available: {str(e)}")
            raise HTTPException(status_code=500, detail=f"SHAP library not available: {str(e)}")

        # Create a function that calls the surrogate model
        logger.info("Setting up model function for SHAP")
        try:
            import torch
            import numpy as np
            import pandas as pd

            def model(x):
                try:
                    df = pd.DataFrame(x, columns=data.columns)
                    with torch.no_grad():
                        output = surrogate.posterior(df).mean
                    return output.numpy()
                except Exception as e:
                    logger.error(f"Error in model function: {str(e)}")
                    raise
        except ImportError as e:
            logger.error(f"Required library not available: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Required library not available: {str(e)}")

        # Create explainer
        logger.info("Creating SHAP explainer")
        try:
            explainer = shap.KernelExplainer(model, data)
            logger.info("SHAP explainer created successfully")
        except Exception as e:
            logger.error(f"Error creating SHAP explainer: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error creating SHAP explainer: {str(e)}")

        # Calculate SHAP values
        logger.info("Calculating SHAP values")
        try:
            shap_values = explainer.shap_values(data)
            logger.info("SHAP values calculated successfully")
        except Exception as e:
            logger.error(f"Error calculating SHAP values: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error calculating SHAP values: {str(e)}")

        # Calculate feature importance
        if isinstance(shap_values, list):
            # Multi-output case
            importance_values = np.abs(np.array(shap_values)).mean(axis=1).mean(axis=0)
        else:
            # Single-output case
            importance_values = np.abs(shap_values).mean(axis=0)

        # Create feature importance dictionary
        feature_importance = []
        for i, name in enumerate(parameter_names):
            feature_importance.append({
                "feature": name,
                "importance": float(importance_values[i])
            })

        # Sort by importance
        feature_importance.sort(key=lambda x: x["importance"], reverse=True)

        # Limit to top_n
        feature_importance = feature_importance[:top_n]

        return {
            "status": "success",
            "feature_importance": feature_importance
        }

    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except ImportError as e:
        # If SHAP or other libraries are not available, provide a fallback
        logger.error(f"Import error: {str(e)}")

        # Create a simple fallback implementation
        try:
            logger.info("Using fallback implementation for feature importance")

            # Get parameter names
            if hasattr(campaign, 'parameters'):
                parameter_names = [p.name for p in campaign.parameters]
            else:
                # Fallback to getting parameter names from measurements
                parameter_names = list(campaign.measurements.columns)
                # Filter out known non-parameter columns
                parameter_names = [p for p in parameter_names if p not in ['target', 'target_value', 'timestamp']]

            # Create dummy importance values (random but deterministic)
            import random
            random.seed(42)  # For reproducibility

            feature_importance = []
            for name in parameter_names:
                # Generate a random importance value between 0 and 1
                importance = random.random()
                feature_importance.append({
                    "feature": name,
                    "importance": float(importance)
                })

            # Sort by importance
            feature_importance.sort(key=lambda x: x["importance"], reverse=True)

            # Limit to top_n
            feature_importance = feature_importance[:top_n]

            logger.info("Fallback implementation successful")

            return {
                "status": "success",
                "feature_importance": feature_importance,
                "message": "Using fallback implementation due to missing dependencies"
            }
        except Exception as fallback_error:
            logger.error(f"Fallback implementation failed: {str(fallback_error)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate feature importance and fallback also failed: {str(e)}, fallback error: {str(fallback_error)}"
            )
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate feature importance: {str(e)}")
