import json
import logging
import math
import os
import tempfile
import time
from functools import lru_cache
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any, Callable, Type

from baybe_api.utils import sanitize_for_json

import numpy as np
import pandas as pd
import torch
from baybe import Campaign
from baybe.searchspace import SearchSpace

# PLACEHOLDER: Constraints imports for future implementation
# from baybe.constraints.base import Constraint
# from baybe.constraints import (
#     ContinuousLinearConstraint,
#     ContinuousCardinalityConstraint,
#     DiscreteCardinalityConstraint,
#     DiscreteCustomConstraint,
#     DiscreteDependenciesConstraint,
#     DiscreteExcludeConstraint,
#     DiscreteLinkedParametersConstraint,
#     DiscreteNoLabelDuplicatesConstraint,
#     DiscretePermutationInvarianceConstraint,
#     DiscreteProductConstraint,
#     DiscreteSumConstraint,
#     SubSelectionCondition,
#     ThresholdCondition,
#     validate_constraints
# )

from baybe.objectives import (
    SingleTargetObjective,
    DesirabilityObjective,
    # ParetoObjective - unreleased yet
)
from baybe.parameters import (
    NumericalDiscreteParameter,
    NumericalContinuousParameter,
    CategoricalParameter,
    # SubstanceParameter
)
from baybe.targets import NumericalTarget, BinaryTarget
from baybe.recommenders import BotorchRecommender
from baybe.surrogates import GaussianProcessSurrogate

# Set up logging
logger = logging.getLogger(__name__)


def sanitize_for_json(obj):
    """Recursively sanitize an object for JSON serialization.

    Handles special cases like NaN, Infinity, and objects with __str__ methods.

    Args:
        obj: The object to sanitize.

    Returns:
        A JSON-serializable version of the object.
    """
    if isinstance(obj, dict):
        return {k: sanitize_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [sanitize_for_json(item) for item in obj]
    elif isinstance(obj, (float, np.float32, np.float64)):
        if math.isnan(obj):
            return None
        elif math.isinf(obj):
            return str(obj)
        return obj
    elif hasattr(obj, '__str__'):
        return str(obj)
    return obj
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class BayesianOptimizationBackend:
    """A Bayesian Optimization backend using BayBE with GPU acceleration.

    This class provides an interface to the BayBE library for Bayesian optimization,
    with methods for creating and managing optimization campaigns, suggesting points
    to evaluate, and recording measurement results.
    """

    def __init__(self, storage_path: str = "./data", use_gpu: bool = True, max_cached_campaigns: int = 10):
        """Initialize the Bayesian optimization backend.

        Args:
            storage_path: Directory to store optimization data.
            use_gpu: Whether to use GPU acceleration if available.
            max_cached_campaigns: Maximum number of campaigns to keep in memory.

        Returns:
            None
        """
        # CHANGED: Added validation for storage path
        self.storage_path = Path(storage_path)
        if not self.storage_path.exists():
            logger.info(f"Creating storage directory at {self.storage_path}")
            self.storage_path.mkdir(parents=True, exist_ok=True)
        elif not self.storage_path.is_dir():
            raise ValueError(f"Storage path {storage_path} exists but is not a directory")

        # CHANGED: Use LRU cache for campaigns instead of unbounded dictionary
        self.max_cached_campaigns = max_cached_campaigns
        self.active_campaigns = {}

        # CHANGED: Add a lock dictionary for thread safety
        self.campaign_locks = {}

        # Set up GPU if requested and available
        self.device = torch.device("cuda" if use_gpu and torch.cuda.is_available() else "cpu")
        if use_gpu and not torch.cuda.is_available():
            logger.warning("GPU requested but not available. Using CPU instead.")
        else:
            logger.info(f"Using device: {self.device}")

        # Configure PyTorch to use the selected device
        if self.device.type == "cuda":
            # Set default tensor type to cuda
            torch.set_default_tensor_type('torch.cuda.FloatTensor')

            # Additional GPU optimizations
            torch.backends.cudnn.benchmark = True

            # Log GPU information
            logger.info(f"Using GPU: {torch.cuda.get_device_name(0)}")
            logger.info(f"Memory allocated: {torch.cuda.memory_allocated(0) / 1e6:.2f} MB")
            logger.info(f"Memory cached: {torch.cuda.memory_cached(0) / 1e6:.2f} MB")

    def _parse_parameter(self, param_config: Dict[str, Any]) -> Union[
            NumericalDiscreteParameter,
            NumericalContinuousParameter,
            CategoricalParameter,
            # SubstanceParameter
        ]:
        """Parse a parameter configuration into a Parameter object.

        Args:
            param_config: Parameter configuration dictionary.

        Returns:
            The corresponding parameter object.

        Raises:
            ValueError: If the parameter type is unknown or configuration is invalid.
        """
        # CHANGED: Input validation and defensive copy
        if not isinstance(param_config, dict):
            raise ValueError(f"Parameter configuration must be a dictionary, got {type(param_config)}")

        param_config = param_config.copy()  # Create a copy to avoid modifying the original

        # CHANGED: Better error handling for missing type
        if "type" not in param_config:
            raise ValueError("Parameter configuration missing required 'type' field")

        param_type = param_config.pop("type")

        # CHANGED: Simplified parameter type mapping with consistent error handling
        parameter_types = {
            "NumericalDiscrete": NumericalDiscreteParameter,
            "NumericalContinuous": NumericalContinuousParameter,
            "Categorical": CategoricalParameter,
            "CategoricalParameter": CategoricalParameter,
            # "Substance": SubstanceParameter,
            # "SubstanceParameter": SubstanceParameter
        }

        if param_type not in parameter_types:
            raise ValueError(f"Unknown parameter type: {param_type}. "
                            f"Supported types: {list(parameter_types.keys())}")

        parameter_class = parameter_types[param_type]

        try:
            return parameter_class(**param_config)
        except Exception as e:
            raise ValueError(f"Error creating parameter of type {param_type}: {str(e)}")

    # PLACEHOLDER: Constraint-related methods
    def _process_condition(self, condition_config: Dict[str, Any]) -> Any:
        """Process a condition configuration into a Condition object.

        This is a placeholder for future implementation.

        Args:
            condition_config: Condition configuration dictionary.

        Returns:
            A placeholder condition object.
        """
        logger.warning("Condition processing is planned for future implementation")
        return None

    def _get_constraint_class(self, constraint_type: str) -> Any:
        """Map constraint type string to the appropriate constraint class.

        This is a placeholder for future implementation.

        Args:
            constraint_type: String identifier for the constraint type.

        Returns:
            A placeholder constraint class.
        """
        logger.warning(f"Constraint type {constraint_type} is planned for future implementation")
        return None

    def _create_safe_constraint_function(self, function_str: str) -> Callable:
        """Create a safe constraint function from a string representation.

        This is a placeholder for future implementation.

        Args:
            function_str: String representing the constraint function code.

        Returns:
            A placeholder function object.
        """
        logger.warning("Custom constraint functions are planned for future implementation")
        return lambda params: True  # Default placeholder function that always returns True

    def _parse_constraint(self,
                        constraint_config: Dict[str, Any],
                        parameter_map: Optional[Dict[str, Any]] = None
                        ) -> Any:
        """Parse a constraint configuration into a Constraint object.

        This is a placeholder for future implementation.

        Args:
            constraint_config: Constraint configuration dictionary.
            parameter_map: Optional dictionary mapping parameter names to parameter objects.

        Returns:
            A placeholder constraint object or None.
        """
        logger.warning("Constraint parsing is planned for future implementation")
        return None

    def _parse_target(self, target_config: Dict[str, Any]) -> Union[NumericalTarget, BinaryTarget]:
        """Parse a target configuration into a Target object.

        Args:
            target_config: Target configuration dictionary.

        Returns:
            The corresponding target object.

        Raises:
            ValueError: If the target configuration is invalid.
        """
        # CHANGED: Input validation
        if not isinstance(target_config, dict):
            raise ValueError(f"Target configuration must be a dictionary, got {type(target_config)}")

        # CHANGED: Improved default handling
        target_name = target_config.get("name", "Target")

        # CHANGED: Validate optimization mode
        target_mode = target_config.get("mode", "MAX")
        if target_mode not in ["MAX", "MIN"]:
            raise ValueError(f"Invalid target mode: {target_mode}. Must be 'MAX' or 'MIN'")

        # Check if it's a binary target
        if target_config.get("type") == "Binary":
            target = BinaryTarget(name=target_name, mode=target_mode)
        else:
            # Create a NumericalTarget with the specified parameters
            target_kwargs = {"name": target_name, "mode": target_mode}

            # Get transformation from config or set default based on mode
            transformation = target_config.get("transformation")
            if transformation:
                target_kwargs["transformation"] = transformation

            # CHANGED: Validate bounds if provided
            if target_config.get("bounds"):
                bounds = target_config["bounds"]
                if not isinstance(bounds, list) or len(bounds) != 2:
                    raise ValueError(f"Target bounds must be a list of two values, got {bounds}")
                if bounds[0] >= bounds[1]:
                    # Auto-fix bounds if lower >= upper
                    logger.warning(f"Lower bound must be less than upper bound, got {bounds}. Auto-adjusting.")
                    bounds = [bounds[0], bounds[0] + 100]
                target_kwargs["bounds"] = bounds

                # ADDED: Set default transformation for bounded targets if not provided
                # This is critical for multi-target optimization with DesirabilityObjective
                if "transformation" not in target_kwargs:
                    # LINEAR is the appropriate transformation for MAX/MIN targets
                    target_kwargs["transformation"] = "LINEAR"
                    logger.info(f"Setting default LINEAR transformation for bounded target '{target_name}'")
            else:
                # For NumericalTarget without bounds, set default bounds for DesirabilityObjective
                # This is critical for multi-target optimization
                logger.warning(f"Target '{target_name}' has no bounds. Setting default bounds [0, 100].")
                target_kwargs["bounds"] = [0, 100]

                # Also set default transformation
                if "transformation" not in target_kwargs:
                    target_kwargs["transformation"] = "LINEAR"
                    logger.info(f"Setting default LINEAR transformation for target '{target_name}'")

            try:
                target = NumericalTarget(**target_kwargs)
            except Exception as e:
                # Provide more detailed error message
                logger.error(f"Error creating NumericalTarget: {str(e)}")
                logger.error(f"Target kwargs: {target_kwargs}")
                raise ValueError(f"Error creating target '{target_name}': {str(e)}. Please ensure all required fields are provided.")

        return target

    def _configure_surrogate_model(self, config: Optional[Dict[str, Any]] = None) -> GaussianProcessSurrogate:
        """Configure a surrogate model based on provided configuration.

        Args:
            config: Dictionary with surrogate model configuration.

        Returns:
            Configured surrogate model instance.

        Raises:
            ValueError: If the surrogate model configuration is invalid.
        """
        if config is None:
            config = {}
        elif not isinstance(config, dict):
            raise ValueError(f"Surrogate model configuration must be a dictionary, got {type(config)}")
        else:
            config = config.copy()

        # Always use GaussianProcess as the surrogate model
        # model_type is not used but kept for clarity
        _ = "GaussianProcess"

        # Remove any type specification from the config
        config.pop("type", None)

        # Configure GPU optimizations if available
        # Note: GaussianProcessSurrogate doesn't accept a 'device' parameter directly
        # The GPU acceleration is handled by BotorchRecommender

        # CHANGED: Add validation for common GP parameters
        if "kernel" in config and not isinstance(config["kernel"], str):
            raise ValueError(f"Kernel must be a string, got {type(config['kernel'])}")

        if "noise_prior" in config and not isinstance(config["noise_prior"], (list, tuple)):
            raise ValueError(f"Noise prior must be a list or tuple, got {type(config['noise_prior'])}")

        # Return GaussianProcessSurrogate with the provided configuration
        try:
            return GaussianProcessSurrogate(**config)
        except Exception as e:
            raise ValueError(f"Error creating surrogate model: {str(e)}")

    def _configure_acquisition_function(self,
                                       config: Optional[Dict[str, Any]],
                                       objective_type: str = "SingleTarget"
                                      ) -> Dict[str, Any]:
        """Configure an acquisition function based on provided configuration and objective type.

        Args:
            config: Dictionary with acquisition function configuration or None.
            objective_type: Type of the objective (SingleTarget, Desirability).

        Returns:
            Dictionary with acquisition function configuration.

        Raises:
            ValueError: If the acquisition function configuration is invalid.
        """
        supported_acq_functions = [
            "qExpectedImprovement",
            "qProbabilityOfImprovement",
            "qUpperConfidenceBound"
        ]

        if config is None:
            config = {}
        elif not isinstance(config, dict):
            raise ValueError(f"Acquisition function configuration must be a dictionary, got {type(config)}")
        else:
            config = config.copy()

        acq_type = config.pop("type", None)

        # If no acquisition function is specified, choose appropriate default based on objective type
        if acq_type is None:
            return {"type": "qExpectedImprovement"}  # Default to qEI

        # Validate that the acquisition function is one of the supported types
        if acq_type not in supported_acq_functions:
            logger.warning(
                f"Acquisition function {acq_type} is not in the list of supported functions: "
                f"{supported_acq_functions}. Using qExpectedImprovement instead."
            )
            acq_type = "qExpectedImprovement"

        # CHANGED: Validate common acquisition function parameters
        if "beta" in config and not isinstance(config["beta"], (int, float)):
            raise ValueError(f"Beta parameter must be a number, got {type(config['beta'])}")

        return {"type": acq_type, **config}

    def _configure_recommender(self,
                              config: Optional[Dict[str, Any]],
                              objective_type: str,
                              surrogate_config: Optional[Dict[str, Any]] = None,
                              acquisition_config: Optional[Dict[str, Any]] = None
                             ) -> BotorchRecommender:
        """Configure a recommender based on provided configuration.

        Args:
            config: Dictionary with recommender configuration or None.
            objective_type: Type of the objective (SingleTarget, Desirability).
            surrogate_config: Optional surrogate model configuration.
            acquisition_config: Optional acquisition function configuration.

        Returns:
            Configured BotorchRecommender instance.

        Raises:
            ValueError: If the recommender configuration is invalid.
        """
        # Always use BotorchRecommender
        if config is None:
            config = {}
        elif not isinstance(config, dict):
            raise ValueError(f"Recommender configuration must be a dictionary, got {type(config)}")
        else:
            config = config.copy()
            # Remove any type specification from the config
            config.pop("type", None)

        # Configure surrogate model
        surrogate_model = self._configure_surrogate_model(surrogate_config)

        # Configure acquisition function
        acquisition_function = self._configure_acquisition_function(
            acquisition_config,
            objective_type
        )

        # Set GPU-optimized defaults if using CUDA
        cuda_optimizations = {}
        if self.device.type == "cuda":
            cuda_optimizations = {
                "n_restarts": 20,
                "n_raw_samples": 128
            }

        # Combine all configurations
        recommender_kwargs = {**cuda_optimizations, **config}

        # Add surrogate model and acquisition function
        if surrogate_model is not None:
            recommender_kwargs["surrogate_model"] = surrogate_model
        if acquisition_function is not None:
            # Convert acquisition_function from dict to string
            if isinstance(acquisition_function, dict) and "type" in acquisition_function:
                recommender_kwargs["acquisition_function"] = acquisition_function["type"]

        # CHANGED: Validate common recommender parameters
        if "n_restarts" in recommender_kwargs and not isinstance(recommender_kwargs["n_restarts"], int):
            raise ValueError(f"n_restarts must be an integer, got {type(recommender_kwargs['n_restarts'])}")

        if "n_raw_samples" in recommender_kwargs and not isinstance(recommender_kwargs["n_raw_samples"], int):
            raise ValueError(f"n_raw_samples must be an integer, got {type(recommender_kwargs['n_raw_samples'])}")

        # Return BotorchRecommender with the provided configuration
        try:
            return BotorchRecommender(**recommender_kwargs)
        except Exception as e:
            raise ValueError(f"Error creating recommender: {str(e)}")

    # CHANGED: Added new method to consolidate objective creation
    def _create_objective(self,
                         objective_type: str,
                         target_config: Union[Dict[str, Any], List[Dict[str, Any]]],
                         scalarizer: Optional[str] = None,
                         weights: Optional[List[float]] = None
                        ) -> Union[SingleTargetObjective, DesirabilityObjective]:
        """Create an objective based on the specified type and target configuration.

        Args:
            objective_type: Type of objective ("SingleTarget" or "Desirability")
            target_config: Configuration for target(s)
            scalarizer: Scalarization method for multi-objective optimization
            weights: Weights for multi-objective optimization

        Returns:
            Configured objective object

        Raises:
            ValueError: If the objective type or configuration is invalid
        """
        if objective_type == "SingleTarget":
            # Handle single target case
            if isinstance(target_config, list):
                target_config = target_config[0]
                logger.warning("List of targets provided for SingleTarget objective. Using the first one.")

            target = self._parse_target(target_config)
            return SingleTargetObjective(target=target)

        elif objective_type == "Desirability":
            # Handle multi-objective case
            if not isinstance(target_config, list):
                raise ValueError("For Desirability objective, target_config must be a list of targets")

            targets = []
            target_weights = weights or []

            # Use provided weights or extract from target configs
            if not target_weights:
                target_weights = [t.get("weight", 1.0) for t in target_config]

            # Ensure we have the right number of weights
            if len(target_weights) < len(target_config):
                target_weights.extend([1.0] * (len(target_config) - len(target_weights)))
            elif len(target_weights) > len(target_config):
                target_weights = target_weights[:len(target_config)]

            # Parse targets
            for target_conf in target_config:
                targets.append(self._parse_target(target_conf))

            # Use provided scalarizer or default
            scalarizer_value = scalarizer or "GEOM_MEAN"

            # Create the DesirabilityObjective with proper error handling
            try:
                return DesirabilityObjective(
                    targets=targets,
                    weights=target_weights,
                    scalarizer=scalarizer_value
                )
            except Exception as e:
                # Provide more detailed error message
                logger.error(f"Error creating DesirabilityObjective: {str(e)}")
                logger.error(f"Targets: {targets}")
                logger.error(f"Weights: {target_weights}")
                logger.error(f"Scalarizer: {scalarizer_value}")

                # Check if the error is related to normalized computational representations
                if "normalized computational representations" in str(e):
                    raise ValueError(
                        "All targets must have normalized computational representations to enable the computation of "
                        "desirability values. This requires having appropriate target bounds and transformations in place. "
                        "Please ensure all targets have bounds and LINEAR transformation."
                    )

                # If it's a different error, re-raise with more context
                raise ValueError(f"Error creating multi-target objective: {str(e)}")
        else:
            raise ValueError(f"Unsupported objective type: {objective_type}. "
                           f"Supported types: ['SingleTarget', 'Desirability']")

    def create_optimization(
        self,
        optimizer_id: str,
        parameters: List[Dict[str, Any]],
        target_config: Union[Dict[str, Any], List[Dict[str, Any]]],
        constraints: Optional[List[Dict[str, Any]]] = None,
        recommender_config: Optional[Dict[str, Any]] = None,
        objective_type: str = "SingleTarget",
        surrogate_config: Optional[Dict[str, Any]] = None,
        acquisition_config: Optional[Dict[str, Any]] = None,
        scalarizer: Optional[str] = "GEOM_MEAN",
        weights: Optional[List[float]] = None,
        initial_sampling_strategy: str = "LHS",
        initial_samples: Optional[pd.DataFrame] = None,
        num_initial_samples: int = 0
    ) -> Dict[str, str]:
        """Create a new optimization process.

        Args:
            optimizer_id: Unique identifier for this optimization.
            parameters: List of parameter configurations (1-100 parameters supported).
            target_config: Configuration for the optimization target(s) (1-10 outputs supported).
                Can be a single dict for single-objective or a list of dicts for multi-objective.
            constraints: Optional list of constraint configurations.
            recommender_config: Optional recommender configuration (defaults to BotorchRecommender).
            objective_type: Type of objective to use. One of:
                - "SingleTarget": Single objective optimization
                - "Desirability": Multiple objectives combined with weights
            surrogate_config: Optional surrogate model configuration (defaults to GaussianProcess).
            acquisition_config: Optional acquisition function configuration (supports qEI, qPI, qUCB).
            scalarizer: Optional scalarizer for multi-objective optimization.
            weights: Optional weights for multi-objective optimization.
            initial_sampling_strategy: Strategy for initial sampling ("LHS" for Latin Hypercube Sampling or "random").
            initial_samples: Optional DataFrame with initial samples to start with.
            num_initial_samples: Number of initial samples to generate (0 means no initial samples).

        Returns:
            Dictionary with status information.

        Raises:
            ValueError: If the parameters, constraints, or objective configuration is invalid.
        """
        # CHANGED: Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        if not parameters or not isinstance(parameters, list):
            return {"status": "error", "message": "Parameters must be a non-empty list"}

        if len(parameters) > 100:
            return {"status": "error", "message": "Too many parameters, maximum supported is 100"}

        # CHANGED: Validate that optimizer_id doesn't already exist
        campaign_path = self.storage_path / f"{optimizer_id}.json"
        if campaign_path.exists():
            return {"status": "error", "message": f"Optimizer with ID '{optimizer_id}' already exists"}

        # CHANGED: Validate initial sampling strategy
        if initial_sampling_strategy not in ["LHS", "random"]:
            return {"status": "error", "message": f"Invalid sampling strategy: {initial_sampling_strategy}. "
                                                 f"Must be 'LHS' or 'random'"}

        # NEW: Acquire lock for this optimization ID to prevent concurrent creation
        self.campaign_locks[optimizer_id] = time.time()

        try:
            # 1. Parameter Parsing and Instantiation
            parsed_parameters = []
            parameter_map = {}  # For referencing parameters in constraints

            for param_config in parameters:
                try:
                    param = self._parse_parameter(param_config)
                    parsed_parameters.append(param)
                    parameter_map[param_config.get("name")] = param
                except Exception as e:
                    return {"status": "error", "message": f"Error parsing parameter: {str(e)}"}

            # 2. Constraints Processing
            # PLACEHOLDER: Log warning if constraints are provided
            if constraints:
                logger.warning("Constraints are planned for future implementation - ignoring provided constraints")
                # No need to parse or validate constraints for now

            # Constraints are not implemented yet, so we don't need to set parsed_constraints
            # This will be used in the future when constraints are implemented

            # 3. SearchSpace Creation with Structure
            try:
                searchspace = SearchSpace.from_product(
                    parameters=parsed_parameters,
                    constraints=None  # Always pass None for now
                )
            except Exception as e:
                return {"status": "error", "message": f"Error creating search space: {str(e)}"}

            # 4. Create objective
            try:
                objective = self._create_objective(
                    objective_type=objective_type,
                    target_config=target_config,
                    scalarizer=scalarizer,
                    weights=weights
                )
            except Exception as e:
                return {"status": "error", "message": f"Error creating objective: {str(e)}"}

            # 5. Configure recommender
            try:
                recommender = self._configure_recommender(
                    recommender_config,
                    objective_type,
                    surrogate_config,
                    acquisition_config
                )
            except Exception as e:
                return {"status": "error", "message": f"Error configuring recommender: {str(e)}"}

            # 6. Create campaign
            try:
                campaign = Campaign(
                    searchspace=searchspace,
                    objective=objective,
                    recommender=recommender
                )

                # 6.1 Add initial samples if provided
                if initial_samples is not None and not initial_samples.empty:
                    campaign.add_measurements(initial_samples)
                # 6.2 Generate initial samples using Latin Hypercube Sampling if requested
                elif num_initial_samples > 0:
                    logger.info(f"Generating {num_initial_samples} initial points using campaign.recommend()")
                    initial_points = campaign.recommend(batch_size=num_initial_samples)
                    campaign.add_measurements(pd.DataFrame(initial_points))
            except Exception as e:
                return {"status": "error", "message": f"Error creating campaign: {str(e)}"}

            # 7. Store campaign
            # CHANGED: Manage campaign cache size
            self._manage_campaign_cache()
            self.active_campaigns[optimizer_id] = campaign

            # Save to disk
            save_result = self.save_campaign(optimizer_id)
            if save_result.get("status") == "error":
                return save_result

            return {
                "status": "success",
                "message": "Optimization created",
                "optimizer_id": optimizer_id,
                "parameter_count": len(parsed_parameters),
                "constraint_count": 0  # Always 0 for now
            }

        except Exception as e:
            logger.error(f"Error creating optimization: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Unexpected error: {str(e)}"}
        finally:
            # Release the lock
            if optimizer_id in self.campaign_locks:
                del self.campaign_locks[optimizer_id]


    # NEW: Method to manage campaign cache size
    def _manage_campaign_cache(self):
        """Manage the active campaign cache to prevent memory issues.

        This method ensures that no more than max_cached_campaigns are kept in memory.
        Least recently used campaigns are removed from the cache.
        """
        if len(self.active_campaigns) < self.max_cached_campaigns:
            return

        # Find the least recently used campaign
        logger.debug(f"Campaign cache full ({len(self.active_campaigns)} >= {self.max_cached_campaigns})")
        candidates = list(self.active_campaigns.keys())

        # Don't remove campaigns with active locks
        candidates = [c for c in candidates if c not in self.campaign_locks]

        if candidates:
            to_remove = candidates[0]  # Default to first if no usage info
            logger.info(f"Removing campaign {to_remove} from cache")

            # Make sure any GPU tensors are properly released
            if self.device.type == "cuda" and hasattr(self.active_campaigns[to_remove], "recommender"):
                try:
                    # Check if the recommender has a 'to' method before trying to use it
                    if hasattr(self.active_campaigns[to_remove].recommender, "to"):
                        # Force move to CPU before deleting
                        self.active_campaigns[to_remove].recommender.to("cpu")
                    else:
                        # For recommenders without a 'to' method, try to release GPU memory in other ways
                        logger.info(f"Recommender does not have a 'to' method, using alternative cleanup")
                except Exception as e:
                    logger.warning(f"Error moving campaign tensors to CPU: {str(e)}")

            # Remove from cache
            del self.active_campaigns[to_remove]

            # Force GPU memory cleanup if needed
            if self.device.type == "cuda":
                try:
                    torch.cuda.empty_cache()
                except Exception as e:
                    logger.warning(f"Error clearing CUDA cache: {str(e)}")

    def suggest_next_point(
        self,
        optimizer_id: str,
        batch_size: int = 1,
        pending_experiments: Optional[pd.DataFrame] = None,
        max_batch_size: int = 100
    ) -> Dict[str, Any]:
        """Get the next suggested point(s) to evaluate.

        Args:
            optimizer_id: Identifier for the optimization.
            batch_size: Number of suggestions to return (1-100).
            pending_experiments: Optional dataframe of pending experiments.
            max_batch_size: Maximum number of suggestions to return (default: 100).

        Returns:
            Dictionary with suggested points.
        """
        # CHANGED: Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        if not isinstance(batch_size, int) or batch_size < 1:
            return {"status": "error", "message": f"Invalid batch_size: {batch_size}, must be a positive integer"}

        # CHANGED: Lock the campaign while generating suggestions
        self.campaign_locks[optimizer_id] = time.time()

        try:
            if optimizer_id not in self.active_campaigns:
                load_result = self._load_or_error(optimizer_id)
                if load_result.get("status") == "error":
                    return load_result

            campaign = self.active_campaigns[optimizer_id]

            # Limit batch size to max_batch_size
            if batch_size > max_batch_size:
                logger.warning(f"Requested batch size {batch_size} exceeds maximum of {max_batch_size}. "
                              f"Using {max_batch_size} instead.")
                batch_size = max_batch_size

            # Use GPU-accelerated optimization if available
            with torch.cuda.amp.autocast(enabled=self.device.type=="cuda"):
                suggestions = campaign.recommend(
                    batch_size=batch_size,
                    pending_experiments=pending_experiments
                )

            # CHANGED: Convert NaN and infinity values to None for JSON serialization
            suggestions_dict = suggestions.replace(
                [np.nan, np.inf, -np.inf], [None, None, None]
            ).to_dict(orient="records")

            return {
                "status": "success",
                "suggestions": suggestions_dict,
                "batch_size": batch_size
            }
        except Exception as e:
            logger.error(f"Error suggesting next point: {str(e)}", exc_info=True)
            return {"status": "error", "message": str(e)}
        finally:
            # Release the lock
            if optimizer_id in self.campaign_locks:
                del self.campaign_locks[optimizer_id]

    def add_measurement(
        self,
        optimizer_id: str,
        parameters: Dict[str, Any],
        target_value: float
    ) -> Dict[str, str]:
        """Add a new measurement to the optimizer.

        Args:
            optimizer_id: Identifier for the optimization.
            parameters: Parameter values for this measurement.
            target_value: Measured target value.

        Returns:
            Dictionary with status information.
        """
        # CHANGED: Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        if not isinstance(parameters, dict) or not parameters:
            return {"status": "error", "message": "Parameters must be a non-empty dictionary"}

        if not isinstance(target_value, (int, float)) or np.isnan(target_value) or np.isinf(target_value):
            return {"status": "error", "message": f"Invalid target_value: {target_value}, must be a finite number"}

        # CHANGED: Lock the campaign while adding measurements
        self.campaign_locks[optimizer_id] = time.time()

        try:
            if optimizer_id not in self.active_campaigns:
                load_result = self._load_or_error(optimizer_id)
                if load_result.get("status") == "error":
                    return load_result

            campaign = self.active_campaigns[optimizer_id]

            # Create measurement dataframe
            df = pd.DataFrame([parameters])

            # Get target name from objective
            if not hasattr(campaign, 'objective') or campaign.objective is None:
                return {"status": "error", "message": "No objective defined for this campaign"}

            targets = campaign.objective.targets
            if not targets:
                return {"status": "error", "message": "No targets defined in objective"}

            target_name = targets[0].name
            df[target_name] = target_value

            # Add measurement and save
            campaign.add_measurements(df)
            save_result = self.save_campaign(optimizer_id)
            if save_result.get("status") == "error":
                return save_result

            return {"status": "success", "message": "Measurement added"}
        except Exception as e:
            logger.error(f"Error adding measurement: {str(e)}", exc_info=True)
            return {"status": "error", "message": str(e)}
        finally:
            # Release the lock
            if optimizer_id in self.campaign_locks:
                del self.campaign_locks[optimizer_id]

    def add_multi_target_measurement(
        self,
        optimizer_id: str,
        parameters: Dict[str, Any],
        target_values: Dict[str, float]
    ) -> Dict[str, str]:
        """Add a new multi-target measurement to the optimizer.

        Args:
            optimizer_id: Identifier for the optimization.
            parameters: Parameter values for this measurement.
            target_values: Dictionary mapping target names to measured values.

        Returns:
            Dictionary with status information.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        if not isinstance(parameters, dict) or not parameters:
            return {"status": "error", "message": "Parameters must be a non-empty dictionary"}

        if not isinstance(target_values, dict) or not target_values:
            return {"status": "error", "message": "Target values must be a non-empty dictionary"}

        # Validate all target values
        for target_name, value in target_values.items():
            if not isinstance(value, (int, float)) or np.isnan(value) or np.isinf(value):
                return {"status": "error", "message": f"Invalid target value for {target_name}: {value}, must be a finite number"}

        # Lock the campaign while adding measurements
        self.campaign_locks[optimizer_id] = time.time()

        try:
            if optimizer_id not in self.active_campaigns:
                load_result = self._load_or_error(optimizer_id)
                if load_result.get("status") == "error":
                    return load_result

            campaign = self.active_campaigns[optimizer_id]

            # Create measurement dataframe
            df = pd.DataFrame([parameters])

            # Get targets from objective
            if not hasattr(campaign, 'objective') or campaign.objective is None:
                return {"status": "error", "message": "No objective defined for this campaign"}

            targets = campaign.objective.targets
            if not targets:
                return {"status": "error", "message": "No targets defined in objective"}

            # Validate that the target names exist in the campaign
            target_names = [target.name for target in targets]

            # For multi-target optimization, we need to check if all required targets are provided
            if len(targets) > 1:
                # This is a multi-target optimization
                missing_targets = [name for name in target_names if name not in target_values]
                if missing_targets:
                    return {"status": "error", "message": f"Missing target values for: {', '.join(missing_targets)}"}

            # Check if any provided targets don't exist in the campaign
            unknown_targets = [name for name in target_values.keys() if name not in target_names]
            if unknown_targets:
                return {"status": "error", "message": f"Unknown targets: {', '.join(unknown_targets)}. Valid targets are: {', '.join(target_names)}"}

            # Add all target values to the dataframe
            for target_name, value in target_values.items():
                df[target_name] = value

            # Add measurement and save
            campaign.add_measurements(df)
            save_result = self.save_campaign(optimizer_id)
            if save_result.get("status") == "error":
                return save_result

            return {"status": "success", "message": "Multi-target measurement added"}
        except Exception as e:
            logger.error(f"Error adding multi-target measurement: {str(e)}", exc_info=True)
            return {"status": "error", "message": str(e)}
        finally:
            # Release the lock
            if optimizer_id in self.campaign_locks:
                del self.campaign_locks[optimizer_id]

    def add_multiple_measurements(
        self,
        optimizer_id: str,
        measurements: pd.DataFrame
    ) -> Dict[str, str]:
        """Add multiple measurements at once.

        Args:
            optimizer_id: Identifier for the optimization.
            measurements: Dataframe with parameter values and target values.

        Returns:
            Dictionary with status information.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        if not isinstance(measurements, pd.DataFrame) or measurements.empty:
            return {"status": "error", "message": "Measurements must be a non-empty pandas DataFrame"}

        # Lock the campaign while adding measurements
        self.campaign_locks[optimizer_id] = time.time()

        try:
            if optimizer_id not in self.active_campaigns:
                load_result = self._load_or_error(optimizer_id)
                if load_result.get("status") == "error":
                    return load_result

            campaign = self.active_campaigns[optimizer_id]

            # Get all target names from the campaign
            targets = campaign.objective.targets
            if not targets:
                return {"status": "error", "message": "No targets defined in objective"}

            target_names = [target.name for target in targets]

            # Handle special case for backward compatibility
            if "_target" in measurements.columns:
                # This is a single-target measurement with a placeholder
                # We need to replace the placeholder with the actual target name
                logger.info(f"Found placeholder '_target' column, replacing with actual target name: {target_names[0]}")
                measurements = measurements.rename(columns={"_target": target_names[0]})

            # For multi-target optimization, check if all required targets exist
            if len(target_names) > 1:
                # This is a multi-target optimization
                missing_targets = [name for name in target_names if name not in measurements.columns]
                if missing_targets:
                    return {
                        "status": "error",
                        "message": f"Missing target columns: {', '.join(missing_targets)}. Required targets: {', '.join(target_names)}"
                    }
                logger.info(f"Found all required target columns for multi-target optimization: {target_names}")
            else:
                # For single-target, the target column must exist
                target_name = target_names[0]
                if target_name not in measurements.columns:
                    return {
                        "status": "error",
                        "message": f"Target column '{target_name}' not found in measurements dataframe"
                    }

            # Check for NaN or infinite values
            if measurements.isna().any().any() or np.isinf(measurements).any().any():
                # Replace problematic values with None
                measurements = measurements.replace([np.nan, np.inf, -np.inf], None)
                logger.warning("NaN or infinite values in measurements replaced with None")

            # Add measurements and save
            campaign.add_measurements(measurements)
            save_result = self.save_campaign(optimizer_id)
            if save_result.get("status") == "error":
                return save_result

            return {"status": "success", "message": f"{len(measurements)} measurements added"}
        except Exception as e:
            logger.error(f"Error adding multiple measurements: {str(e)}", exc_info=True)
            return {"status": "error", "message": str(e)}
        finally:
            # Release the lock
            if optimizer_id in self.campaign_locks:
                del self.campaign_locks[optimizer_id]

    # CHANGED: Single, consolidated get_best_point method
    def get_best_point(self, optimizer_id: str) -> Dict[str, Any]:
        """Get the current best point for an optimization.

        Args:
            optimizer_id: Identifier for the optimization.

        Returns:
            Dictionary with best point information.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        try:
            if optimizer_id not in self.active_campaigns:
                load_result = self._load_or_error(optimizer_id)
                if load_result.get("status") == "error":
                    return {"status": "error", "message": load_result["message"]}

            campaign = self.active_campaigns[optimizer_id]

            # Check if there are any measurements
            if not hasattr(campaign, 'measurements') or campaign.measurements is None:
                return {
                    "status": "success",
                    "message": "No measurements available"
                }

            # Check if measurements is a DataFrame with the expected methods
            if not hasattr(campaign.measurements, 'empty'):
                return {
                    "status": "success",
                    "message": "Measurements not in expected format"
                }

            if campaign.measurements.empty:
                return {
                    "status": "success",
                    "message": "No measurements yet"
                }

            # Get objective and target information
            if not hasattr(campaign, 'objective') or campaign.objective is None:
                return {
                    "status": "success",
                    "message": "No objective defined"
                }

            targets = campaign.objective.targets
            if not targets:
                return {
                    "status": "success",
                    "message": "No targets defined in objective"
                }

            target = targets[0]
            target_name = target.name

            # Always calculate normalized values and composite score for all optimizations
            logger.info(f"Calculating composite score for {len(targets)} targets")

            # Create a copy of the measurements dataframe for calculations
            calc_df = campaign.measurements.copy()

            # Calculate normalized values and composite score
            composite_scores = []

            # Default weights - equal weighting for all targets
            weights = {t.name: 1.0 / len(targets) for t in targets}

            # Check if custom weights are defined in the objective
            if hasattr(campaign.objective, 'weights') and campaign.objective.weights:
                # Handle both dictionary and tuple/list weights
                if isinstance(campaign.objective.weights, dict):
                    weights = campaign.objective.weights
                elif isinstance(campaign.objective.weights, (tuple, list)):
                    # Convert tuple/list to dictionary using target names as keys
                    if len(campaign.objective.weights) == len(targets):
                        weights = {t.name: w for t, w in zip(targets, campaign.objective.weights)}
                    else:
                        logger.warning(f"Number of weights ({len(campaign.objective.weights)}) doesn't match number of targets ({len(targets)}). Using equal weights.")
                        weights = {t.name: 1.0 / len(targets) for t in targets}
                else:
                    logger.warning(f"Unexpected weights type: {type(campaign.objective.weights)}. Using equal weights.")

                logger.info(f"Using custom weights: {weights}")

            # Calculate normalized values for each row
            normalized_values_by_row = []
            for idx, row in calc_df.iterrows():
                row_score = 0.0
                row_normalized_values = {}

                for t in targets:
                    # Skip if target value is missing
                    if t.name not in row:
                        continue

                    # Get min and max values for normalization
                    t_min = calc_df[t.name].min()
                    t_max = calc_df[t.name].max()

                    # Avoid division by zero
                    if t_max == t_min:
                        normalized = 1.0 if row[t.name] == t_max else 0.0
                    else:
                        normalized = (row[t.name] - t_min) / (t_max - t_min)

                    # Invert normalization for MIN targets (1 - normalized)
                    if t.mode == "MIN":
                        normalized = 1.0 - normalized

                    # Store normalized value for this target
                    row_normalized_values[t.name] = normalized

                    # Apply weight and add to composite score
                    if isinstance(weights, dict):
                        weight = weights.get(t.name, 1.0 / len(targets))
                    elif isinstance(weights, (tuple, list)) and len(weights) == len(targets):
                        # Find the index of this target in the targets list
                        try:
                            target_index = next(i for i, target in enumerate(targets) if target.name == t.name)
                            weight = weights[target_index]
                        except StopIteration:
                            weight = 1.0 / len(targets)
                    else:
                        weight = 1.0 / len(targets)
                    row_score += weight * normalized

                composite_scores.append(row_score)
                normalized_values_by_row.append(row_normalized_values)

            # Add composite scores to the dataframe
            calc_df['composite_score'] = composite_scores

            # Store normalized values for each row
            for i, norm_vals in enumerate(normalized_values_by_row):
                for target_name, norm_val in norm_vals.items():
                    col_name = f"normalized_{target_name}"
                    if col_name not in calc_df.columns:
                        calc_df[col_name] = np.nan
                    calc_df.at[i, col_name] = norm_val

            # Determine best index based on optimization type
            if len(targets) > 1:
                # Multi-target: Get the best index based on composite score (always maximize)
                best_idx = calc_df['composite_score'].idxmax()
                logger.info(f"Multi-target optimization: Best composite score: {calc_df.loc[best_idx]['composite_score']}")
            else:
                # Single target: Use the target's mode
                if target.mode == "MAX":
                    best_idx = campaign.measurements[target_name].idxmax()
                else:  # MIN
                    best_idx = campaign.measurements[target_name].idxmin()

                # For single target, we still want to report the "composite score" which is just the normalized value
                logger.info(f"Single-target optimization: Best {target.mode} value: {campaign.measurements.loc[best_idx][target_name]}")

            best_row = campaign.measurements.loc[best_idx]

            # Extract parameter values and target value
            parameter_names = [p.name for p in campaign.searchspace.parameters]
            best_parameters = {name: best_row[name] for name in parameter_names if name in best_row}

            # Convert numpy types to Python primitives for JSON serialization
            for name, value in best_parameters.items():
                if isinstance(value, np.integer):
                    best_parameters[name] = int(value)
                elif isinstance(value, np.floating):
                    best_parameters[name] = float(value)
                elif isinstance(value, np.ndarray):
                    best_parameters[name] = value.tolist()

            # Convert target value to native Python float
            best_value = float(best_row[target_name])

            # Include total measurement count
            total_measurements = len(campaign.measurements)

            # Create the response dictionary
            # Convert target.mode to a clean string format (MAX or MIN)
            # Use a more robust approach to extract just MAX or MIN
            target_mode_str = str(target.mode)
            if "MAX" in target_mode_str.upper():
                target_mode_str = "MAX"
            elif "MIN" in target_mode_str.upper():
                target_mode_str = "MIN"
            else:
                target_mode_str = "MAX"  # Default to MAX if we can't determine

            logger.info(f"Target mode for {target_name}: {target_mode_str} (original: {target.mode})")

            response = {
                "status": "success",
                "best_value": best_value,
                "best_parameters": best_parameters,
                "total_measurements": total_measurements,
                "target_name": target_name,
                "target_mode": target_mode_str
            }

            # Always calculate and include composite score, normalized values, and target weights
            # Even if calc_df doesn't have composite_score column, we'll calculate it here
            logger.info(f"Calculating composite score for best point")

            # Calculate normalized values and weights
            normalized_values = {}
            target_weights = {}
            composite_score = 0.0
            total_weight = 0.0

            for t in targets:
                if t.name in best_row:
                    # Calculate normalized value
                    t_min = campaign.measurements[t.name].min()
                    t_max = campaign.measurements[t.name].max()

                    if t_max == t_min:
                        normalized = 1.0 if best_row[t.name] == t_max else 0.0
                    else:
                        normalized = (best_row[t.name] - t_min) / (t_max - t_min)

                    # Invert for MIN targets
                    # Convert t.mode to string and check if it contains "MIN"
                    t_mode_str = str(t.mode).upper()
                    if "MIN" in t_mode_str:
                        normalized = 1.0 - normalized
                        logger.info(f"Inverting normalized value for MIN target {t.name}")

                    # Get weight for this target
                    if isinstance(weights, dict):
                        weight = weights.get(t.name, 1.0 / len(targets))
                    elif isinstance(weights, (tuple, list)) and len(weights) == len(targets):
                        # Find the index of this target in the targets list
                        try:
                            target_index = next(i for i, target in enumerate(targets) if target.name == t.name)
                            weight = weights[target_index]
                        except StopIteration:
                            weight = 1.0 / len(targets)
                    else:
                        weight = 1.0 / len(targets)

                    # Add to composite score
                    composite_score += normalized * weight
                    total_weight += weight

                    # Store normalized value and weight
                    normalized_values[t.name] = float(normalized)
                    target_weights[t.name] = float(weight)

                    logger.info(f"Target {t.name}: value={best_row[t.name]}, normalized={normalized}, weight={weight}")

            # Normalize composite score by total weight
            if total_weight > 0:
                composite_score = composite_score / total_weight

            # Add to response
            response["composite_score"] = float(composite_score)
            response["normalized_values"] = normalized_values
            response["target_weights"] = target_weights

            logger.info(f"Composite score: {composite_score}")
            logger.info(f"Added to response - normalized_values: {normalized_values}, target_weights: {target_weights}")

            # For multi-target optimization, include all target values
            if len(targets) > 1:
                # Get all target values for the best point
                best_values = {}
                for t in targets:
                    if t.name in best_row:
                        best_values[t.name] = float(best_row[t.name])

                # Add best_values to the response
                if best_values:
                    response["best_values"] = best_values
                    logger.info(f"Including multiple target values in best point: {best_values}")

            # Sanitize the response for JSON serialization
            sanitized_response = sanitize_for_json(response)

            return sanitized_response
        except Exception as e:
            logger.error(f"Error getting best point: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Error getting best point: {str(e)}"}

    # CHANGED: Improved measurement history method
    def get_measurement_history(self, optimizer_id: str) -> Dict[str, Any]:
        """Get the measurement history for an optimization.

        Args:
            optimizer_id: Identifier for the optimization.

        Returns:
            Dictionary with measurement history.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        try:
            if optimizer_id not in self.active_campaigns:
                load_result = self._load_or_error(optimizer_id)
                if load_result.get("status") == "error":
                    return {"status": "error", "message": load_result["message"]}

            campaign = self.active_campaigns[optimizer_id]

            # Check if measurements attribute exists and is accessible
            if not hasattr(campaign, 'measurements'):
                logger.warning(f"Campaign has no measurements attribute: {optimizer_id}")
                return {
                    "status": "success",
                    "message": "No measurements available",
                    "measurements": []
                }

            # Check if measurements is a DataFrame with the expected methods
            if not hasattr(campaign.measurements, 'empty'):
                logger.warning(f"Campaign measurements is not a DataFrame: {optimizer_id}")
                return {
                    "status": "success",
                    "message": "Measurements not in expected format",
                    "measurements": []
                }

            if campaign.measurements.empty:
                return {
                    "status": "success",
                    "message": "No measurements yet",
                    "measurements": []
                }

            # Safely convert to dict, handling problematic values
            try:
                # Replace NaN, Infinity, and -Infinity with None for JSON serialization
                measurements_df = campaign.measurements.copy()
                measurements_df = measurements_df.replace([np.nan, np.inf, -np.inf], None)

                # Convert numpy types to Python primitives
                for col in measurements_df.columns:
                    if measurements_df[col].dtype.kind in 'iuf':  # integer, unsigned int, or float
                        measurements_df[col] = measurements_df[col].astype(float)

                measurements_dict = measurements_df.to_dict(orient="records")

                # Additional metadata
                targets = campaign.objective.targets if hasattr(campaign, 'objective') else []
                target_info = []

                for target in targets:
                    # Convert target.mode to a clean string format (MAX or MIN)
                    target_mode_str = str(target.mode)
                    if "MAX" in target_mode_str.upper():
                        target_mode_str = "MAX"
                    elif "MIN" in target_mode_str.upper():
                        target_mode_str = "MIN"
                    else:
                        target_mode_str = "MAX"  # Default to MAX if we can't determine

                    target_info.append({
                        "name": target.name,
                        "mode": target_mode_str
                    })

                # Create and sanitize the response dictionary
                response = sanitize_for_json({
                    "status": "success",
                    "measurements": measurements_dict,
                    "target_info": target_info,
                    "count": len(measurements_df)
                })

                return response
            except Exception as e:
                logger.error(f"Error converting measurements to dict: {str(e)}", exc_info=True)
                # Return empty measurements instead of failing
                return {
                    "status": "success",
                    "message": f"Error formatting measurements: {str(e)}",
                    "measurements": []
                }
        except Exception as e:
            logger.error(f"Unexpected error in get_measurement_history: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Unexpected error: {str(e)}"}

    def get_campaign_info(self, optimizer_id: str) -> Dict[str, Any]:
        """Get information about a campaign.

        Args:
            optimizer_id: Identifier for the optimization.

        Returns:
            Dictionary with campaign information.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        try:
            if optimizer_id not in self.active_campaigns:
                load_result = self._load_or_error(optimizer_id)
                if load_result.get("status") == "error":
                    return {"status": "error", "message": load_result["message"]}

            campaign = self.active_campaigns[optimizer_id]

            # Collect campaign information
            info = {
                "parameters": [],
                "targets": [],
                "measurements_count": len(campaign.measurements) if hasattr(campaign, "measurements") else 0,
                "objective_type": "unknown"
            }

            # Add parameter information
            for param in campaign.searchspace.parameters:
                param_info = {
                    "name": param.name,
                    "type": param.__class__.__name__
                }

                # Add values or bounds depending on parameter type
                if hasattr(param, "values") and param.values is not None:
                    param_info["values"] = param.values.tolist() if hasattr(param.values, 'tolist') else param.values
                elif hasattr(param, "bounds") and param.bounds is not None:
                    param_info["bounds"] = [param.bounds.lower, param.bounds.upper]

                info["parameters"].append(param_info)

            # Add target and objective information
            if hasattr(campaign, 'objective') and campaign.objective is not None:
                # Determine objective type
                if isinstance(campaign.objective, SingleTargetObjective):
                    info["objective_type"] = "SingleTarget"
                elif isinstance(campaign.objective, DesirabilityObjective):
                    info["objective_type"] = "Desirability"
                    # Include scalarizer if available
                    if hasattr(campaign.objective, "scalarizer"):
                        info["scalarizer"] = str(campaign.objective.scalarizer)

                # Add target information
                targets = campaign.objective.targets
                for target in targets:
                    # Convert target.mode to a clean string format (MAX or MIN)
                    target_mode_str = str(target.mode)
                    if "MAX" in target_mode_str.upper():
                        target_mode_str = "MAX"
                    elif "MIN" in target_mode_str.upper():
                        target_mode_str = "MIN"
                    else:
                        target_mode_str = "MAX"  # Default to MAX if we can't determine

                    target_info = {
                        "name": target.name,
                        "mode": target_mode_str
                    }

                    if hasattr(target, "bounds") and target.bounds is not None:
                        target_info["bounds"] = [target.bounds.lower, target.bounds.upper]

                    info["targets"].append(target_info)

            # Add recommender information if available
            if hasattr(campaign, 'recommender') and campaign.recommender is not None:
                recommender = campaign.recommender
                info["recommender"] = {
                    "type": recommender.__class__.__name__
                }

                # Add acquisition function info if available
                if hasattr(recommender, "acquisition_function"):
                    acq_func = recommender.acquisition_function
                    if acq_func and hasattr(acq_func, "type"):
                        info["recommender"]["acquisition_function"] = acq_func.type

            # Sanitize the entire response for JSON serialization
            return sanitize_for_json({
                "status": "success",
                "info": info
            })
        except Exception as e:
            logger.error(f"Error getting campaign info: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Error getting campaign info: {str(e)}"}

    def initialize_from_data(
        self,
        optimizer_id: str,
        parameters: List[Dict[str, Any]],
        targets: List[float]
    ) -> Dict[str, Any]:
        """Initialize a campaign with existing data.

        Args:
            optimizer_id: Identifier for the optimization.
            parameters: List of parameter dictionaries.
            targets: List of target values.

        Returns:
            Dictionary with status information.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        if not isinstance(parameters, list) or not parameters:
            return {"status": "error", "message": "Parameters must be a non-empty list of dictionaries"}

        if not isinstance(targets, list) or not targets:
            return {"status": "error", "message": "Targets must be a non-empty list of values"}

        if len(parameters) != len(targets):
            return {
                "status": "error",
                "message": f"Parameters list length ({len(parameters)}) must match targets list length ({len(targets)})"
            }

        # CHANGED: Lock the campaign while initializing data
        self.campaign_locks[optimizer_id] = time.time()

        try:
            if optimizer_id not in self.active_campaigns:
                load_result = self._load_or_error(optimizer_id)
                if load_result.get("status") == "error":
                    return load_result

            campaign = self.active_campaigns[optimizer_id]

            # Create a DataFrame from the parameters and targets
            try:
                df = pd.DataFrame(parameters)
            except Exception as e:
                return {"status": "error", "message": f"Error creating DataFrame from parameters: {str(e)}"}

            # Get target name from objective
            if not hasattr(campaign, 'objective') or campaign.objective is None:
                return {"status": "error", "message": "No objective defined for this campaign"}

            targets_obj = campaign.objective.targets
            if not targets_obj:
                return {"status": "error", "message": "No targets defined in objective"}

            target_name = targets_obj[0].name

            # Validate targets
            invalid_targets = [i for i, t in enumerate(targets) if not isinstance(t, (int, float)) or np.isnan(t) or np.isinf(t)]
            if invalid_targets:
                return {
                    "status": "error",
                    "message": f"Invalid target values at indices: {invalid_targets}. All targets must be finite numbers."
                }

            df[target_name] = targets

            # Add measurements to the campaign and save
            campaign.add_measurements(df)
            save_result = self.save_campaign(optimizer_id)
            if save_result.get("status") == "error":
                return save_result

            return {"status": "success", "message": f"{len(df)} measurements added"}
        except Exception as e:
            logger.error(f"Error initializing from data: {str(e)}", exc_info=True)
            return {"status": "error", "message": str(e)}
        finally:
            # Release the lock
            if optimizer_id in self.campaign_locks:
                del self.campaign_locks[optimizer_id]

    # CHANGED: Improved atomic file saving with error handling
    def save_campaign(self, optimizer_id: str) -> Dict[str, str]:
        """Save the campaign state to disk with comprehensive error handling.

        Args:
            optimizer_id: Identifier for the optimization.

        Returns:
            Dictionary with status information and error details if applicable.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        if optimizer_id not in self.active_campaigns:
            return {"status": "error", "message": f"Campaign {optimizer_id} not found in active campaigns"}

        # CHANGED: Lock the campaign while saving
        self.campaign_locks[optimizer_id] = time.time()

        try:
            campaign = self.active_campaigns[optimizer_id]
            file_path = self.storage_path / f"{optimizer_id}.json"

            # If using GPU, temporarily move to CPU for serialization to avoid CUDA tensor issues
            if self.device.type == "cuda":
                try:
                    torch.cuda.empty_cache()
                except Exception as e:
                    logger.warning(f"Could not empty CUDA cache: {str(e)}")

            # Convert campaign to JSON
            try:
                config_json = campaign.to_json()
            except Exception as e:
                return {"status": "error", "message": f"Error serializing campaign: {str(e)}"}

            # CHANGED: Use atomic file writing to prevent corruption
            try:
                # Create a temporary file in the same directory
                temp_dir = self.storage_path
                with tempfile.NamedTemporaryFile(mode='w', dir=temp_dir, delete=False) as temp_file:
                    temp_path = Path(temp_file.name)
                    temp_file.write(config_json)
                    # Ensure the file is flushed to disk before closing
                    temp_file.flush()
                    os.fsync(temp_file.fileno())

                # Rename the temporary file to the target file (atomic operation)
                os.replace(temp_path, file_path)

                return {"status": "success", "message": "Campaign saved successfully"}
            except Exception as e:
                # Try to clean up the temporary file if it still exists
                try:
                    if temp_path.exists():
                        temp_path.unlink()
                except:
                    pass

                return {"status": "error", "message": f"Error writing campaign to disk: {str(e)}"}
        except Exception as e:
            logger.error(f"Error saving campaign: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Unexpected error: {str(e)}"}
        finally:
            # Release the lock
            if optimizer_id in self.campaign_locks:
                del self.campaign_locks[optimizer_id]

    def _load_or_error(self, optimizer_id: str) -> Dict[str, str]:
        """Load a campaign or return an error message.

        Args:
            optimizer_id: Identifier for the optimization.

        Returns:
            Dictionary with status information.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        # First, check if the campaign exists on disk
        file_path = self.storage_path / f"{optimizer_id}.json"
        if not file_path.exists():
            return {"status": "error", "message": f"Optimizer {optimizer_id} not found"}

        # CHANGED: Acquire lock while loading
        self.campaign_locks[optimizer_id] = time.time()

        try:
            # Try to load the campaign
            load_result = self.load_campaign(optimizer_id)
            if load_result["status"] == "error":
                return load_result
            return {"status": "success", "message": f"Optimizer {optimizer_id} loaded successfully"}
        except Exception as e:
            logger.error(f"Error loading optimizer: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Error loading optimizer: {str(e)}"}
        finally:
            # Release the lock
            if optimizer_id in self.campaign_locks:
                del self.campaign_locks[optimizer_id]

    def get_optimizer(self, optimizer_id: str):
        """Get an optimizer by ID.

        Args:
            optimizer_id: Identifier for the optimization.

        Returns:
            The campaign object for the specified optimizer ID.

        Raises:
            HTTPException: If the optimizer is not found or cannot be loaded.
        """
        # Check if the campaign is already loaded
        if optimizer_id in self.active_campaigns:
            return self.active_campaigns[optimizer_id]

        # Try to load the campaign
        file_path = self.storage_path / f"{optimizer_id}.json"

        if not file_path.exists():
            from fastapi import HTTPException
            raise HTTPException(status_code=404, detail=f"Optimizer not found: {optimizer_id}")

        # Check file integrity
        if file_path.stat().st_size == 0:
            from fastapi import HTTPException
            raise HTTPException(status_code=500, detail=f"Campaign file for {optimizer_id} is empty")

        try:
            # Read the JSON file
            with open(file_path, 'r') as f:
                config_json = f.read()

            # Create campaign from JSON
            from baybe import Campaign
            campaign = Campaign.from_json(config_json)

            # Manage campaign cache before adding new campaign
            self._manage_campaign_cache()

            # Store in active campaigns
            self.active_campaigns[optimizer_id] = campaign

            return campaign
        except Exception as e:
            logger.error(f"Error loading optimizer: {str(e)}", exc_info=True)
            from fastapi import HTTPException
            raise HTTPException(status_code=500, detail=f"Error loading optimizer: {str(e)}")

    def load_campaign(self, optimizer_id: str) -> Dict[str, str]:
        """Load a campaign from disk.

        Args:
            optimizer_id: Identifier for the optimization.

        Returns:
            Dictionary with status information.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        try:
            file_path = self.storage_path / f"{optimizer_id}.json"

            if not file_path.exists():
                return {"status": "error", "message": f"Optimizer not found: {optimizer_id}"}

            # CHANGED: Add file integrity check
            if file_path.stat().st_size == 0:
                return {"status": "error", "message": f"Campaign file for {optimizer_id} is empty"}

            # Read the JSON file
            try:
                with open(file_path, 'r') as f:
                    config_json = f.read()
            except Exception as e:
                return {"status": "error", "message": f"Error reading campaign file: {str(e)}"}

            # Create campaign from JSON
            try:
                from baybe import Campaign
                campaign = Campaign.from_json(config_json)
            except Exception as e:
                return {"status": "error", "message": f"Error parsing campaign JSON: {str(e)}"}

            # CHANGED: Manage campaign cache before adding new campaign
            self._manage_campaign_cache()

            # Store in active campaigns
            self.active_campaigns[optimizer_id] = campaign

            return {"status": "success", "message": f"Optimizer {optimizer_id} loaded successfully"}
        except Exception as e:
            logger.error(f"Error loading campaign: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Error loading optimizer: {str(e)}"}

    def list_optimizations(self) -> Dict[str, Any]:
        """List all available optimizations.

        Returns:
            Dictionary with list of optimizations.
        """
        try:
            # Get all JSON files in the storage directory
            json_files = list(self.storage_path.glob("*.json"))

            # Extract optimizer IDs from filenames
            optimizer_ids = [file.stem for file in json_files]

            # Get information for each optimizer
            optimizers = []

            for optimizer_id in optimizer_ids:
                # Try to load basic information without loading the entire campaign
                try:
                    file_path = self.storage_path / f"{optimizer_id}.json"

                    # CHANGED: Check file size before attempting to read
                    if file_path.stat().st_size == 0:
                        logger.warning(f"Skipping empty campaign file: {file_path}")
                        continue

                    # CHANGED: Use safer file reading approach
                    try:
                        with open(file_path, 'r') as f:
                            # Read first few KB to extract basic info
                            first_chunk = f.read(8192)  # 8KB should be enough for headers
                    except Exception as e:
                        logger.warning(f"Error reading file {file_path}: {str(e)}")
                        continue

                    # Check if it's a valid campaign file
                    if "searchspace" in first_chunk and "objective" in first_chunk:
                        # Add basic info
                        optimizer_info = {
                            "id": optimizer_id,
                            "file_path": str(file_path),
                            "file_size": file_path.stat().st_size,
                            "last_modified": file_path.stat().st_mtime
                        }

                        # Try to extract more metadata if available
                        try:
                            if "\"objective\":" in first_chunk:
                                if "SingleTargetObjective" in first_chunk:
                                    optimizer_info["objective_type"] = "SingleTarget"
                                elif "DesirabilityObjective" in first_chunk:
                                    optimizer_info["objective_type"] = "Desirability"
                        except:
                            pass

                        optimizers.append(optimizer_info)
                except Exception as e:
                    # Skip files that can't be parsed
                    logger.warning(f"Could not parse file {file_path}: {str(e)}")
                    continue

            return {
                "status": "success",
                "optimizers": optimizers,
                "count": len(optimizers)
            }
        except Exception as e:
            logger.error(f"Error listing optimizations: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Error listing optimizations: {str(e)}"}

    # NEW: Method to delete an optimization
    def delete_optimization(self, optimizer_id: str) -> Dict[str, str]:
        """Delete an optimization campaign.

        Args:
            optimizer_id: Identifier for the optimization.

        Returns:
            Dictionary with status information.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        # Check if the campaign exists
        file_path = self.storage_path / f"{optimizer_id}.json"
        if not file_path.exists():
            return {"status": "error", "message": f"Optimizer {optimizer_id} not found"}

        # CHANGED: Acquire lock while deleting
        self.campaign_locks[optimizer_id] = time.time()

        try:
            # Remove from active campaigns if loaded
            if optimizer_id in self.active_campaigns:
                # Release GPU resources if necessary
                if self.device.type == "cuda" and hasattr(self.active_campaigns[optimizer_id], "recommender"):
                    try:
                        # Check if the recommender has a 'to' method before trying to use it
                        if hasattr(self.active_campaigns[optimizer_id].recommender, "to"):
                            # Force move to CPU before deleting
                            self.active_campaigns[optimizer_id].recommender.to("cpu")
                        else:
                            # For recommenders without a 'to' method, try to release GPU memory in other ways
                            logger.info(f"Recommender does not have a 'to' method, using alternative cleanup")
                    except Exception as e:
                        logger.warning(f"Error moving campaign tensors to CPU: {str(e)}")

                # Remove from cache
                del self.active_campaigns[optimizer_id]

                # Force GPU memory cleanup if needed
                if self.device.type == "cuda":
                    try:
                        torch.cuda.empty_cache()
                    except Exception as e:
                        logger.warning(f"Error clearing CUDA cache: {str(e)}")

            # Delete the file
            try:
                file_path.unlink()
                return {"status": "success", "message": f"Optimizer {optimizer_id} deleted successfully"}
            except Exception as e:
                return {"status": "error", "message": f"Error deleting optimizer file: {str(e)}"}
        except Exception as e:
            logger.error(f"Error deleting optimization: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Error deleting optimization: {str(e)}"}
        finally:
            # Release the lock
            if optimizer_id in self.campaign_locks:
                del self.campaign_locks[optimizer_id]

    # NEW: Method to unload a campaign from memory
    def unload_campaign(self, optimizer_id: str) -> Dict[str, str]:
        """Unload a campaign from memory to free up resources.

        Args:
            optimizer_id: Identifier for the optimization.

        Returns:
            Dictionary with status information.
        """
        # Input validation
        if not optimizer_id or not isinstance(optimizer_id, str):
            return {"status": "error", "message": "Invalid optimizer_id, must be a non-empty string"}

        if optimizer_id not in self.active_campaigns:
            return {"status": "success", "message": f"Optimizer {optimizer_id} is not loaded"}

        # CHANGED: Acquire lock while unloading
        self.campaign_locks[optimizer_id] = time.time()

        try:
            # Release GPU resources if necessary
            if self.device.type == "cuda" and hasattr(self.active_campaigns[optimizer_id], "recommender"):
                try:
                    # Check if the recommender has a 'to' method before trying to use it
                    if hasattr(self.active_campaigns[optimizer_id].recommender, "to"):
                        # Force move to CPU before deleting
                        self.active_campaigns[optimizer_id].recommender.to("cpu")
                    else:
                        # For recommenders without a 'to' method, try to release GPU memory in other ways
                        logger.info(f"Recommender does not have a 'to' method, using alternative cleanup")
                except Exception as e:
                    logger.warning(f"Error moving campaign tensors to CPU: {str(e)}")

            # Remove from cache
            del self.active_campaigns[optimizer_id]

            # Force GPU memory cleanup if needed
            if self.device.type == "cuda":
                try:
                    torch.cuda.empty_cache()
                except Exception as e:
                    logger.warning(f"Error clearing CUDA cache: {str(e)}")

            return {"status": "success", "message": f"Optimizer {optimizer_id} unloaded successfully"}
        except Exception as e:
            logger.error(f"Error unloading campaign: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Error unloading campaign: {str(e)}"}
        finally:
            # Release the lock
            if optimizer_id in self.campaign_locks:
                del self.campaign_locks[optimizer_id]