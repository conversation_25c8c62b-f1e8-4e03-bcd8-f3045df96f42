@router.get("/optimizations/{optimizer_id}/parameter-info")
async def get_parameter_info(
    optimizer_id: str,
    backend: BayesianOptimizationBackend = Depends(get_backend)
) -> Dict:
    """Get information about parameters and targets for an optimization."""
    campaign = backend.load_campaign(optimizer_id)
    if campaign is None:
        raise HTTPException(
            status_code=404,
            detail=f"Optimization {optimizer_id} not found"
        )
    
    return {
        "parameters": [
            {
                "name": param.name,
                "type": param.type.__name__,
                "bounds": param.bounds if hasattr(param, 'bounds') else None,
                "categories": param.categories if hasattr(param, 'categories') else None
            }
            for param in campaign.parameters
        ],
        "targets": [
            {
                "name": target.name,
                "type": target.type.__name__ if hasattr(target, 'type') else None
            }
            for target in campaign.targets
        ]
    }