"""Utility functions for the BayBE API."""

import json
import math
from typing import Any, Dict, List, Optional, Union

import numpy as np
from fastapi.responses import JSONResponse


class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles NaN, Infinity, and other special values."""

    def default(self, obj: Any) -> Any:
        """Handle special values like NaN, Infinity, and objects with __str__ methods."""
        if isinstance(obj, (float, np.float32, np.float64)):
            if math.isnan(obj):
                return None
            if math.isinf(obj):
                return str(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int32, np.int64)):
            return int(obj)
        elif hasattr(obj, "__str__") and not isinstance(obj, (dict, list, str, int, float, bool, type(None))):
            return str(obj)
        return super().default(obj)


class SafeJSONResponse(JSONResponse):
    """A JSONResponse that handles NaN, Infinity, and other special values."""

    def render(self, content: Any) -> bytes:
        """Render the content as JSON bytes."""
        # First sanitize the content
        sanitized_content = sanitize_for_json(content)

        # Then use the standard JSON encoder
        return json.dumps(
            sanitized_content,
            ensure_ascii=False,
            allow_nan=True,  # We've already sanitized the content
        ).encode("utf-8")


def sanitize_for_json(obj: Any) -> Any:
    """Recursively sanitize an object for JSON serialization.

    Handles special cases like NaN, Infinity, and objects with __str__ methods.

    Args:
        obj: The object to sanitize.

    Returns:
        A JSON-serializable version of the object.
    """
    if isinstance(obj, dict):
        return {k: sanitize_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [sanitize_for_json(item) for item in obj]
    elif isinstance(obj, (float, np.float32, np.float64)):
        if math.isnan(obj):
            return None
        elif math.isinf(obj):
            return str(obj)
        return obj
    elif isinstance(obj, np.ndarray):
        return sanitize_for_json(obj.tolist())
    elif isinstance(obj, (np.int32, np.int64)):
        return int(obj)
    elif hasattr(obj, "__str__") and not isinstance(obj, (dict, list, str, int, float, bool, type(None))):
        return str(obj)
    return obj


# Global variable to store the backend instance
_backend_instance = None


def set_backend_instance(backend):
    """Set the global backend instance."""
    global _backend_instance
    _backend_instance = backend


def get_backend_instance():
    """Get the global backend instance."""
    global _backend_instance
    if _backend_instance is None:
        # Try to get the backend from main.py
        try:
            from baybe_api.main import bayes_backend
            if bayes_backend is not None:
                _backend_instance = bayes_backend
                return _backend_instance
        except (ImportError, AttributeError):
            pass

        # If we still don't have a backend, raise an exception
        from fastapi import HTTPException
        raise HTTPException(status_code=500, detail="Backend not initialized")
    return _backend_instance


def get_optimizer(optimizer_id: str):
    """Get an optimizer by ID."""
    # Get the backend instance
    backend = get_backend_instance()

    # Check if the backend has the get_optimizer method
    if hasattr(backend, 'get_optimizer'):
        return backend.get_optimizer(optimizer_id)

    # Fallback: Try to load the campaign directly
    if optimizer_id in backend.active_campaigns:
        return backend.active_campaigns[optimizer_id]

    # Try to load the campaign from disk
    file_path = backend.storage_path / f"{optimizer_id}.json"

    if not file_path.exists():
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail=f"Optimizer not found: {optimizer_id}")

    # Check file integrity
    if file_path.stat().st_size == 0:
        from fastapi import HTTPException
        raise HTTPException(status_code=500, detail=f"Campaign file for {optimizer_id} is empty")

    try:
        # Read the JSON file
        with open(file_path, 'r') as f:
            config_json = f.read()

        # Create campaign from JSON
        from baybe import Campaign
        campaign = Campaign.from_json(config_json)

        # Store in active campaigns
        backend.active_campaigns[optimizer_id] = campaign

        return campaign
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error loading optimizer: {str(e)}", exc_info=True)
        from fastapi import HTTPException
        raise HTTPException(status_code=500, detail=f"Error loading optimizer: {str(e)}")
