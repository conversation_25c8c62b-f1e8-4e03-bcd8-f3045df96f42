from typing import Dict, List, Optional, Union, Any
import logging
import os
from pathlib import Path

import numpy as np
import pandas as pd
from fastapi import FastAPI, HTTPException, Depends, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, validator, root_validator, field_validator
from contextlib import asynccontextmanager

from baybe_api.utils import SafeJSONResponse, sanitize_for_json
from baybe_api.backend_utils import set_backend_instance, get_backend, get_optimizer

# Import the Bayesian Optimization Backend
from baybe_api.optimizer.backend import BayesianOptimizationBackend

# Import the initialization router
from baybe_api.initialization import initialization_router

# Import the insights router
from baybe_api.insights_api import insights_router

# Import the strategy router
from baybe_api.strategy_router import strategy_router

# Import the SOBOL analysis router
from baybe_api.sobol_analysis import router as sobol_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Define configuration from environment variables or defaults
STORAGE_PATH = os.environ.get("STORAGE_PATH", "./data")
USE_GPU = os.environ.get("USE_GPU", "true").lower() == "true"
MAX_CACHED_CAMPAIGNS = int(os.environ.get("MAX_CACHED_CAMPAIGNS", "10"))

# Singleton instance of the backend
bayes_backend = None

# Lifespan for managing backend initialization and cleanup
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize the backend on startup
    global bayes_backend
    logger.info(f"Initializing Bayesian Optimization Backend with storage_path={STORAGE_PATH}, use_gpu={USE_GPU}")
    bayes_backend = BayesianOptimizationBackend(
        storage_path=STORAGE_PATH,
        use_gpu=USE_GPU,
        max_cached_campaigns=MAX_CACHED_CAMPAIGNS
    )

    # Set the backend instance in utils.py for global access
    set_backend_instance(bayes_backend)
    yield
    # Cleanup on shutdown (nothing to do currently)
    logger.info("Shutting down Bayesian Optimization Backend")

# Create FastAPI app
app = FastAPI(
    title="Bayesian Optimization API",
    description="API for Bayesian Optimization using BayBE",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Can be set to specific origins in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include the initialization router
app.include_router(initialization_router, prefix="/optimizations")

# Include the insights router
app.include_router(insights_router)

# Include the strategy router
app.include_router(strategy_router)

# Include the SOBOL analysis router
logger.info("Including SOBOL analysis router with routes: %s", [f"{route.path} [{route.methods}]" for route in sobol_router.routes])
app.include_router(sobol_router)

# Backend functions are now in backend_utils.py

# ---- Pydantic Models for Request/Response Validation ----

# Parameter Models
class ParameterBase(BaseModel):
    name: str

class NumericalDiscreteParameter(ParameterBase):
    type: str = "NumericalDiscrete"
    values: List[float]

class NumericalContinuousParameter(ParameterBase):
    type: str = "NumericalContinuous"
    bounds: List[float]

    @validator('bounds')
    def validate_bounds(cls, v):
        if len(v) != 2:
            raise ValueError("Bounds must have exactly 2 elements [min, max]")
        if v[0] >= v[1]:
            raise ValueError("Min bound must be less than max bound")
        return v

class CategoricalParameter(ParameterBase):
    type: str = "Categorical"
    values: List[str]

class AnyParameter(BaseModel):
    """Union of all parameter types for validation"""
    name: str
    type: str
    values: Optional[List[Union[float, str]]] = None
    bounds: Optional[List[float]] = None

    @root_validator(skip_on_failure=True)
    def validate_parameter(cls, values):
        param_type = values.get("type")
        if param_type == "NumericalDiscrete" and not values.get("values"):
            raise ValueError("NumericalDiscrete parameter must specify values")
        elif param_type == "NumericalContinuous" and not values.get("bounds"):
            raise ValueError("NumericalContinuous parameter must specify bounds")
        elif param_type == "Categorical" and not values.get("values"):
            raise ValueError("Categorical parameter must specify values")
        return values

# Target Models
class TargetBase(BaseModel):
    name: str = "Target"
    mode: str = "MAX"

    @validator('mode')
    def validate_mode(cls, v):
        if v not in ["MAX", "MIN"]:
            raise ValueError("Mode must be either 'MAX' or 'MIN'")
        return v

class NumericalTarget(TargetBase):
    type: str = "Numerical"
    bounds: Optional[List[float]] = None

    @validator('bounds')
    def validate_bounds(cls, v):
        if v is not None:
            if len(v) != 2:
                raise ValueError("Bounds must have exactly 2 elements [min, max]")
            if v[0] >= v[1]:
                raise ValueError("Min bound must be less than max bound")
        return v

class BinaryTarget(TargetBase):
    type: str = "Binary"

class AnyTarget(BaseModel):
    """Union of all target types for validation"""
    name: str = "Target"
    mode: str = "MAX"
    type: Optional[str] = "Numerical"
    bounds: Optional[List[float]] = None
    transformation: Optional[str] = None
    weight: Optional[float] = 1.0

    @validator('transformation')
    def validate_transformation(cls, v, values):
        if v is not None:
            # For MAX/MIN targets, only LINEAR transformation is valid
            mode = values.get('mode', 'MAX')
            if mode in ["MAX", "MIN"] and v != "LINEAR":
                raise ValueError(f"For {mode} targets, only LINEAR transformation is supported")
        return v

# Configuration Models
class SurrogateConfig(BaseModel):
    type: Optional[str] = "GaussianProcess"
    kernel: Optional[str] = None
    noise_prior: Optional[List[float]] = None
    # Other GP parameters can be added here

class AcquisitionConfig(BaseModel):
    type: Optional[str] = "qExpectedImprovement"
    beta: Optional[float] = None
    # Other acquisition function parameters can be added here

class RecommenderConfig(BaseModel):
    type: Optional[str] = "BotorchRecommender"
    n_restarts: Optional[int] = None
    n_raw_samples: Optional[int] = None
    # Other recommender parameters can be added here

# Request Models
class CreateOptimizationRequest(BaseModel):
    optimizer_id: str
    parameters: List[AnyParameter]
    target_config: Union[AnyTarget, List[AnyTarget]]
    constraints: Optional[List[Dict[str, Any]]] = None
    recommender_config: Optional[RecommenderConfig] = None
    objective_type: str = "SingleTarget"
    surrogate_config: Optional[SurrogateConfig] = None
    acquisition_config: Optional[AcquisitionConfig] = None
    scalarizer: Optional[str] = "GEOM_MEAN"
    weights: Optional[List[float]] = None
    initial_sampling_strategy: str = "LHS"
    num_initial_samples: int = 0

    @field_validator('objective_type')
    @classmethod
    def validate_objective_type(cls, v):
        if v not in ["SingleTarget", "Desirability"]:
            raise ValueError("Objective type must be either 'SingleTarget' or 'Desirability'")
        return v

    @validator('initial_sampling_strategy')
    def validate_sampling_strategy(cls, v):
        if v not in ["LHS", "random"]:
            raise ValueError("Initial sampling strategy must be either 'LHS' or 'random'")
        return v

@validator('scalarizer')
def validate_scalarizer(cls, v):
    if v is not None and v not in ["GEOM_MEAN", "ARITH_MEAN", "HARMONIC_MEAN", "MIN", "MAX"]:
        raise ValueError("Scalarizer must be one of 'GEOM_MEAN', 'ARITH_MEAN', 'HARMONIC_MEAN', 'MIN', or 'MAX'")
    return v

@field_validator('initial_sampling_strategy')
@classmethod
def validate_sampling_strategy(cls, v):
    if v not in ["LHS", "random"]:
        raise ValueError("Initial sampling strategy must be either 'LHS' or 'random'")
    return v

    @root_validator(skip_on_failure=True)
    def validate_multi_objective(cls, values):
        objective_type = values.get('objective_type')
        target_config = values.get('target_config')
        weights = values.get('weights')
        # scalarizer is not used in validation but might be used in the future
        _ = values.get('scalarizer')

        # Check that Desirability objective has multiple targets
        if objective_type == "Desirability":
            if not isinstance(target_config, list):
                raise ValueError("For Desirability objective, target_config must be a list of targets")
            if len(target_config) < 2:
                raise ValueError("Desirability objective requires at least 2 targets")

            # Check weights if provided
            if weights is not None and len(weights) != len(target_config):
                raise ValueError(f"Number of weights ({len(weights)}) must match number of targets ({len(target_config)})")

            # Ensure all targets have bounds for Desirability objective
            for target in target_config:
                # Check bounds
                if target.bounds is None:
                    logger.warning(f"Target {target.name} has no bounds. Default bounds [0, 100] will be used.")
                    # We don't set bounds here because it will be handled in the endpoint

                # Check transformation
                if target.transformation is None:
                    logger.warning(f"Target {target.name} has no transformation. LINEAR transformation will be used.")
                    # We don't set transformation here because it will be handled in the endpoint

        # For SingleTarget, warn if multiple targets provided
        elif objective_type == "SingleTarget" and isinstance(target_config, list) and len(target_config) > 1:
            logger.warning("Multiple targets provided for SingleTarget objective. Only the first target will be used.")

        return values

class SuggestNextPointRequest(BaseModel):
    batch_size: int = 1
    pending_experiments: Optional[List[Dict[str, Any]]] = None

    @validator('batch_size')
    def validate_batch_size(cls, v):
        if v < 1:
            raise ValueError("Batch size must be at least 1")
        return v

class TargetValue(BaseModel):
    """Model for a single target value"""
    name: str
    value: float

    @validator('value')
    def validate_value(cls, v):
        if np.isnan(v) or np.isinf(v):
            raise ValueError("Target value must be a finite number")
        return v

class AddMeasurementRequest(BaseModel):
    """Model for adding a measurement"""
    parameters: Dict[str, Any]
    target_values: Union[float, List[TargetValue]]

    @validator('target_values')
    def validate_target_values(cls, v):
        if isinstance(v, list):
            # For multi-target measurements
            if not v:
                raise ValueError("Target values list cannot be empty")
            # Validation of individual values is handled by the TargetValue model
        else:
            # For single-target measurements (backward compatibility)
            if np.isnan(v) or np.isinf(v):
                raise ValueError("Target value must be a finite number")
        return v

class BatchMeasurement(BaseModel):
    """Model for a single measurement in a batch"""
    parameters: Dict[str, Any]
    target_values: Union[float, List[TargetValue]]

    @validator('target_values')
    def validate_target_values(cls, v):
        if isinstance(v, list):
            # For multi-target measurements
            if not v:
                raise ValueError("Target values list cannot be empty")
            # Validation of individual values is handled by the TargetValue model
        else:
            # For single-target measurements (backward compatibility)
            if np.isnan(v) or np.isinf(v):
                raise ValueError("Target value must be a finite number")
        return v

class AddMultipleMeasurementsRequest(BaseModel):
    """Model for adding multiple measurements at once"""
    measurements: List[BatchMeasurement]

    @validator('measurements')
    def validate_measurements(cls, v):
        if not v:
            raise ValueError("Measurements list cannot be empty")
        return v

class InitializeFromDataRequest(BaseModel):
    parameters: List[Dict[str, Any]]
    targets: List[float]

    @validator('parameters')
    def validate_parameters(cls, v):
        if not v:
            raise ValueError("Parameters list cannot be empty")
        return v

    @validator('targets')
    def validate_targets(cls, v):
        if not v:
            raise ValueError("Targets list cannot be empty")
        if any(np.isnan(t) or np.isinf(t) for t in v):
            raise ValueError("All target values must be finite numbers")
        return v

    @root_validator(skip_on_failure=True)
    def validate_lists_length(cls, values):
        parameters = values.get("parameters", [])
        targets = values.get("targets", [])
        if len(parameters) != len(targets):
            raise ValueError(f"Parameters list length ({len(parameters)}) must match targets list length ({len(targets)})")
        return values

# Response Models
class StatusResponse(BaseModel):
    status: str
    message: str

class OptimizationResponse(StatusResponse):
    optimizer_id: Optional[str] = None
    parameter_count: Optional[int] = None
    constraint_count: Optional[int] = None

class SuggestResponse(BaseModel):
    status: str
    suggestions: Optional[List[Dict[str, Any]]] = None
    batch_size: Optional[int] = None
    message: Optional[str] = None

class SamplesResponse(BaseModel):
    status: str
    samples: Optional[List[Dict[str, Any]]] = None
    message: Optional[str] = None

class BestPointResponse(BaseModel):
    status: str
    best_value: Optional[float] = None
    best_parameters: Optional[Dict[str, Any]] = None
    best_values: Optional[Dict[str, float]] = None  # Added for multi-target optimizations
    composite_score: Optional[float] = None  # Added for multi-target optimizations
    normalized_values: Optional[Dict[str, float]] = None  # Added for multi-target optimizations
    target_weights: Optional[Dict[str, float]] = None  # Added for multi-target optimizations
    total_measurements: Optional[int] = None
    target_name: Optional[str] = None
    target_mode: Optional[str] = None
    message: Optional[str] = None

class MeasurementHistoryResponse(BaseModel):
    status: str
    measurements: List[Dict[str, Any]] = []
    target_info: Optional[List[Dict[str, Any]]] = None
    count: Optional[int] = None
    message: Optional[str] = None

class CampaignInfoResponse(BaseModel):
    status: str
    info: Optional[Dict[str, Any]] = None
    message: Optional[str] = None

class ListOptimizationsResponse(BaseModel):
    status: str
    optimizers: Optional[List[Dict[str, Any]]] = None
    count: Optional[int] = None
    message: Optional[str] = None

# ---- API Endpoints ----

@app.get("/", response_model=StatusResponse)
async def root():
    """Root endpoint to check if the API is running"""
    return {"status": "success", "message": "Bayesian Optimization API is running"}

@app.get("/health", response_class=SafeJSONResponse)
async def health(backend: BayesianOptimizationBackend = Depends(get_backend)):
    """Health check endpoint for monitoring"""
    # Get GPU information if available
    gpu_info = None
    using_gpu = False

    try:
        import torch
        if torch.cuda.is_available():
            using_gpu = True
            device_name = torch.cuda.get_device_name(0)
            memory_allocated = torch.cuda.memory_allocated(0)
            memory_reserved = torch.cuda.memory_reserved(0)

            gpu_info = {
                "name": device_name,
                "memory_allocated_mb": memory_allocated / (1024 * 1024),
                "memory_reserved_mb": memory_reserved / (1024 * 1024),
                "device_count": torch.cuda.device_count()
            }

            logger.info(f"Health check: GPU is available - {device_name}")
        else:
            logger.info("Health check: GPU is not available")
    except Exception as e:
        logger.warning(f"Error checking GPU status: {str(e)}")

    # Return the health status with GPU information
    return {
        "status": "healthy",
        "message": "API is healthy",
        "using_gpu": using_gpu,
        "gpu_info": gpu_info,
        "backend_config": {
            "use_gpu": USE_GPU,  # Use the global variable instead of backend attribute
            "storage_path": STORAGE_PATH,
            "max_cached_campaigns": MAX_CACHED_CAMPAIGNS
        }
    }

@app.get("/test-sanitize", response_class=SafeJSONResponse)
async def test_sanitize():
    """Test endpoint for sanitizing JSON responses"""
    # Create a response with problematic values
    response = {
        "status": "success",
        "nan_value": float('nan'),
        "inf_value": float('inf'),
        "neg_inf_value": float('-inf'),
        "normal_value": 42.0,
        "nested": {
            "nan_value": float('nan'),
            "inf_value": float('inf')
        }
    }
    return response

@app.post("/optimizations", response_model=OptimizationResponse)
async def create_optimization(
    request: CreateOptimizationRequest,
    background_tasks: BackgroundTasks,
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """Create a new optimization process"""

    # The Pydantic model validation already ensures target_config is appropriate for the objective type

    # For SingleTarget, convert to list if not already
    target_config = request.target_config
    if request.objective_type == "SingleTarget" and not isinstance(target_config, list):
        target_config = [target_config]

    # For Desirability, ensure we have a list (validation should have caught this already)
    if request.objective_type == "Desirability" and not isinstance(target_config, list):
        raise HTTPException(status_code=400, detail="Desirability objective requires a list of targets")

    # Process initial samples if provided
    initial_samples = None

    # Add some logging for multi-objective case
    if request.objective_type == "Desirability":
        logger.info(f"Creating multi-objective optimization with {len(target_config)} targets and scalarizer {request.scalarizer}")
        if request.weights:
            logger.info(f"Using custom weights: {request.weights}")

    # Convert parameters to dictionaries with only the necessary fields
    parameters_dict = []
    for param in request.parameters:
        param_dict = {"name": param.name, "type": param.type}
        if param.type == "NumericalContinuous" and param.bounds is not None:
            param_dict["bounds"] = param.bounds
        elif (param.type == "NumericalDiscrete" or param.type == "Categorical") and param.values is not None:
            param_dict["values"] = param.values
        parameters_dict.append(param_dict)

    # Convert target_config to dictionaries if it's a list
    if isinstance(target_config, list):
        target_config_dict = []
        for target in target_config:
            target_dict = {"name": target.name, "mode": target.mode}

            # Add weight if available
            if hasattr(target, "weight") and target.weight is not None:
                target_dict["weight"] = target.weight

            # Add type if available
            if hasattr(target, "type") and target.type is not None:
                target_dict["type"] = target.type

            # Add bounds if available - CRITICAL for multi-target optimization
            if hasattr(target, "bounds") and target.bounds is not None:
                target_dict["bounds"] = target.bounds
            elif request.objective_type == "Desirability":
                # For Desirability objective, bounds are required
                logger.info(f"Setting default bounds [0, 100] for target {target.name}")
                target_dict["bounds"] = [0, 100]

            # Add transformation if available - CRITICAL for multi-target optimization
            if hasattr(target, "transformation") and target.transformation is not None:
                target_dict["transformation"] = target.transformation
            elif request.objective_type == "Desirability":
                # For Desirability objective with MAX/MIN mode, LINEAR transformation is required
                logger.info(f"Setting default LINEAR transformation for target {target.name}")
                target_dict["transformation"] = "LINEAR"

            target_config_dict.append(target_dict)
    else:
        target_dict = {"name": target_config.name, "mode": target_config.mode}

        # Add weight if available
        if hasattr(target_config, "weight") and target_config.weight is not None:
            target_dict["weight"] = target_config.weight

        # Add type if available
        if hasattr(target_config, "type") and target_config.type is not None:
            target_dict["type"] = target_config.type

        # Add bounds if available
        if hasattr(target_config, "bounds") and target_config.bounds is not None:
            target_dict["bounds"] = target_config.bounds

        # Add transformation if available
        if hasattr(target_config, "transformation") and target_config.transformation is not None:
            target_dict["transformation"] = target_config.transformation

        target_config_dict = target_dict

    # Create the optimization
    result = backend.create_optimization(
        optimizer_id=request.optimizer_id,
        parameters=parameters_dict,
        target_config=target_config_dict,
        constraints=request.constraints,
        recommender_config=request.recommender_config.model_dump() if request.recommender_config else None,
        objective_type=request.objective_type,
        surrogate_config=request.surrogate_config.model_dump() if request.surrogate_config else None,
        acquisition_config=request.acquisition_config.model_dump() if request.acquisition_config else None,
        scalarizer=request.scalarizer,
        weights=request.weights,
        initial_sampling_strategy=request.initial_sampling_strategy,
        initial_samples=initial_samples,
        num_initial_samples=request.num_initial_samples
    )

    # Schedule background unloading after creation to free memory
    # This is useful if users create many optimizations but don't use them immediately
    if result["status"] == "success":
        background_tasks.add_task(backend.unload_campaign, request.optimizer_id)

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

@app.get("/optimizations/{optimizer_id}/suggest", response_model=SuggestResponse)
async def suggest_next_point(
    optimizer_id: str,
    batch_size: int = Query(1, ge=1, le=100),
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """Get the next suggested point(s) to evaluate (1-100 suggestions)"""

    result = backend.suggest_next_point(
        optimizer_id=optimizer_id,
        batch_size=batch_size,
        max_batch_size=100  # Increase the maximum batch size to 100
    )

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

@app.post("/optimizations/{optimizer_id}/measurements", response_model=StatusResponse)
async def add_measurement(
    optimizer_id: str,
    request: AddMeasurementRequest,
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """Add a new measurement to the optimizer"""

    # Handle both single and multi-target measurements
    if isinstance(request.target_values, list):
        # For multi-target measurements
        logger.info(f"Adding multi-target measurement with {len(request.target_values)} targets for optimizer {optimizer_id}")

        # Convert the list of TargetValue objects to a dictionary
        target_values_dict = {target.name: target.value for target in request.target_values}

        result = backend.add_multi_target_measurement(
            optimizer_id=optimizer_id,
            parameters=request.parameters,
            target_values=target_values_dict
        )
    else:
        # For single-target measurements (backward compatibility)
        logger.info(f"Adding single-target measurement for optimizer {optimizer_id}")
        result = backend.add_measurement(
            optimizer_id=optimizer_id,
            parameters=request.parameters,
            target_value=request.target_values  # Note: using target_values for backward compatibility
        )

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

@app.post("/optimizations/{optimizer_id}/measurements/batch", response_model=StatusResponse)
async def add_multiple_measurements(
    optimizer_id: str,
    request: AddMultipleMeasurementsRequest,
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """Add multiple measurements at once"""

    # Convert the measurements list to a DataFrame, handling multi-target measurements
    try:
        # Process measurements to handle multi-target values
        processed_measurements = []

        for measurement in request.measurements:
            # Create a base measurement with parameters
            processed_measurement = measurement.parameters.copy()

            # Handle target values
            if isinstance(measurement.target_values, list):
                # Multi-target measurement
                logger.info(f"Processing multi-target measurement with {len(measurement.target_values)} targets")

                # Add each target value to the measurement
                for target in measurement.target_values:
                    processed_measurement[target.name] = target.value
            else:
                # Single-target measurement (backward compatibility)
                # We need to know the target name for the first target
                # For now, we'll use a placeholder that will be replaced in the backend
                processed_measurement["_target"] = measurement.target_values

            processed_measurements.append(processed_measurement)

        # Create DataFrame from processed measurements
        measurements_df = pd.DataFrame(processed_measurements)
        logger.info(f"Processed {len(processed_measurements)} measurements with columns: {measurements_df.columns.tolist()}")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing measurements: {str(e)}")

    result = backend.add_multiple_measurements(
        optimizer_id=optimizer_id,
        measurements=measurements_df
    )

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

@app.get("/optimizations/{optimizer_id}/best", response_class=SafeJSONResponse)
async def get_best_point(
    optimizer_id: str,
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """Get the current best point for an optimization"""

    result = backend.get_best_point(optimizer_id=optimizer_id)

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

@app.get("/optimizations/{optimizer_id}/measurements", response_class=SafeJSONResponse)
@app.get("/optimizations/{optimizer_id}/history", response_class=SafeJSONResponse)
async def get_measurement_history(
    optimizer_id: str,
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """Get the measurement history for an optimization"""

    result = backend.get_measurement_history(optimizer_id=optimizer_id)

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

# SafeJSONResponse is already imported above

@app.get("/optimizations/{optimizer_id}", response_class=SafeJSONResponse)
@app.get("/optimizations/{optimizer_id}/info", response_class=SafeJSONResponse)
async def get_campaign_info(
    optimizer_id: str,
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """Get information about a campaign"""

    result = backend.get_campaign_info(optimizer_id=optimizer_id)

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

@app.post("/optimizations/{optimizer_id}/initialize", response_model=StatusResponse)
async def initialize_from_data(
    optimizer_id: str,
    request: InitializeFromDataRequest,
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """Initialize a campaign with existing data"""

    result = backend.initialize_from_data(
        optimizer_id=optimizer_id,
        parameters=request.parameters,
        targets=request.targets
    )

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

@app.get("/optimizations", response_model=ListOptimizationsResponse)
async def list_optimizations(
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """List all available optimizations"""

    result = backend.list_optimizations()

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

@app.delete("/optimizations/{optimizer_id}", response_model=StatusResponse)
async def delete_optimization(
    optimizer_id: str,
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """Delete an optimization campaign"""

    result = backend.delete_optimization(optimizer_id=optimizer_id)

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

@app.post("/optimizations/{optimizer_id}/unload", response_model=StatusResponse)
async def unload_campaign(
    optimizer_id: str,
    backend: BayesianOptimizationBackend = Depends(get_backend)
):
    """Unload a campaign from memory to free up resources"""

    result = backend.unload_campaign(optimizer_id=optimizer_id)

    if result["status"] == "error":
        raise HTTPException(status_code=400, detail=result["message"])

    return result

# Add any other utility endpoints here

if __name__ == "__main__":
    import uvicorn
    # Get port from environment or use default
    port = int(os.environ.get("PORT", "8000"))

    # Start the server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )