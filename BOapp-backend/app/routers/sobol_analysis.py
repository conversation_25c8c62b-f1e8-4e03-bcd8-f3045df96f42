"""
SOBOL Analysis Module for Contour Plot Generation

This module provides functionality for generating contour plot data using
Sobol' sensitivity analysis from SALib.
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from SALib.sample import saltelli
from SALib.analyze import sobol
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern, RBF, ConstantKernel

from app.optimizer.backend import OptimizerBackend
from app.dependencies import get_optimizer_backend

router = APIRouter(
    prefix="/sobol-analysis",
    tags=["sobol-analysis"],
    responses={404: {"description": "Not found"}},
)

class SobolContourRequest(BaseModel):
    """Request model for SOBOL contour plot data."""
    optimizer_id: str
    param1_name: str
    param2_name: str
    grid_size: int = 50
    num_samples: int = 1024

class SobolContourResponse(BaseModel):
    """Response model for SOBOL contour plot data."""
    x_values: List[float]
    y_values: List[float]
    z_values: List[List[float]]
    uncertainty: List[List[float]]
    sobol_first_order: Dict[str, float]
    sobol_second_order: float
    sobol_total: Dict[str, float]

def _extract_parameter_data(
    optimizer_backend: OptimizerBackend,
    optimizer_id: str,
    param1_name: str,
    param2_name: str
) -> Tuple[np.ndarray, np.ndarray, Dict[str, List[float]], Dict[str, Dict[str, Any]]]:
    """
    Extract parameter data from the optimizer.
    
    Args:
        optimizer_backend: The optimizer backend instance
        optimizer_id: The ID of the optimizer
        param1_name: The name of the first parameter
        param2_name: The name of the second parameter
        
    Returns:
        Tuple containing:
        - X: Array of parameter values
        - y: Array of target values
        - param_ranges: Dictionary of parameter ranges
        - param_configs: Dictionary of parameter configurations
    """
    # Get optimizer state
    optimizer = optimizer_backend.get_optimizer(optimizer_id)
    if optimizer is None:
        raise HTTPException(status_code=404, detail=f"Optimizer {optimizer_id} not found")
    
    # Get measurements
    measurements = optimizer_backend.get_measurements(optimizer_id)
    if not measurements:
        raise HTTPException(status_code=400, detail="No measurements available for this optimization")
    
    # Get parameter configurations
    param_configs = {}
    for param in optimizer.config.parameters:
        if param.name == param1_name or param.name == param2_name:
            param_configs[param.name] = param
    
    if param1_name not in param_configs:
        raise HTTPException(status_code=400, detail=f"Parameter {param1_name} not found")
    if param2_name not in param_configs:
        raise HTTPException(status_code=400, detail=f"Parameter {param2_name} not found")
    
    # Extract parameter ranges
    param_ranges = {}
    for name, config in param_configs.items():
        if hasattr(config, "bounds"):
            param_ranges[name] = [config.bounds[0], config.bounds[1]]
        elif hasattr(config, "lower_bound") and hasattr(config, "upper_bound"):
            param_ranges[name] = [config.lower_bound, config.upper_bound]
        else:
            # For categorical parameters, use indices
            if hasattr(config, "categories"):
                param_ranges[name] = [0, len(config.categories) - 1]
            else:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Cannot determine range for parameter {name}"
                )
    
    # Extract parameter values and target values from measurements
    X = []
    y = []
    
    for measurement in measurements:
        params = measurement.parameters
        if param1_name in params and param2_name in params:
            # Handle categorical parameters by converting to indices
            param1_value = params[param1_name]
            param2_value = params[param2_name]
            
            if hasattr(param_configs[param1_name], "categories"):
                param1_value = param_configs[param1_name].categories.index(param1_value)
            
            if hasattr(param_configs[param2_name], "categories"):
                param2_value = param_configs[param2_name].categories.index(param2_value)
            
            X.append([param1_value, param2_value])
            y.append(measurement.target_value)
    
    if not X:
        raise HTTPException(
            status_code=400, 
            detail=f"No measurements found with both parameters {param1_name} and {param2_name}"
        )
    
    return np.array(X), np.array(y), param_ranges, param_configs

def _build_surrogate_model(X: np.ndarray, y: np.ndarray) -> GaussianProcessRegressor:
    """
    Build a Gaussian Process surrogate model.
    
    Args:
        X: Array of parameter values
        y: Array of target values
        
    Returns:
        Trained Gaussian Process model
    """
    # Define kernel
    kernel = ConstantKernel() * Matern(nu=2.5, length_scale=[1.0] * X.shape[1])
    
    # Create and fit GP model
    gp = GaussianProcessRegressor(
        kernel=kernel,
        normalize_y=True,
        n_restarts_optimizer=5,
        random_state=42
    )
    gp.fit(X, y)
    
    return gp

def _perform_sobol_analysis(
    gp: GaussianProcessRegressor,
    param_ranges: Dict[str, List[float]],
    param1_name: str,
    param2_name: str,
    num_samples: int = 1024
) -> Dict[str, Any]:
    """
    Perform Sobol' sensitivity analysis.
    
    Args:
        gp: Trained Gaussian Process model
        param_ranges: Dictionary of parameter ranges
        param1_name: Name of first parameter
        param2_name: Name of second parameter
        num_samples: Number of samples for Sobol' analysis
        
    Returns:
        Dictionary containing Sobol' indices
    """
    # Define problem for SALib
    problem = {
        'num_vars': 2,
        'names': [param1_name, param2_name],
        'bounds': [param_ranges[param1_name], param_ranges[param2_name]]
    }
    
    # Generate samples using Saltelli's extension of Sobol' sequence
    param_values = saltelli.sample(problem, num_samples)
    
    # Evaluate model at sample points
    Y = np.zeros(param_values.shape[0])
    for i, X in enumerate(param_values):
        Y[i] = gp.predict([X])[0]
    
    # Perform Sobol' Analysis
    Si = sobol.analyze(problem, Y, print_to_console=False)
    
    return {
        'S1': {
            param1_name: float(Si['S1'][0]),
            param2_name: float(Si['S1'][1])
        },
        'S2': float(Si['S2'][0, 1]),
        'ST': {
            param1_name: float(Si['ST'][0]),
            param2_name: float(Si['ST'][1])
        }
    }

def _generate_contour_data(
    gp: GaussianProcessRegressor,
    param_ranges: Dict[str, List[float]],
    param1_name: str,
    param2_name: str,
    grid_size: int = 50
) -> Dict[str, Any]:
    """
    Generate contour plot data.
    
    Args:
        gp: Trained Gaussian Process model
        param_ranges: Dictionary of parameter ranges
        param1_name: Name of first parameter
        param2_name: Name of second parameter
        grid_size: Size of the grid for contour plot
        
    Returns:
        Dictionary containing contour plot data
    """
    # Generate grid for contour plot
    x1 = np.linspace(param_ranges[param1_name][0], param_ranges[param1_name][1], grid_size)
    x2 = np.linspace(param_ranges[param2_name][0], param_ranges[param2_name][1], grid_size)
    X1, X2 = np.meshgrid(x1, x2)
    
    # Predict values across the grid
    Z = np.zeros(X1.shape)
    Z_std = np.zeros(X1.shape)
    
    for i in range(X1.shape[0]):
        for j in range(X1.shape[1]):
            x = np.array([[X1[i,j], X2[i,j]]])
            Z[i,j], Z_std[i,j] = gp.predict(x, return_std=True)
    
    return {
        'x_values': x1.tolist(),
        'y_values': x2.tolist(),
        'z_values': Z.tolist(),
        'uncertainty': Z_std.tolist()
    }

@router.post("/contour", response_model=SobolContourResponse)
async def generate_sobol_contour(
    request: SobolContourRequest,
    optimizer_backend: OptimizerBackend = Depends(get_optimizer_backend)
) -> SobolContourResponse:
    """
    Generate contour plot data with Sobol' sensitivity analysis.
    
    Args:
        request: The request containing optimizer ID and parameter names
        optimizer_backend: The optimizer backend instance
        
    Returns:
        Contour plot data with Sobol' indices
    """
    try:
        # Extract parameter data
        X, y, param_ranges, param_configs = _extract_parameter_data(
            optimizer_backend,
            request.optimizer_id,
            request.param1_name,
            request.param2_name
        )
        
        # Build surrogate model
        gp = _build_surrogate_model(X, y)
        
        # Perform Sobol' analysis
        sobol_indices = _perform_sobol_analysis(
            gp,
            param_ranges,
            request.param1_name,
            request.param2_name,
            request.num_samples
        )
        
        # Generate contour data
        contour_data = _generate_contour_data(
            gp,
            param_ranges,
            request.param1_name,
            request.param2_name,
            request.grid_size
        )
        
        # Combine results
        return SobolContourResponse(
            x_values=contour_data['x_values'],
            y_values=contour_data['y_values'],
            z_values=contour_data['z_values'],
            uncertainty=contour_data['uncertainty'],
            sobol_first_order=sobol_indices['S1'],
            sobol_second_order=sobol_indices['S2'],
            sobol_total=sobol_indices['ST']
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
