#!/bin/bash
# Launcher script for Bayesian Optimization API

# Make sure the script is executable
chmod +x entrypoint.sh

# Function to display help
show_help() {
    echo "Usage: ./launcher.sh [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start         Start the API server"
    echo "  test          Run all tests"
    echo "  client        Run the test client"
    echo "  test-gpu      Test GPU setup"
    echo "  benchmark     Run performance benchmarks"
    echo "  install-gpu   Install NVIDIA components for Docker"
    echo "  help          Show this help message"
    echo ""
}

# Process command line arguments
case "$1" in
    start)
        echo "Starting Bayesian Optimization API..."
        python run.py
        ;;
    test)
        echo "Running all tests..."
        cd tests && python run_all_tests.py
        ;;
    client)
        echo "Running test client..."
        python previous_tests/test_client.py
        ;;
    test-gpu)
        echo "Testing GPU setup..."
        python previous_tests/gpu_test.py
        ;;
    benchmark)
        echo "Running performance benchmarks..."
        python previous_tests/profile-performance.py
        ;;
    install-gpu)
        echo "Installing NVIDIA components for Docker..."
        sudo ./install-gpu.sh
        ;;
    help|*)
        show_help
        ;;
esac