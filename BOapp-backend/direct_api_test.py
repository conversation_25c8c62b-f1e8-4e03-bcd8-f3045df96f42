"""Direct tests for the API endpoints using HTTP calls to the running API.

This script is independent of pytest and conftest.py, so it can be run directly
without any dependencies on the baybe module.
"""
import requests
import time
import json
from typing import Dict, Any, List

# API configuration
API_URL = "http://localhost:8000"
API_KEY = "123456789"
HEADERS = {"X-API-Key": API_KEY}

def run_test(test_name: str, test_func):
    """Run a test function and print the result."""
    print(f"Running test: {test_name}")
    try:
        test_func()
        print(f"✅ PASSED: {test_name}")
        return True
    except Exception as e:
        print(f"❌ FAILED: {test_name}")
        print(f"Error: {str(e)}")
        return False

def test_health_endpoint():
    """Test that the health endpoint returns a healthy status."""
    response = requests.get(f"{API_URL}/health", headers=HEADERS)
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "healthy", f"Expected status 'healthy', got {data['status']}"

def test_root_endpoint():
    """Test that the root endpoint returns a success status."""
    response = requests.get(f"{API_URL}/", headers=HEADERS)
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    assert "message" in data, "Expected 'message' in response"
    assert "Bayesian Optimization API is running" in data["message"], "Expected 'Bayesian Optimization API is running' in message"

def test_optimization_test_endpoint():
    """Test the optimization test endpoint."""
    response = requests.get(f"{API_URL}/optimization/test", headers=HEADERS)
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    assert "message" in data, "Expected 'message' in response"
    assert "Optimization test successful" in data["message"], "Expected 'Optimization test successful' in message"

def test_create_and_use_optimization():
    """Test creating an optimization and using it."""
    # Create a unique optimizer ID based on timestamp
    optimizer_id = f"test_optimizer_{int(time.time())}"
    
    # 1. Create optimization
    create_payload = {
        "optimizer_id": optimizer_id,
        "parameters": [
            {"name": "x", "type": "NumericalContinuous", "bounds": [0, 1]},
            {"name": "y", "type": "NumericalContinuous", "bounds": [0, 1]}
        ],
        "target_config": {"name": "Target", "mode": "MAX"},
        "objective_type": "SingleTarget",
        "num_initial_samples": 0,  # No initial samples for faster test
        "recommender_config": {
            "type": "BotorchRecommender"
        },
        "acquisition_config": {
            "type": "qExpectedImprovement"
        },
        "surrogate_config": {
            "type": "GaussianProcess"
        }
    }
    
    response = requests.post(
        f"{API_URL}/optimizations", 
        headers=HEADERS,
        json=create_payload
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    assert "optimizer_id" in data, "Expected 'optimizer_id' in response"
    assert data["optimizer_id"] == optimizer_id, f"Expected optimizer_id {optimizer_id}, got {data['optimizer_id']}"
    
    # 2. Get suggestions
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/suggest?batch_size=1", 
        headers=HEADERS
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    assert "suggestions" in data, "Expected 'suggestions' in response"
    assert len(data["suggestions"]) == 1, f"Expected 1 suggestion, got {len(data['suggestions'])}"
    suggestion = data["suggestions"][0]
    assert "x" in suggestion, "Expected 'x' in suggestion"
    assert "y" in suggestion, "Expected 'y' in suggestion"
    
    # 3. Add measurement
    measurement_payload = {
        "parameters": suggestion,
        "target_value": 0.75  # Some arbitrary value
    }
    
    response = requests.post(
        f"{API_URL}/optimizations/{optimizer_id}/measurements", 
        headers=HEADERS,
        json=measurement_payload
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    
    # 4. Get another suggestion (now with data)
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/suggest?batch_size=1", 
        headers=HEADERS
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    assert "suggestions" in data, "Expected 'suggestions' in response"
    assert len(data["suggestions"]) == 1, f"Expected 1 suggestion, got {len(data['suggestions'])}"
    
    # 5. Get campaign info
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/info", 
        headers=HEADERS
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    assert "info" in data, "Expected 'info' in response"
    assert "parameters" in data["info"], "Expected 'parameters' in info"
    assert "targets" in data["info"], "Expected 'targets' in info"
    assert data["info"]["measurements_count"] == 1, f"Expected 1 measurement, got {data['info']['measurements_count']}"
    
    # 6. Get history
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/history", 
        headers=HEADERS
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    assert "measurements" in data, "Expected 'measurements' in response"
    assert len(data["measurements"]) == 1, f"Expected 1 measurement, got {len(data['measurements'])}"
    
    # 7. Get best point
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/best", 
        headers=HEADERS
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    assert "best_parameters" in data, "Expected 'best_parameters' in response"
    assert "best_value" in data, "Expected 'best_value' in response"
    
    # 8. Delete optimization
    response = requests.delete(
        f"{API_URL}/optimizations/{optimizer_id}", 
        headers=HEADERS
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    
    # 9. Verify it's gone
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/info", 
        headers=HEADERS
    )
    assert response.status_code == 404, f"Expected status code 404, got {response.status_code}"

def test_list_optimizations():
    """Test listing all optimizations."""
    response = requests.get(f"{API_URL}/optimizations", headers=HEADERS)
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    assert "optimizations" in data, "Expected 'optimizations' in response"
    # We can't assert the exact count as it may vary

def test_multi_objective_optimization():
    """Test creating and using a multi-objective optimization."""
    # Create a unique optimizer ID based on timestamp
    optimizer_id = f"multi_obj_test_{int(time.time())}"
    
    # 1. Create optimization
    create_payload = {
        "optimizer_id": optimizer_id,
        "parameters": [
            {"name": "x", "type": "NumericalContinuous", "bounds": [0, 1]},
            {"name": "y", "type": "NumericalContinuous", "bounds": [0, 1]}
        ],
        "target_config": [
            {"name": "efficiency", "mode": "MAX"},
            {"name": "cost", "mode": "MIN", "weight": 0.7}
        ],
        "objective_type": "Desirability",
        "scalarizer": "GEOM_MEAN",
        "weights": [1.0, 0.7],
        "num_initial_samples": 0,  # No initial samples for faster test
        "recommender_config": {
            "type": "BotorchRecommender"
        },
        "acquisition_config": {
            "type": "qExpectedImprovement"
        },
        "surrogate_config": {
            "type": "GaussianProcess"
        }
    }
    
    response = requests.post(
        f"{API_URL}/optimizations", 
        headers=HEADERS,
        json=create_payload
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    
    # 2. Get suggestions
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/suggest?batch_size=1", 
        headers=HEADERS
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    suggestion = data["suggestions"][0]
    
    # 3. Add measurement with multiple targets
    measurement_payload = {
        "parameters": suggestion,
        "target_values": {
            "efficiency": 0.8,
            "cost": 0.3
        }
    }
    
    response = requests.post(
        f"{API_URL}/optimizations/{optimizer_id}/measurements", 
        headers=HEADERS,
        json=measurement_payload
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    
    # 4. Get campaign info
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/info", 
        headers=HEADERS
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    data = response.json()
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"
    assert len(data["info"]["targets"]) == 2, f"Expected 2 targets, got {len(data['info']['targets'])}"
    
    # 5. Delete optimization
    response = requests.delete(
        f"{API_URL}/optimizations/{optimizer_id}", 
        headers=HEADERS
    )
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

def main():
    """Run all tests."""
    tests = [
        ("Health Endpoint", test_health_endpoint),
        ("Root Endpoint", test_root_endpoint),
        ("Optimization Test Endpoint", test_optimization_test_endpoint),
        ("List Optimizations", test_list_optimizations),
        ("Create and Use Optimization", test_create_and_use_optimization),
        ("Multi-Objective Optimization", test_multi_objective_optimization)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = run_test(test_name, test_func)
        results.append((test_name, result))
    
    # Print summary
    print("\n=== Test Summary ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed < total:
        print("\nFailed tests:")
        for test_name, result in results:
            if not result:
                print(f"- {test_name}")
        return 1
    return 0

if __name__ == "__main__":
    exit(main())
