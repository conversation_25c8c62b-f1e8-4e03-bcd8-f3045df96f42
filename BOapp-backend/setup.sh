#!/bin/bash
# Setup script for Bayesian Optimization API

# Create virtual environment
echo "Creating virtual environment..."
python -m venv venv

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Make scripts executable
echo "Making scripts executable..."
chmod +x launcher.sh
chmod +x entrypoint.sh
chmod +x install-gpu.sh

echo "Setup complete! You can now run the API with ./launcher.sh start"