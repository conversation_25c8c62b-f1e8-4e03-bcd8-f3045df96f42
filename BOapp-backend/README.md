# Bayesian Optimization API with GPU Acceleration

A high-performance FastAPI-based web service for Bayesian optimization using the BayBE (Bayesian Backend) package with GPU acceleration for faster model training and optimization.

![Bayesian Optimization](https://img.shields.io/badge/Bayesian-Optimization-blue)
![GPU Accelerated](https://img.shields.io/badge/GPU-Accelerated-green)
![Docker](https://img.shields.io/badge/Docker-Ready-blue)
![Python](https://img.shields.io/badge/Python-3.10%2B-blue)

## 🚀 Quick Start

### Setup

```bash
# Clone the repository
git clone https://github.com/yourusername/BOapp-backend.git
cd BOapp-backend

# Run the setup script to create a virtual environment and install dependencies
./setup.sh

# Start the API server
./launcher.sh start
```

The API will be available at http://localhost:8000. You can access the API documentation at http://localhost:8000/docs.

### Running Tests

```bash
# Run all tests
./launcher.sh test

# Run the test client
./launcher.sh client
```

## 🚀 Features

- **GPU Acceleration** for significantly faster model training and optimization
- **RESTful API** for easy integration with existing systems
- **Multiple Parameter Types**:
  - Numerical (continuous and discrete)
  - Categorical
  - Chemical substances with descriptor-based encodings
- **Batch Processing** for efficient parallel experimentation
- **Persistent Storage** of optimization campaigns
- **Docker Integration** with NVIDIA GPU support
- **Automated Parameter Tuning** using Bayesian optimization techniques
- **Performance Benchmarking Tools** to compare CPU vs GPU performance

## 📊 Use Cases

- **Experimental Design Optimization** in chemistry, materials science, biology
- **Hyperparameter Tuning** for machine learning models
- **Process Optimization** in manufacturing and engineering
- **Product Formulation** optimization in pharmaceuticals, food science, and consumer products
- **A/B Testing** and optimization for web applications

## 🖥 Requirements

- **Python 3.10+**
- **NVIDIA GPU** with CUDA support (optional but recommended)
- **Docker** and **Docker Compose** (for containerized deployment)
- **NVIDIA Container Toolkit** (for GPU support in Docker)

## ⚡ Quick Start

### Using Docker (Recommended)

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-username/bayesian-optimization-gpu.git
   cd bayesian-optimization-gpu
   ```

2. **Run the setup script**:
   ```bash
   ./setup.sh
   ```

3. **Start the API with the launcher**:
   ```bash
   ./launcher.sh start
   ```

4. **Access the API documentation**:
   Open your browser and navigate to [http://localhost:8000/docs](http://localhost:8000/docs)

### Manual Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Start the API server**:
   ```bash
   uvicorn main:app --host 0.0.0.0 --port 8000
   ```

## 🛠 API Endpoints

- **`GET /health`** - Check API health and GPU status
- **`POST /optimization/{optimizer_id}`** - Create a new optimization process
- **`GET /optimization/{optimizer_id}/suggest`** - Get next point(s) to evaluate
- **`POST /optimization/{optimizer_id}/measurement`** - Add a new measurement
- **`POST /optimization/{optimizer_id}/measurements`** - Add multiple measurements
- **`GET /optimization/{optimizer_id}/best`** - Get the current best point
- **`GET /optimization/{optimizer_id}/load`** - Load an existing optimization

## 📝 Example Usage

### 1. Create a new optimization

```bash
curl -X POST "http://localhost:8000/optimization/my-experiment" \
  -H "X-API-Key: 123456789" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": [
      {
        "type": "NumericalDiscrete",
        "name": "Temperature",
        "values": [50, 60, 70, 80, 90],
        "tolerance": 0.5
      },
      {
        "type": "CategoricalParameter",
        "name": "Catalyst",
        "values": ["A", "B", "C", "D"],
        "encoding": "OHE"
      }
    ],
    "target_config": {
      "name": "Yield",
      "mode": "MAX"
    }
  }'
```

### 2. Get next suggestion

```bash
curl -X GET "http://localhost:8000/optimization/my-experiment/suggest?batch_size=1" \
  -H "X-API-Key: 123456789"
```

### 3. Add a measurement

```bash
curl -X POST "http://localhost:8000/optimization/my-experiment/measurement" \
  -H "X-API-Key: 123456789" \
  -H "Content-Type: application/json" \
  -d '{
    "parameters": {
      "Temperature": 70,
      "Catalyst": "B"
    },
    "target_value": 82.5
  }'
```

## 🧰 Tools and Utilities

The repository includes several tools to help you get the most out of the API:

### Test Client

Run the test client to see the API in action with a simulated chemical reaction optimization:

```bash
./launcher.sh client
```

### GPU Performance Testing

Test your GPU configuration and compare CPU vs GPU performance:

```bash
# Test GPU setup
./launcher.sh test-gpu

# Run benchmarks comparing CPU and GPU performance
./launcher.sh benchmark
```

### GPU Installation Tool

If you need to install the NVIDIA components for Docker:

```bash
sudo ./launcher.sh install-gpu
```

## 🔍 Performance Insights

The GPU-accelerated version offers significant performance improvements, especially for:

- **Large parameter spaces** (many dimensions)
- **Complex models** with many measurements
- **Batch recommendations** with large batch sizes

Performance benchmarking results on an NVIDIA RTX 3080:

| Scenario | CPU Time | GPU Time | Speedup |
|----------|----------|----------|---------|
| 5 parameters, batch size 1  | 0.5s  | 0.3s  | 1.7x |
| 10 parameters, batch size 1 | 1.2s  | 0.4s  | 3.0x |
| 20 parameters, batch size 1 | 5.4s  | 0.6s  | 9.0x |
| 10 parameters, batch size 8 | 2.7s  | 0.5s  | 5.4x |
| 10 parameters, batch size 32| 9.8s  | 1.1s  | 8.9x |

## 🧪 Environment Variables

- **`API_KEY`**: Authentication key for the API (default: "your-default-api-key")
- **`USE_GPU`**: Set to "true" to enable GPU acceleration or "false" to use CPU only (default: "true")

## 🏆 Advanced Configuration

For advanced users, the system supports:

- **Custom Recommenders**: Configure different recommendation strategies
- **Multiple Parameters**: Support for a wide variety of parameter types
- **Complex Constraints**: Apply constraints on the search space

## 📚 Documentation

For more information about BayBE, see the [official BayBE documentation](https://emdgroup.github.io/baybe/stable/userguide/userguide.html).

## 🛡 License

This project is licensed under the Apache License 2.0 - see the LICENSE file for details.

## 🤝 Acknowledgements

- [BayBE](https://github.com/emdgroup/baybe) - The Bayesian Back End library
- [FastAPI](https://fastapi.tiangolo.com/) - The web framework used
- [BoTorch](https://botorch.org/) - Bayesian optimization library (used by BayBE)
- [PyTorch](https://pytorch.org/) - The machine learning framework