"""Tests for the delete optimization endpoint."""
import pytest

def test_delete_optimization_success(client, mock_backend):
    """Test successful deletion of an optimization."""
    response = client.delete("/optimizations/test_optimizer")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["message"] == "Optimizer test_optimizer deleted successfully"
    
    # Verify the backend was called correctly
    mock_backend.delete_optimization.assert_called_once_with(
        optimizer_id="test_optimizer"
    )

def test_delete_optimization_not_found(client, mock_backend):
    """Test deletion when optimizer doesn't exist."""
    # Configure the mock to return an error
    mock_backend.delete_optimization.return_value = {
        "status": "error",
        "message": "Optimizer not_found_optimizer not found"
    }
    
    response = client.delete("/optimizations/not_found_optimizer")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"]

def test_delete_optimization_io_error(client, mock_backend):
    """Test handling of I/O errors during deletion."""
    # Configure the mock to return an I/O error
    mock_backend.delete_optimization.return_value = {
        "status": "error",
        "message": "Error deleting optimizer file: Permission denied"
    }
    
    response = client.delete("/optimizations/test_optimizer")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Permission denied" in data["detail"]

def test_delete_optimization_backend_error(client, mock_backend):
    """Test handling of backend errors during optimization deletion."""
    # Configure the mock to return an error
    mock_backend.delete_optimization.return_value = {
        "status": "error",
        "message": "Error deleting optimization: Unexpected error"
    }
    
    response = client.delete("/optimizations/test_optimizer")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Error deleting optimization" in data["detail"]