"""Tests for the initialize from data endpoint."""
import pytest

def test_initialize_from_data_success(client, sample_initialize_data_request, mock_backend):
    """Test successful initialization from data."""
    response = client.post(
        "/optimizations/test_optimizer/initialize",
        json=sample_initialize_data_request
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["message"] == "2 measurements added"
    
    # Verify the backend was called correctly
    mock_backend.initialize_from_data.assert_called_once_with(
        optimizer_id="test_optimizer",
        parameters=sample_initialize_data_request["parameters"],
        targets=sample_initialize_data_request["targets"]
    )

def test_initialize_from_data_invalid_request(client):
    """Test initialization with invalid request."""
    # Empty parameters and targets
    invalid_request = {
        "parameters": [],
        "targets": []
    }
    
    response = client.post(
        "/optimizations/test_optimizer/initialize",
        json=invalid_request
    )
    assert response.status_code == 422  # Unprocessable Entity
    
    # Missing targets field
    invalid_request = {
        "parameters": [{"x": 0.1, "y": 0.2}]
    }
    
    response = client.post(
        "/optimizations/test_optimizer/initialize",
        json=invalid_request
    )
    assert response.status_code == 422  # Unprocessable Entity
    
    # Mismatched length of parameters and targets
    invalid_request = {
        "parameters": [{"x": 0.1, "y": 0.2}, {"x": 0.3, "y": 0.4}],
        "targets": [0.5]
    }
    
    response = client.post(
        "/optimizations/test_optimizer/initialize",
        json=invalid_request
    )
    assert response.status_code == 422  # Unprocessable Entity
    
    # Invalid target values
    invalid_request = {
        "parameters": [{"x": 0.1, "y": 0.2}],
        "targets": [float('nan')]
    }
    
    response = client.post(
        "/optimizations/test_optimizer/initialize",
        json=invalid_request
    )
    assert response.status_code == 422  # Unprocessable Entity or 400 Bad Request

def test_initialize_from_data_optimizer_not_found(client, sample_initialize_data_request, mock_backend):
    """Test initialization when optimizer doesn't exist."""
    # Configure the mock to return an error
    mock_backend.initialize_from_data.return_value = {
        "status": "error",
        "message": "Optimizer not_found_optimizer not found"
    }
    
    response = client.post(
        "/optimizations/not_found_optimizer/initialize",
        json=sample_initialize_data_request
    )
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"]

def test_initialize_from_data_no_objective(client, sample_initialize_data_request, mock_backend):
    """Test initialization when no objective is defined."""
    # Configure the mock to return an error
    mock_backend.initialize_from_data.return_value = {
        "status": "error",
        "message": "No objective defined for this campaign"
    }
    
    response = client.post(
        "/optimizations/test_optimizer/initialize",
        json=sample_initialize_data_request
    )
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "No objective defined" in data["detail"]

def test_initialize_from_data_backend_error(client, sample_initialize_data_request, mock_backend):
    """Test handling of backend errors during initialization."""
    # Configure the mock to return an error
    mock_backend.initialize_from_data.return_value = {
        "status": "error",
        "message": "Error initializing from data: Unexpected error"
    }
    
    response = client.post(
        "/optimizations/test_optimizer/initialize",
        json=sample_initialize_data_request
    )
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Error initializing from data" in data["detail"]