"""Tests for the suggest next point endpoint."""
import pytest

def test_suggest_next_point_success(client, mock_backend):
    """Test successful suggestion of next point(s)."""
    # Mock setup is in the fixture
    response = client.get("/optimizations/test_optimizer/suggest")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "suggestions" in data
    assert isinstance(data["suggestions"], list)
    assert len(data["suggestions"]) == 1
    assert "x" in data["suggestions"][0]
    assert "y" in data["suggestions"][0]
    assert data["batch_size"] == 1
    
    # Verify the backend was called correctly
    mock_backend.suggest_next_point.assert_called_once_with(
        optimizer_id="test_optimizer",
        batch_size=1
    )

def test_suggest_next_point_with_batch_size(client, mock_backend):
    """Test suggestion with specific batch size."""
    # Configure the mock to return multiple suggestions
    mock_backend.suggest_next_point.return_value = {
        "status": "success",
        "suggestions": [
            {"x": 0.1, "y": 0.2},
            {"x": 0.3, "y": 0.4},
            {"x": 0.5, "y": 0.6}
        ],
        "batch_size": 3
    }
    
    response = client.get("/optimizations/test_optimizer/suggest?batch_size=3")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert len(data["suggestions"]) == 3
    assert data["batch_size"] == 3
    
    # Verify the backend was called with batch_size=3
    mock_backend.suggest_next_point.assert_called_once_with(
        optimizer_id="test_optimizer",
        batch_size=3
    )

def test_suggest_next_point_invalid_batch_size(client):
    """Test suggestion with invalid batch size."""
    response = client.get("/optimizations/test_optimizer/suggest?batch_size=0")
    assert response.status_code == 422  # Unprocessable Entity
    
    response = client.get("/optimizations/test_optimizer/suggest?batch_size=-1")
    assert response.status_code == 422  # Unprocessable Entity

def test_suggest_next_point_excessive_batch_size(client, mock_backend):
    """Test suggestion with batch size exceeding maximum."""
    # The API should handle this internally
    response = client.get("/optimizations/test_optimizer/suggest?batch_size=100")
    assert response.status_code == 200
    
    # Verify backend was called with potentially capped batch size
    mock_backend.suggest_next_point.assert_called_once()
    # We don't check the exact batch_size as it's handled by the backend

def test_suggest_next_point_optimizer_not_found(client, mock_backend):
    """Test suggestion when optimizer doesn't exist."""
    # Configure the mock to return an error
    mock_backend.suggest_next_point.return_value = {
        "status": "error",
        "message": "Optimizer not_found_optimizer not found"
    }
    
    response = client.get("/optimizations/not_found_optimizer/suggest")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"]

def test_suggest_next_point_backend_error(client, mock_backend):
    """Test handling of backend errors during suggestion."""
    # Configure the mock to return an error
    mock_backend.suggest_next_point.return_value = {
        "status": "error",
        "message": "Internal error in suggestion algorithm"
    }
    
    response = client.get("/optimizations/test_optimizer/suggest")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Internal error" in data["detail"]