"""Tests for the add multiple measurements endpoint."""
import pytest
from unittest.mock import patch, ANY

def test_add_multiple_measurements_success(client, sample_multiple_measurements_request, mock_backend):
    """Test successful addition of multiple measurements."""
    response = client.post(
        "/optimizations/test_optimizer/measurements/batch",
        json=sample_multiple_measurements_request
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["message"] == "2 measurements added"
    
    # Verify the backend was called correctly
    mock_backend.add_multiple_measurements.assert_called_once()
    args, kwargs = mock_backend.add_multiple_measurements.call_args
    assert kwargs["optimizer_id"] == "test_optimizer"
    # We can't directly compare the DataFrame, but we can check it was called with something

def test_add_multiple_measurements_invalid_request(client):
    """Test addition of multiple measurements with invalid request."""
    # Empty measurements list
    invalid_request = {
        "measurements": []
    }
    
    response = client.post(
        "/optimizations/test_optimizer/measurements/batch",
        json=invalid_request
    )
    assert response.status_code == 422  # Unprocessable Entity
    
    # Missing measurements field
    invalid_request = {}
    
    response = client.post(
        "/optimizations/test_optimizer/measurements/batch",
        json=invalid_request
    )
    assert response.status_code == 422  # Unprocessable Entity

def test_add_multiple_measurements_conversion_error(client, mock_backend):
    """Test handling of conversion errors."""
    # Configure mock to simulate DataFrame creation error
    with patch('pandas.DataFrame', side_effect=Exception("DataFrame creation error")):
        invalid_request = {
            "measurements": [
                {"x": "not_a_number", "y": 0.2, "Target": 0.5}
            ]
        }
        
        response = client.post(
            "/optimizations/test_optimizer/measurements/batch",
            json=invalid_request
        )
        assert response.status_code == 400  # Bad Request

def test_add_multiple_measurements_optimizer_not_found(client, sample_multiple_measurements_request, mock_backend):
    """Test addition of multiple measurements when optimizer doesn't exist."""
    # Configure the mock to return an error
    mock_backend.add_multiple_measurements.return_value = {
        "status": "error",
        "message": "Optimizer not_found_optimizer not found"
    }
    
    response = client.post(
        "/optimizations/not_found_optimizer/measurements/batch",
        json=sample_multiple_measurements_request
    )
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"]

def test_add_multiple_measurements_backend_error(client, sample_multiple_measurements_request, mock_backend):
    """Test handling of backend errors during multiple measurement addition."""
    # Configure the mock to return an error
    mock_backend.add_multiple_measurements.return_value = {
        "status": "error",
        "message": "Target column 'Target' not found in measurements dataframe"
    }
    
    response = client.post(
        "/optimizations/test_optimizer/measurements/batch",
        json=sample_multiple_measurements_request
    )
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Target column" in data["detail"]