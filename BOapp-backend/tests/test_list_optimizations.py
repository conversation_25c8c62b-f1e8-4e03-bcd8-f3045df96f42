"""Tests for the list optimizations endpoint."""
import pytest

def test_list_optimizations_success(client, mock_backend):
    """Test successful listing of optimizations."""
    response = client.get("/optimizations")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "optimizers" in data
    assert isinstance(data["optimizers"], list)
    assert len(data["optimizers"]) == 2
    assert data["optimizers"][0]["id"] == "test_optimizer_1"
    assert data["optimizers"][1]["id"] == "test_optimizer_2"
    assert data["count"] == 2
    
    # Verify the backend was called correctly
    mock_backend.list_optimizations.assert_called_once()

def test_list_optimizations_empty(client, mock_backend):
    """Test listing optimizations when none exist."""
    # Configure the mock to return empty list
    mock_backend.list_optimizations.return_value = {
        "status": "success",
        "optimizers": [],
        "count": 0
    }
    
    response = client.get("/optimizations")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["optimizers"] == []
    assert data["count"] == 0

def test_list_optimizations_with_metadata(client, mock_backend):
    """Test listing optimizations with additional metadata."""
    # Configure the mock to return additional metadata
    mock_backend.list_optimizations.return_value = {
        "status": "success",
        "optimizers": [
            {
                "id": "test_optimizer_1",
                "file_path": "/app/data/test_optimizer_1.json",
                "file_size": 10240,
                "last_modified": 1649876543,
                "objective_type": "SingleTarget"
            },
            {
                "id": "test_optimizer_2",
                "file_path": "/app/data/test_optimizer_2.json",
                "file_size": 15360,
                "last_modified": 1649876600,
                "objective_type": "Desirability"
            }
        ],
        "count": 2
    }
    
    response = client.get("/optimizations")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "file_size" in data["optimizers"][0]
    assert "last_modified" in data["optimizers"][0]
    assert "objective_type" in data["optimizers"][0]
    assert data["optimizers"][1]["objective_type"] == "Desirability"

def test_list_optimizations_backend_error(client, mock_backend):
    """Test handling of backend errors during optimization listing."""
    # Configure the mock to return an error
    mock_backend.list_optimizations.return_value = {
        "status": "error",
        "message": "Error listing optimizations: Unexpected error"
    }
    
    response = client.get("/optimizations")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Error listing optimizations" in data["detail"]