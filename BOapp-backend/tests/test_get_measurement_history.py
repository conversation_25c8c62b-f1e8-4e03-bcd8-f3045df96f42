"""Tests for the get measurement history endpoint."""
import pytest

def test_get_measurement_history_success(client, mock_backend):
    """Test successful retrieval of measurement history."""
    response = client.get("/optimizations/test_optimizer/measurements")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "measurements" in data
    assert isinstance(data["measurements"], list)
    assert len(data["measurements"]) == 2
    assert "target_info" in data
    assert data["target_info"][0]["name"] == "Target"
    assert data["target_info"][0]["mode"] == "MAX"
    assert data["count"] == 2
    
    # Verify the backend was called correctly
    mock_backend.get_measurement_history.assert_called_once_with(
        optimizer_id="test_optimizer"
    )

def test_get_measurement_history_no_measurements(client, mock_backend):
    """Test retrieval of measurement history when no measurements exist."""
    # Configure the mock to return no measurements
    mock_backend.get_measurement_history.return_value = {
        "status": "success",
        "message": "No measurements yet",
        "measurements": []
    }
    
    response = client.get("/optimizations/test_optimizer/measurements")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "message" in data
    assert data["message"] == "No measurements yet"
    assert data["measurements"] == []

def test_get_measurement_history_formatter_error(client, mock_backend):
    """Test handling of formatting errors in measurement history."""
    # Configure the mock to return a formatting error
    mock_backend.get_measurement_history.return_value = {
        "status": "success",
        "message": "Error formatting measurements: TypeError",
        "measurements": []
    }
    
    response = client.get("/optimizations/test_optimizer/measurements")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "message" in data
    assert "Error formatting measurements" in data["message"]
    assert data["measurements"] == []

def test_get_measurement_history_optimizer_not_found(client, mock_backend):
    """Test retrieval of measurement history when optimizer doesn't exist."""
    # Configure the mock to return an error
    mock_backend.get_measurement_history.return_value = {
        "status": "error",
        "message": "Optimizer not_found_optimizer not found"
    }
    
    response = client.get("/optimizations/not_found_optimizer/measurements")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"]

def test_get_measurement_history_backend_error(client, mock_backend):
    """Test handling of backend errors during measurement history retrieval."""
    # Configure the mock to return an error
    mock_backend.get_measurement_history.return_value = {
        "status": "error",
        "message": "Unexpected error in get_measurement_history"
    }
    
    response = client.get("/optimizations/test_optimizer/measurements")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Unexpected error" in data["detail"]