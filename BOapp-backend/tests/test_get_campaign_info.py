"""Tests for the get campaign info endpoint."""
import pytest

def test_get_campaign_info_success(client, mock_backend):
    """Test successful retrieval of campaign information."""
    response = client.get("/optimizations/test_optimizer")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "info" in data
    assert "parameters" in data["info"]
    assert len(data["info"]["parameters"]) == 2
    assert "targets" in data["info"]
    assert data["info"]["targets"][0]["name"] == "Target"
    assert data["info"]["measurements_count"] == 10
    assert data["info"]["objective_type"] == "SingleTarget"
    
    # Verify the backend was called correctly
    mock_backend.get_campaign_info.assert_called_once_with(
        optimizer_id="test_optimizer"
    )

def test_get_campaign_info_multi_objective(client, mock_backend):
    """Test retrieval of multi-objective campaign information."""
    # Configure the mock to return multi-objective info
    mock_backend.get_campaign_info.return_value = {
        "status": "success",
        "info": {
            "parameters": [
                {"name": "x", "type": "NumericalContinuousParameter", "bounds": [0, 1]},
                {"name": "y", "type": "NumericalContinuousParameter", "bounds": [0, 1]}
            ],
            "targets": [
                {"name": "efficiency", "mode": "MAX"},
                {"name": "cost", "mode": "MIN"}
            ],
            "measurements_count": 10,
            "objective_type": "Desirability",
            "scalarizer": "GEOM_MEAN"
        }
    }
    
    response = client.get("/optimizations/multi_objective_test")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["info"]["objective_type"] == "Desirability"
    assert "scalarizer" in data["info"]
    assert len(data["info"]["targets"]) == 2

def test_get_campaign_info_optimizer_not_found(client, mock_backend):
    """Test retrieval of campaign info when optimizer doesn't exist."""
    # Configure the mock to return an error
    mock_backend.get_campaign_info.return_value = {
        "status": "error",
        "message": "Optimizer not_found_optimizer not found"
    }
    
    response = client.get("/optimizations/not_found_optimizer")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"]

def test_get_campaign_info_backend_error(client, mock_backend):
    """Test handling of backend errors during campaign info retrieval."""
    # Configure the mock to return an error
    mock_backend.get_campaign_info.return_value = {
        "status": "error",
        "message": "Error getting campaign info: Unexpected error"
    }
    
    response = client.get("/optimizations/test_optimizer")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Error getting campaign info" in data["detail"]