"""Tests for the create optimization endpoint."""
import pytest
from unittest.mock import patch

def test_create_optimization_success(client, sample_optimization_request, mock_backend):
    """Test successful optimization creation."""
    response = client.post("/optimizations", json=sample_optimization_request)
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["optimizer_id"] == "test_optimizer"
    assert data["parameter_count"] == 2
    
    # Verify the backend was called with the correct parameters
    mock_backend.create_optimization.assert_called_once()
    args, kwargs = mock_backend.create_optimization.call_args
    assert kwargs["optimizer_id"] == "test_optimizer"
    assert len(kwargs["parameters"]) == 2
    assert kwargs["objective_type"] == "SingleTarget"

def test_create_multi_objective_optimization(client, sample_multi_objective_request, mock_backend):
    """Test creating a multi-objective optimization."""
    # Configure the mock to return success for multi-objective
    mock_backend.create_optimization.return_value = {
        "status": "success",
        "message": "Optimization created",
        "optimizer_id": "multi_objective_test",
        "parameter_count": 2,
        "constraint_count": 0
    }
    
    response = client.post("/optimizations", json=sample_multi_objective_request)
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["optimizer_id"] == "multi_objective_test"
    
    # Verify the backend was called with the correct parameters
    mock_backend.create_optimization.assert_called_once()
    args, kwargs = mock_backend.create_optimization.call_args
    assert kwargs["optimizer_id"] == "multi_objective_test"
    assert kwargs["objective_type"] == "Desirability"
    assert kwargs["scalarizer"] == "GEOM_MEAN"
    assert kwargs["weights"] == [1.0, 0.7]
    
    # Check that target_config was passed as a list
    assert isinstance(kwargs["target_config"], list)
    assert len(kwargs["target_config"]) == 2

def test_create_optimization_invalid_request(client):
    """Test optimization creation with invalid request."""
    # Missing required parameter
    invalid_request = {
        "parameters": [
            {"name": "x", "type": "NumericalContinuous", "bounds": [0, 1]}
        ],
        "target_config": {"name": "Target", "mode": "MAX"}
        # Missing optimizer_id
    }
    
    response = client.post("/optimizations", json=invalid_request)
    assert response.status_code == 422  # Unprocessable Entity
    data = response.json()
    assert "detail" in data

def test_create_optimization_backend_error(client, sample_optimization_request, mock_backend):
    """Test handling of backend errors during optimization creation."""
    # Configure the mock to return an error
    mock_backend.create_optimization.return_value = {
        "status": "error",
        "message": "Optimizer already exists"
    }
    
    response = client.post("/optimizations", json=sample_optimization_request)
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Optimizer already exists" in data["detail"]

def test_create_optimization_invalid_parameter_type(client):
    """Test creation with invalid parameter type."""
    invalid_request = {
        "optimizer_id": "test_optimizer",
        "parameters": [
            {"name": "x", "type": "InvalidType", "bounds": [0, 1]}
        ],
        "target_config": {"name": "Target", "mode": "MAX"},
        "objective_type": "SingleTarget"
    }
    
    response = client.post("/optimizations", json=invalid_request)
    assert response.status_code == 422  # Unprocessable Entity

def test_create_optimization_invalid_objective_type(client, sample_optimization_request):
    """Test creation with invalid objective type."""
    invalid_request = sample_optimization_request.copy()
    invalid_request["objective_type"] = "InvalidObjective"
    
    response = client.post("/optimizations", json=invalid_request)
    assert response.status_code == 422  # Unprocessable Entity

def test_create_optimization_invalid_multi_objective(client, sample_multi_objective_request):
    """Test multi-objective with invalid configuration."""
    # Single target with Desirability objective
    invalid_request = sample_multi_objective_request.copy()
    invalid_request["target_config"] = {"name": "Target", "mode": "MAX"}
    
    response = client.post("/optimizations", json=invalid_request)
    assert response.status_code == 422  # Unprocessable Entity
    
    # Multi-objective with invalid scalarizer
    invalid_request = sample_multi_objective_request.copy()
    invalid_request["scalarizer"] = "INVALID_SCALARIZER"
    
    response = client.post("/optimizations", json=invalid_request)
    assert response.status_code == 422  # Unprocessable Entity
    
    # Multi-objective with mismatched weights
    invalid_request = sample_multi_objective_request.copy()
    invalid_request["weights"] = [1.0]  # Only one weight for two targets
    
    response = client.post("/optimizations", json=invalid_request)
    assert response.status_code == 422  # Unprocessable Entity