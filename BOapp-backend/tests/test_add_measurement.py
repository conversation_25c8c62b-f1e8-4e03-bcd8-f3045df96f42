"""Tests for the add measurement endpoint."""
import pytest

def test_add_measurement_success(client, sample_measurement_request, mock_backend):
    """Test successful addition of a measurement."""
    response = client.post(
        "/optimizations/test_optimizer/measurements",
        json=sample_measurement_request
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["message"] == "Measurement added"
    
    # Verify the backend was called correctly
    mock_backend.add_measurement.assert_called_once_with(
        optimizer_id="test_optimizer",
        parameters=sample_measurement_request["parameters"],
        target_value=sample_measurement_request["target_value"]
    )

def test_add_measurement_invalid_request(client):
    """Test addition of measurement with invalid request."""
    # Missing target_value
    invalid_request = {
        "parameters": {"x": 0.5, "y": 0.5}
    }
    
    response = client.post(
        "/optimizations/test_optimizer/measurements",
        json=invalid_request
    )
    assert response.status_code == 422  # Unprocessable Entity
    
    # Empty parameters
    invalid_request = {
        "parameters": {},
        "target_value": 0.75
    }
    
    response = client.post(
        "/optimizations/test_optimizer/measurements",
        json=invalid_request
    )
    assert response.status_code == 422  # Unprocessable Entity
    
    # Invalid target value (NaN)
    invalid_request = {
        "parameters": {"x": 0.5, "y": 0.5},
        "target_value": float('nan')
    }
    
    response = client.post(
        "/optimizations/test_optimizer/measurements",
        json=invalid_request
    )
    assert response.status_code == 422  # Unprocessable Entity or 400 Bad Request

def test_add_measurement_optimizer_not_found(client, sample_measurement_request, mock_backend):
    """Test addition of measurement when optimizer doesn't exist."""
    # Configure the mock to return an error
    mock_backend.add_measurement.return_value = {
        "status": "error",
        "message": "Optimizer not_found_optimizer not found"
    }
    
    response = client.post(
        "/optimizations/not_found_optimizer/measurements",
        json=sample_measurement_request
    )
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"]

def test_add_measurement_backend_error(client, sample_measurement_request, mock_backend):
    """Test handling of backend errors during measurement addition."""
    # Configure the mock to return an error
    mock_backend.add_measurement.return_value = {
        "status": "error",
        "message": "Error adding measurement: target column not found"
    }
    
    response = client.post(
        "/optimizations/test_optimizer/measurements",
        json=sample_measurement_request
    )
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "target column not found" in data["detail"]