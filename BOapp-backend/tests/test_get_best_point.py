"""Tests for the get best point endpoint."""
import pytest

def test_get_best_point_success(client, mock_backend):
    """Test successful retrieval of best point."""
    response = client.get("/optimizations/test_optimizer/best")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["best_value"] == 0.95
    assert data["best_parameters"] == {"x": 0.7, "y": 0.3}
    assert data["total_measurements"] == 10
    assert data["target_name"] == "Target"
    assert data["target_mode"] == "MAX"
    
    # Verify the backend was called correctly
    mock_backend.get_best_point.assert_called_once_with(
        optimizer_id="test_optimizer"
    )

def test_get_best_point_no_measurements(client, mock_backend):
    """Test retrieval of best point when no measurements exist."""
    # Configure the mock to return no measurements
    mock_backend.get_best_point.return_value = {
        "status": "success",
        "message": "No measurements yet"
    }
    
    response = client.get("/optimizations/test_optimizer/best")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "message" in data
    assert data["message"] == "No measurements yet"
    assert "best_value" not in data

def test_get_best_point_optimizer_not_found(client, mock_backend):
    """Test retrieval of best point when optimizer doesn't exist."""
    # Configure the mock to return an error
    mock_backend.get_best_point.return_value = {
        "status": "error",
        "message": "Optimizer not_found_optimizer not found"
    }
    
    response = client.get("/optimizations/not_found_optimizer/best")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"]

def test_get_best_point_invalid_objective_structure(client, mock_backend):
    """Test handling when the objective structure is invalid."""
    # Configure the mock to return no objective
    mock_backend.get_best_point.return_value = {
        "status": "success",
        "message": "No objective defined"
    }
    
    response = client.get("/optimizations/test_optimizer/best")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "message" in data
    assert data["message"] == "No objective defined"

def test_get_best_point_backend_error(client, mock_backend):
    """Test handling of backend errors during best point retrieval."""
    # Configure the mock to return an error
    mock_backend.get_best_point.return_value = {
        "status": "error",
        "message": "Error getting best point: Unexpected error"
    }
    
    response = client.get("/optimizations/test_optimizer/best")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Error getting best point" in data["detail"]