"""Tests for the API endpoints using direct HTTP calls to the running API."""
import requests
import pytest
import time

# API configuration
API_URL = "http://localhost:8000"
API_KEY = "123456789"
HEADERS = {"X-API-Key": API_KEY}

def test_health_endpoint():
    """Test that the health endpoint returns a healthy status."""
    response = requests.get(f"{API_URL}/health", headers=HEADERS)
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_root_endpoint():
    """Test that the root endpoint returns a success status."""
    response = requests.get(f"{API_URL}/", headers=HEADERS)
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "message" in data
    assert "Bayesian Optimization API is running" in data["message"]

def test_optimization_test_endpoint():
    """Test the optimization test endpoint."""
    response = requests.get(f"{API_URL}/optimization/test", headers=HEADERS)
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "message" in data
    assert "Optimization test successful" in data["message"]

def test_create_and_use_optimization():
    """Test creating an optimization and using it."""
    # Create a unique optimizer ID based on timestamp
    optimizer_id = f"test_optimizer_{int(time.time())}"
    
    # 1. Create optimization
    create_payload = {
        "optimizer_id": optimizer_id,
        "parameters": [
            {"name": "x", "type": "NumericalContinuous", "bounds": [0, 1]},
            {"name": "y", "type": "NumericalContinuous", "bounds": [0, 1]}
        ],
        "target_config": {"name": "Target", "mode": "MAX"},
        "objective_type": "SingleTarget",
        "num_initial_samples": 0,  # No initial samples for faster test
        "recommender_config": {
            "type": "BotorchRecommender"
        },
        "acquisition_config": {
            "type": "qExpectedImprovement"
        },
        "surrogate_config": {
            "type": "GaussianProcess"
        }
    }
    
    response = requests.post(
        f"{API_URL}/optimizations", 
        headers=HEADERS,
        json=create_payload
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "optimizer_id" in data
    assert data["optimizer_id"] == optimizer_id
    
    # 2. Get suggestions
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/suggest?batch_size=1", 
        headers=HEADERS
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "suggestions" in data
    assert len(data["suggestions"]) == 1
    suggestion = data["suggestions"][0]
    assert "x" in suggestion
    assert "y" in suggestion
    
    # 3. Add measurement
    measurement_payload = {
        "parameters": suggestion,
        "target_value": 0.75  # Some arbitrary value
    }
    
    response = requests.post(
        f"{API_URL}/optimizations/{optimizer_id}/measurements", 
        headers=HEADERS,
        json=measurement_payload
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    
    # 4. Get another suggestion (now with data)
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/suggest?batch_size=1", 
        headers=HEADERS
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "suggestions" in data
    assert len(data["suggestions"]) == 1
    
    # 5. Get campaign info
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/info", 
        headers=HEADERS
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "info" in data
    assert "parameters" in data["info"]
    assert "targets" in data["info"]
    assert data["info"]["measurements_count"] == 1
    
    # 6. Get history
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/history", 
        headers=HEADERS
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "measurements" in data
    assert len(data["measurements"]) == 1
    
    # 7. Get best point
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/best", 
        headers=HEADERS
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "best_parameters" in data
    assert "best_value" in data
    
    # 8. Delete optimization
    response = requests.delete(
        f"{API_URL}/optimizations/{optimizer_id}", 
        headers=HEADERS
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    
    # 9. Verify it's gone
    response = requests.get(
        f"{API_URL}/optimizations/{optimizer_id}/info", 
        headers=HEADERS
    )
    assert response.status_code == 404

def test_list_optimizations():
    """Test listing all optimizations."""
    response = requests.get(f"{API_URL}/optimizations", headers=HEADERS)
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "optimizations" in data
    # We can't assert the exact count as it may vary
