"""Tests for the unload campaign endpoint."""
import pytest

def test_unload_campaign_success(client, mock_backend):
    """Test successful unloading of a campaign."""
    response = client.post("/optimizations/test_optimizer/unload")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["message"] == "Optimizer test_optimizer unloaded successfully"
    
    # Verify the backend was called correctly
    mock_backend.unload_campaign.assert_called_once_with(
        optimizer_id="test_optimizer"
    )

def test_unload_campaign_not_loaded(client, mock_backend):
    """Test unloading when the campaign is not loaded."""
    # Configure the mock to return not loaded
    mock_backend.unload_campaign.return_value = {
        "status": "success",
        "message": "Optimizer test_optimizer is not loaded"
    }
    
    response = client.post("/optimizations/test_optimizer/unload")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "is not loaded" in data["message"]

def test_unload_campaign_not_found(client, mock_backend):
    """Test unloading when the campaign doesn't exist."""
    # Configure the mock to return an error
    mock_backend.unload_campaign.return_value = {
        "status": "error",
        "message": "Invalid optimizer_id, must be a non-empty string"
    }
    
    response = client.post("/optimizations//unload")  # Empty optimizer_id
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Invalid optimizer_id" in data["detail"]

def test_unload_campaign_backend_error(client, mock_backend):
    """Test handling of backend errors during campaign unloading."""
    # Configure the mock to return an error
    mock_backend.unload_campaign.return_value = {
        "status": "error",
        "message": "Error unloading campaign: Unexpected error"
    }
    
    response = client.post("/optimizations/test_optimizer/unload")
    assert response.status_code == 400  # Bad Request
    data = response.json()
    assert "detail" in data
    assert "Error unloading campaign" in data["detail"]