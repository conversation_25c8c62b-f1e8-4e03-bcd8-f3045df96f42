import torch
import logging
import time
import numpy as np
import pandas as pd
from baybe import Campaign
from baybe.searchspace import SearchSpace
from baybe.parameters import NumericalDiscreteParameter
from baybe.targets import NumericalTarget
from baybe.objectives import SingleTargetObjective
from baybe.recommenders import <PERSON>tor<PERSON>Recommender
from baybe.surrogates import GaussianProcessSurrogate

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

def test_baybe_gpu():
    logger.info("Testing BayBE with GPU acceleration...")
    
    # Check if CUDA is available
    if torch.cuda.is_available():
        logger.info("CUDA is available!")
        device = torch.device("cuda")
        
        # Set default tensor type to cuda
        torch.set_default_tensor_type('torch.cuda.FloatTensor')
        
        # Additional GPU optimizations
        torch.backends.cudnn.benchmark = True
        
        # Log GPU information
        logger.info(f"Using GPU: {torch.cuda.get_device_name(0)}")
        logger.info(f"Memory allocated: {torch.cuda.memory_allocated(0) / 1e6:.2f} MB")
    else:
        logger.warning("CUDA is not available. Using CPU.")
        device = torch.device("cpu")
    
    # Create a simple test campaign
    logger.info("Creating test campaign...")
    
    try:
        # Define parameters
        param1 = NumericalDiscreteParameter(
            name="P1",
            values=list(range(1, 26))  # 1-25
        )
        param2 = NumericalDiscreteParameter(
            name="P2",
            values=list(range(1, 101))  # 1-100
        )
        
        # Try different ways to create search space based on API version
        try:
            # Newer API version
            from baybe.searchspace.discrete import DiscreteSearchSpace
            from baybe.searchspace.continuous import ContinuousSearchSpace
            
            logger.info("Using newer API version with DiscreteSearchSpace")
            discrete_space = DiscreteSearchSpace([param1, param2])
            continuous_space = ContinuousSearchSpace([])
            search_space = SearchSpace(discrete=discrete_space, continuous=continuous_space)
        except (ImportError, TypeError) as e:
            logger.warning(f"Failed to create search space with newer API: {e}")
            
            try:
                # Try direct initialization
                logger.info("Trying direct initialization of SearchSpace")
                search_space = SearchSpace(parameters=[param1, param2])
            except TypeError as e2:
                logger.warning(f"Failed direct initialization: {e2}")
                
                # Fallback to older API
                logger.info("Falling back to older API version")
                search_space = SearchSpace([param1, param2])
        
        logger.info("Search space created successfully")
        
        # Define target
        target = NumericalTarget(name="Target", mode="MAX")
        
        # Create objective
        objective = SingleTargetObjective(target=target)
        
        # Configure recommender with GPU optimizations
        recommender_kwargs = {}
        if device.type == "cuda":
            recommender_kwargs = {
                "n_restarts": 20,
                "n_raw_samples": 128
            }
        
        # Create surrogate model
        surrogate_model = GaussianProcessSurrogate()
        
        # Create recommender
        recommender = BotorchRecommender(
            surrogate_model=surrogate_model,
            acquisition_function="qExpectedImprovement",
            **recommender_kwargs
        )
        
        # Create campaign
        campaign = Campaign(
            searchspace=search_space,
            objective=objective,
            recommender=recommender
        )
        
        # Add some initial measurements
        initial_data = pd.DataFrame({
            "P1": [1, 5, 10, 15, 20, 25],
            "P2": [10, 30, 50, 70, 90, 100],
            "Target": [10, 30, 50, 70, 90, 100]
        })
        campaign.add_measurements(initial_data)
        
        # Test recommendation with different batch sizes
        batch_sizes = [1, 2, 5, 10, 20]
        
        for batch_size in batch_sizes:
            logger.info(f"Testing recommendation with batch_size={batch_size}...")
            
            # Measure time
            start_time = time.time()
            
            # Use GPU-accelerated optimization if available
            with torch.cuda.amp.autocast(enabled=device.type=="cuda"):
                suggestions = campaign.recommend(batch_size=batch_size)
            
            end_time = time.time()
            duration = end_time - start_time
            
            logger.info(f"Batch size {batch_size}: Generated {len(suggestions)} suggestions in {duration:.2f} seconds")
            logger.info(f"First suggestion: {suggestions.iloc[0].to_dict()}")
            
            # Log GPU memory usage
            if device.type == "cuda":
                logger.info(f"GPU memory allocated: {torch.cuda.memory_allocated(0) / 1e6:.2f} MB")
        
        logger.info("BayBE GPU test completed!")
    
    except Exception as e:
        logger.error(f"Error during BayBE GPU test: {str(e)}", exc_info=True)

if __name__ == "__main__":
    test_baybe_gpu()
