import uvicorn
import argparse
import logging
from baybe_api.main import app

if __name__ == "__main__":
    # Set up command line arguments
    parser = argparse.ArgumentParser(description="Run the BayBE API server")
    parser.add_argument("--port", type=int, default=8000, help="Port to run the server on")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    args = parser.parse_args()

    # Configure logging
    log_level = "debug" if args.debug else "info"
    logging.basicConfig(level=logging.DEBUG if args.debug else logging.INFO)

    # Run the server
    print(f"Starting server on port {args.port} with log level {log_level}")
    uvicorn.run(app, host="0.0.0.0", port=args.port, log_level=log_level)